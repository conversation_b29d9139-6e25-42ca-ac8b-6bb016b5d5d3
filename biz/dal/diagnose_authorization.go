package dal

import (
	"code.byted.org/volcengine-support/cloud-sherlock/gen/gorm/model"
	"code.byted.org/volcengine-support/cloud-sherlock/gen/hertz/diagnose_authorization"
	"code.byted.org/volcengine-support/cloud-sherlock/pkg/rds"
	"context"
	"gorm.io/gen"
)

func FindLastDiagnoseAuthorizationByTicketID(ctx context.Context, ticketId string) (*model.DiagnoseAuthorization, error) {
	entity := rds.QueryCloudSherlock.DiagnoseAuthorization
	return entity.WithContext(ctx).Where(entity.TicketID.Eq(ticketId)).Order(entity.UpdateTime.Desc()).First()
}

func SaveDiagnoseAuthorization(ctx context.Context, diagnoseAuthorization *model.DiagnoseAuthorization) error {
	entity := rds.QueryCloudSherlock.DiagnoseAuthorization
	return entity.WithContext(ctx).Save(diagnoseAuthorization)
}

func UpdatesDiagnoseAuthsWithTransaction(ctx context.Context, Auths []*model.DiagnoseAuthorization) error {
	err := rds.Transaction(ctx, func(ctx context.Context) error {
		err := BatchUpdateDifferentDiagnoseAuth(ctx, Auths)
		if err != nil {
			return err
		}
		return nil
	})
	if err != nil {
		return err
	}
	return nil
}

func BatchUpdateDifferentDiagnoseAuth(ctx context.Context, Auths []*model.DiagnoseAuthorization) error {
	return rds.Transaction(ctx, func(ctx context.Context) error {
		entity := rds.QueryCloudSherlock.DiagnoseAuthorization
		for _, auth := range Auths {
			_, err := entity.WithContext(ctx).
				Where(entity.AuthorizationID.Eq(*auth.AuthorizationID)).
				Updates(auth)
			if err != nil {
				return err
			}
		}
		return nil
	})
}

func FindDiagnoseAuthLastUpdatedTime(ctx context.Context) (int32, error) {
	entity := rds.QueryCloudSherlock.DiagnoseAuthorization
	auth, err := entity.WithContext(ctx).Select(entity.UpdateTime).Order(entity.UpdateTime.Desc()).First()
	if err != nil {
		return -1, err
	}
	return *auth.UpdateTime, nil
}

func AddDiagnoseAuth(ctx context.Context, auth *model.DiagnoseAuthorization) error {
	entity := rds.QueryCloudSherlock.DiagnoseAuthorization
	return entity.WithContext(ctx).Create(auth)
}

func ListDiagnoseAuth(ctx context.Context, req *diagnose_authorization.ListDiagnoseAuthorizationRequest) ([]*model.DiagnoseAuthorization, int64, error) {
	entity := rds.QueryCloudSherlock.DiagnoseAuthorization
	conditions := make([]gen.Condition, 0)
	if req.ApplyUser != nil && *req.ApplyUser != "" {
		conditions = append(conditions, entity.ApplyUser.Like("%"+*req.ApplyUser+"%"))
	}
	if req.Status != nil && len(req.Status) != 0 {
		conditions = append(conditions, entity.Status.In(req.Status...))
	}
	if req.AccountID != nil && *req.AccountID != "" {
		conditions = append(conditions, entity.AccountID.Like("%"+*req.AccountID+"%"))
	}
	if req.DiagnoseTemplateNames != nil && len(req.DiagnoseTemplateNames) != 0 {
		conditions = append(conditions, entity.DiagnoseTemplateName.In(req.DiagnoseTemplateNames...))
	}
	if req.TicketID != nil && *req.TicketID != "" {
		conditions = append(conditions, entity.TicketID.Like("%"+*req.TicketID+"%"))
	}
	return entity.WithContext(ctx).Where(conditions...).
		FindByPage(int((req.PageNumber-1)*req.PageSize), int(req.PageSize))
}
func TerminateDiagnoseAuth(ctx context.Context, auth *model.DiagnoseAuthorization) error {
	entity := rds.QueryCloudSherlock.DiagnoseAuthorization
	_, err := entity.WithContext(ctx).Where(entity.AuthorizationID.Eq(*auth.AuthorizationID)).Updates(auth)
	return err
}
