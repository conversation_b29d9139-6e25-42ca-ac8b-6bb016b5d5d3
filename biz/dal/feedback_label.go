package dal

import (
	"code.byted.org/volcengine-support/cloud-sherlock/gen/gorm/model"
	"code.byted.org/volcengine-support/cloud-sherlock/pkg/rds"
	"context"
	"gorm.io/gen"
)

const (
	FeedbackLabelResultStage = "result"
)

func QueryFeedbackLabelsByIDs(ctx context.Context, ids []int64) ([]*model.FeedbackLabel, error) {
	if len(ids) == 0 {
		return nil, nil
	}
	entity := rds.QueryCloudSherlock.FeedbackLabel
	return entity.WithContext(ctx).Where(entity.ID.In(ids...)).Find()
}

func QueryFeedbackLabels(ctx context.Context, filter *model.FeedbackLabel) ([]*model.FeedbackLabel, error) {
	entity := rds.QueryCloudSherlock.FeedbackLabel
	conditions := make([]gen.Condition, 0)
	if filter.ID != 0 {
		conditions = append(conditions, entity.ID.Eq(filter.ID))
	}
	if filter.Stage != "" {
		conditions = append(conditions, entity.Stage.Eq(filter.Stage))
	} else {
		// 不查询result阶段的标签
		conditions = append(conditions, entity.Stage.Neq(FeedbackLabelResultStage))
	}
	if filter.LabelKey != "" {
		conditions = append(conditions, entity.LabelKey.Eq(filter.LabelKey))
	}
	if filter.LabelName != "" {
		conditions = append(conditions, entity.LabelName.Eq(filter.LabelName))
	}
	return entity.WithContext(ctx).Where(conditions...).Find()
}

func QueryFeedbackLabelByStageName(ctx context.Context, stage, name string) (*model.FeedbackLabel, error) {
	entity := rds.QueryCloudSherlock.FeedbackLabel
	return entity.WithContext(ctx).Where(entity.Stage.Eq(stage), entity.LabelName.Eq(name)).First()
}

func CreateFeedbackLabel(ctx context.Context, feedbackLabel *model.FeedbackLabel) error {
	entity := rds.QueryCloudSherlock.FeedbackLabel
	return entity.WithContext(ctx).Create(feedbackLabel)
}

func DeleteFeedbackLabelByID(ctx context.Context, id int64) error {
	entity := rds.QueryCloudSherlock.FeedbackLabel
	_, err := entity.WithContext(ctx).Where(entity.ID.Eq(id)).Delete()
	return err
}

func UpdateFeedbackLabel(ctx context.Context, id int64, feedbackLabel *model.FeedbackLabel) error {
	entity := rds.QueryCloudSherlock.FeedbackLabel
	_, err := entity.WithContext(ctx).Where(entity.ID.Eq(id)).Updates(feedbackLabel)
	return err
}
