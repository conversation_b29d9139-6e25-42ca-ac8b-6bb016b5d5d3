package dal

import (
	"code.byted.org/volcengine-support/cloud-sherlock/gen/gorm/model"
	"code.byted.org/volcengine-support/cloud-sherlock/pkg/rds"
	"context"
	"gorm.io/gen"
)

func CreateFeedbackV2(ctx context.Context, feedback *model.FeedbackV2) error {
	entity := rds.QueryCloudSherlock.FeedbackV2
	return entity.WithContext(ctx).Create(feedback)
}

func FindFeedback(ctx context.Context, feedback *model.FeedbackV2, isLike, valid *bool, sortByFeedbackTime, sortByHandleStartTime, sortByHandleEndTime *string, pageNumber, pageSize int) ([]*model.FeedbackV2, int64, error) {
	entity := rds.QueryCloudSherlock.FeedbackV2
	conditions := make([]gen.Condition, 0)
	if feedback.ID != 0 {
		conditions = append(conditions, entity.ID.Eq(feedback.ID))
	}
	if feedback.Status != "" {
		conditions = append(conditions, entity.Status.Eq(feedback.Status))
	}
	if feedback.Stage != "" {
		conditions = append(conditions, entity.Stage.Eq(feedback.Stage))
	}
	if feedback.SessionID != "" {
		conditions = append(conditions, entity.SessionID.Eq(feedback.SessionID))
	}
	if feedback.MessageID != "" {
		conditions = append(conditions, entity.MessageID.Eq(feedback.MessageID))
	}
	if feedback.TicketID != nil {
		conditions = append(conditions, entity.TicketID.Eq(*feedback.TicketID))
	}
	if feedback.FeedbackUserID != nil {
		conditions = append(conditions, entity.FeedbackUserID.Eq(*feedback.FeedbackUserID))
	}
	if feedback.HandleUserIds != nil {
		conditions = append(conditions, entity.HandleUserIds.Like("%"+*feedback.HandleUserIds+"%"))
	}
	if feedback.Title != nil {
		conditions = append(conditions, entity.Title.Like("%"+*feedback.Title+"%"))
	}
	if isLike != nil {
		conditions = append(conditions, entity.IsLike.Is(*isLike))
	}
	if valid != nil {
		conditions = append(conditions, entity.Valid.Is(*valid))
	}
	if sortByFeedbackTime != nil && *sortByFeedbackTime == "asc" {
		return entity.WithContext(ctx).Where(conditions...).Order(entity.FeedbackUserID.Asc()).FindByPage((pageNumber-1)*pageSize, pageSize)
	}
	if sortByHandleStartTime != nil && *sortByHandleStartTime == "asc" {
		return entity.WithContext(ctx).Where(conditions...).Order(entity.StartHandleTime.Asc()).FindByPage((pageNumber-1)*pageSize, pageSize)
	}
	if sortByHandleEndTime != nil && *sortByHandleEndTime == "asc" {
		return entity.WithContext(ctx).Where(conditions...).Order(entity.EndHandleTime.Asc()).FindByPage((pageNumber-1)*pageSize, pageSize)
	}
	if sortByFeedbackTime != nil && *sortByFeedbackTime == "desc" {
		return entity.WithContext(ctx).Where(conditions...).Order(entity.FeedbackTime.Desc()).FindByPage((pageNumber-1)*pageSize, pageSize)
	}
	if sortByHandleStartTime != nil && *sortByHandleStartTime == "desc" {
		return entity.WithContext(ctx).Where(conditions...).Order(entity.StartHandleTime.Desc()).FindByPage((pageNumber-1)*pageSize, pageSize)
	}
	if sortByHandleEndTime != nil && *sortByHandleEndTime == "desc" {
		return entity.WithContext(ctx).Where(conditions...).Order(entity.EndHandleTime.Desc()).FindByPage((pageNumber-1)*pageSize, pageSize)
	}
	return entity.WithContext(ctx).Where(conditions...).FindByPage((pageNumber-1)*pageSize, pageSize)
}

func FeedbackV2FindBySessionIDMessageID(ctx context.Context, sessionID, messageID string) (*model.FeedbackV2, error) {
	entity := rds.QueryCloudSherlock.FeedbackV2
	return entity.WithContext(ctx).Where(entity.SessionID.Eq(sessionID), entity.MessageID.Eq(messageID)).First()
}

func ValidFeedbackV2FindBySessionIDMessageID(ctx context.Context, sessionID, messageID string) (*model.FeedbackV2, error) {
	entity := rds.QueryCloudSherlock.FeedbackV2
	return entity.WithContext(ctx).Where(entity.SessionID.Eq(sessionID), entity.MessageID.Eq(messageID), entity.Valid.Is(true)).First()
}

func FeedbackV2FindByID(ctx context.Context, id int64) (*model.FeedbackV2, error) {
	entity := rds.QueryCloudSherlock.FeedbackV2
	return entity.WithContext(ctx).Where(entity.ID.Eq(id)).First()
}

func FeedbackV2Update(ctx context.Context, feedback *model.FeedbackV2) error {
	entity := rds.QueryCloudSherlock.FeedbackV2
	_, err := entity.WithContext(ctx).Where(entity.ID.Eq(feedback.ID)).Updates(feedback)
	return err
}

func SetUnValidFeedbackV2(ctx context.Context, id int64) error {
	entity := rds.QueryCloudSherlock.FeedbackV2
	_, err := entity.WithContext(ctx).Where(entity.ID.Eq(id)).UpdateColumn(entity.Valid, false)
	return err
}
