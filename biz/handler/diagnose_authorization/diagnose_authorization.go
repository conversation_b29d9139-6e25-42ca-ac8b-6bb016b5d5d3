package diagnose_authorization

import (
	"code.byted.org/middleware/hertz/pkg/app"
	"code.byted.org/middleware/hertz_ext/v2/binding"
	"code.byted.org/volcengine-support/cloud-sherlock/biz/errorcode"
	"code.byted.org/volcengine-support/cloud-sherlock/biz/handler"
	"code.byted.org/volcengine-support/cloud-sherlock/biz/middleware"
	DiagnoseSvc "code.byted.org/volcengine-support/cloud-sherlock/biz/service/diagnose_authorization"
	"code.byted.org/volcengine-support/cloud-sherlock/gen/hertz/diagnose_authorization"
	ehttp "code.byted.org/volcengine-support/gopkg/hertzext"
	"context"
)

type ActionHandler struct {
	ehttp.CommonActionHandler
}

func (h *ActionHandler) ApplyDiagnoseAuthorization20241201(ctx context.Context, c *app.RequestContext) {
	req := &diagnose_authorization.ApplyDiagnoseAuthorizationRequest{}
	if err := binding.BindAndValidate(c, req); err != nil {
		handler.ErrorResponse(ctx, c, err)
		return
	}
	email, isExist := c.Get(middleware.Email)
	if !isExist {
		handler.ErrorResponse(ctx, c, errorcode.ErrRequestParamInvalid.WithArgs("token"))
		return
	}

	result, err := DiagnoseSvc.GetDiagnoseAuthService().ApplyDiagnoseAuthorization(ctx, email.(string), req)
	if err != nil {
		handler.ErrorResponse(ctx, c, err)
		return
	}
	ehttp.DoResponse(ctx, c, result)

}

func (h *ActionHandler) ListDiagnoseAuthorization20241201(ctx context.Context, c *app.RequestContext) {
	req := &diagnose_authorization.ListDiagnoseAuthorizationRequest{}
	if err := binding.BindAndValidate(c, req); err != nil {
		handler.ErrorResponse(ctx, c, err)
		return
	}
	email, isExist := c.Get(middleware.Email)
	if !isExist {
		handler.ErrorResponse(ctx, c, errorcode.ErrRequestParamInvalid.WithArgs("token"))
		return
	}

	result, err := DiagnoseSvc.GetDiagnoseAuthService().ListDiagnoseAuthorization(ctx, email.(string), req)
	if err != nil {
		handler.ErrorResponse(ctx, c, err)
		return
	}
	ehttp.DoResponse(ctx, c, result)
}

func (h *ActionHandler) TerminateDiagnoseAuthorization20241201(ctx context.Context, c *app.RequestContext) {
	req := &diagnose_authorization.TerminateDiagnoseAuthorizationRequest{}
	if err := binding.BindAndValidate(c, req); err != nil {
		handler.ErrorResponse(ctx, c, err)
		return
	}
	email, isExist := c.Get(middleware.Email)
	if !isExist {
		handler.ErrorResponse(ctx, c, errorcode.ErrRequestParamInvalid.WithArgs("token"))
		return
	}

	result, err := DiagnoseSvc.GetDiagnoseAuthService().TerminateDiagnoseAuthorization(ctx, email.(string), req)
	if err != nil {
		handler.ErrorResponse(ctx, c, err)
		return
	}
	ehttp.DoResponse(ctx, c, result)
}
