package diagnose_item

import (
	"code.byted.org/middleware/hertz/pkg/app"
	"code.byted.org/middleware/hertz_ext/v2/binding"
	"code.byted.org/volcengine-support/cloud-sherlock/biz/errorcode"
	"code.byted.org/volcengine-support/cloud-sherlock/biz/handler"
	"code.byted.org/volcengine-support/cloud-sherlock/biz/middleware"
	DiagnoseSvc "code.byted.org/volcengine-support/cloud-sherlock/biz/service/diagnose_item"
	diagnoseItemHertz "code.byted.org/volcengine-support/cloud-sherlock/gen/hertz/diagnose_item"
	ehttp "code.byted.org/volcengine-support/gopkg/hertzext"
	"context"
	"fmt"
)

type ActionHandler struct {
	ehttp.CommonActionHandler
}

var (
	defaultPageSize   int64 = 10
	defaultPageNumber int64 = 1
)

func (h *ActionHandler) RegisterDiagnoseItem20241201(ctx context.Context, c *app.RequestContext) {
	req := &diagnoseItemHertz.RegisterDiagnoseItemRequest{}
	if err := binding.BindAndValidate(c, req); err != nil {
		handler.ErrorResponse(ctx, c, err)
		return
	}

	if req.UserInfo == nil || req.UserInfo.UserID == "" || req.UserInfo.UserName == "" {
		handler.ErrorResponse(ctx, c, errorcode.ErrRequestParamInvalid.WithArgs("UserInfo"))
		return
	}

	if req.ResourceType == "" {
		handler.ErrorResponse(ctx, c, errorcode.ErrRequestParamInvalid.WithArgs("ResourceType "))
		return
	}
	if req.DiagnoseItemDescription == nil || *req.DiagnoseItemDescription == "" {
		handler.ErrorResponse(ctx, c, errorcode.ErrRequestParamInvalid.WithArgs("DiagnoseItemDescription "))
		return
	}

	result, err := DiagnoseSvc.GetDiagnoseItemService().RegisterDiagnoseItem(ctx, req)
	if err != nil {
		handler.ErrorResponse(ctx, c, err)
		return
	}
	ehttp.DoResponse(ctx, c, result)
}

func (h *ActionHandler) ListDiagnoseItem20241201(ctx context.Context, c *app.RequestContext) {
	req := &diagnoseItemHertz.ListDiagnoseItemRequest{}
	if err := binding.BindAndValidate(c, req); err != nil {
		handler.ErrorResponse(ctx, c, err)
		return
	}
	data, _ := c.Get(middleware.Email)
	email := data.(string)
	fmt.Println(email)
	if req.UserInfo == nil || req.UserInfo.UserID == "" || req.UserInfo.UserName == "" {
		handler.ErrorResponse(ctx, c, errorcode.ErrRequestParamInvalid.WithArgs("CreatUser"))
		return
	}

	result, err := DiagnoseSvc.GetDiagnoseItemService().ListDiagnoseItem(ctx, req)
	if err != nil {
		handler.ErrorResponse(ctx, c, err)
		return
	}
	ehttp.DoResponse(ctx, c, result)
}

func (h *ActionHandler) UpdateDiagnoseItem20241201(ctx context.Context, c *app.RequestContext) {
	req := &diagnoseItemHertz.UpdateDiagnoseItemRequest{}
	if err := binding.BindAndValidate(c, req); err != nil {
		handler.ErrorResponse(ctx, c, err)
		return
	}

	if req.UserInfo == nil || req.UserInfo.UserID == "" || req.UserInfo.UserName == "" {
		handler.ErrorResponse(ctx, c, errorcode.ErrRequestParamInvalid.WithArgs("UserInfo"))
		return
	}
	if req.DiagnoseItemID == nil {
		handler.ErrorResponse(ctx, c, errorcode.ErrRequestParamInvalid.WithArgs("DiagnoseItemId"))
		return
	}

	result, err := DiagnoseSvc.GetDiagnoseItemService().UpdateDiagnoseItem(ctx, req)
	if err != nil {
		handler.ErrorResponse(ctx, c, err)
		return
	}
	ehttp.DoResponse(ctx, c, result)
}

func (h *ActionHandler) DeleteDiagnoseItem20241201(ctx context.Context, c *app.RequestContext) {
	req := &diagnoseItemHertz.DeleteDiagnoseItemRequest{}
	if err := binding.BindAndValidate(c, req); err != nil {
		handler.ErrorResponse(ctx, c, err)
		return
	}
	if req.UserInfo == nil || req.UserInfo.UserID == "" || req.UserInfo.UserName == "" {
		handler.ErrorResponse(ctx, c, errorcode.ErrRequestParamInvalid.WithArgs("UserInfo"))
		return
	}
	if req.DiagnoseItemID == nil {
		handler.ErrorResponse(ctx, c, errorcode.ErrRequestParamInvalid.WithArgs("DiagnoseItemId"))
		return
	}

	result, err := DiagnoseSvc.GetDiagnoseItemService().DeleteDiagnoseItem(ctx, req)
	if err != nil {
		handler.ErrorResponse(ctx, c, err)
		return
	}
	ehttp.DoResponse(ctx, c, result)
}

func (h *ActionHandler) GetAllDiagnoseItemWithProduct20241201(ctx context.Context, c *app.RequestContext) {
	req := &diagnoseItemHertz.GetAllDiagnoseItemWithProductRequest{}
	if err := binding.BindAndValidate(c, req); err != nil {
		handler.ErrorResponse(ctx, c, err)
		return
	}
	if req.UserInfo == nil || req.UserInfo.UserID == "" || req.UserInfo.UserName == "" {
		handler.ErrorResponse(ctx, c, errorcode.ErrRequestParamInvalid.WithArgs("CreatUser"))
		return
	}

	result, err := DiagnoseSvc.GetDiagnoseItemService().GetAllDiagnoseItemWithProduct(ctx, req)
	if err != nil {
		handler.ErrorResponse(ctx, c, err)
		return
	}
	ehttp.DoResponse(ctx, c, result)
}
