package discourse

import (
	"code.byted.org/middleware/hertz/pkg/app"
	"code.byted.org/middleware/hertz_ext/v2/binding"
	"code.byted.org/volcengine-support/cloud-sherlock/biz/handler"
	discourseSvc "code.byted.org/volcengine-support/cloud-sherlock/biz/service/discourse"
	discourseHertz "code.byted.org/volcengine-support/cloud-sherlock/gen/hertz/discourse"
	ehttp "code.byted.org/volcengine-support/gopkg/hertzext"
	"context"
)

var (
	defaultPageSize   int64 = 10
	defaultPageNumber int64 = 1
)

type ActionHandler struct {
	ehttp.CommonActionHandler
}

func (h *ActionHandler) Create20250501(ctx context.Context, c *app.RequestContext) {
	req := &discourseHertz.CreateReq{}
	if err := binding.BindAndValidate(c, req); err != nil {
		handler.ErrorResponse(ctx, c, err)
		return
	}
	result, err := discourseSvc.GetDiscourseService().Create(ctx, req)
	if err != nil {
		handler.ErrorResponse(ctx, c, err)
		return
	}
	ehttp.DoResponse(ctx, c, result)
}

func (h *ActionHandler) Discourse20250501(ctx context.Context, c *app.RequestContext) {
	req := &discourseHertz.HandleDiscourseRequest{}
	if err := binding.BindAndValidate(c, req); err != nil {
		handler.ErrorResponse(ctx, c, err)
		return
	}
	discourseSvc.GetDiscourseService().Discourse(ctx, c, req)
}

func (h *ActionHandler) Run20250501(ctx context.Context, c *app.RequestContext) {
	req := &discourseHertz.RunRequest{}
	if err := binding.BindAndValidate(c, req); err != nil {
		handler.ErrorResponse(ctx, c, err)
		return
	}
	discourseSvc.GetDiscourseService().Run(ctx, c, req)
}

func (h *ActionHandler) Report20250501(ctx context.Context, c *app.RequestContext) {
	req := &discourseHertz.QueryDiagnoseTaskRunDetailReq{}
	if err := binding.BindAndValidate(c, req); err != nil {
		handler.ErrorResponse(ctx, c, err)
		return
	}

	result, err := discourseSvc.GetDiscourseService().Report(ctx, req)
	if err != nil {
		handler.ErrorResponse(ctx, c, err)
		return
	}
	ehttp.DoResponse(ctx, c, result)
}

func (h *ActionHandler) ReportMirror20250501(ctx context.Context, c *app.RequestContext) {
	req := &discourseHertz.QueryDiagnoseTaskRunDetailReq{}
	if err := binding.BindAndValidate(c, req); err != nil {
		handler.ErrorResponse(ctx, c, err)
		return
	}

	result, err := discourseSvc.GetDiscourseService().ReportMirror(ctx, req)
	if err != nil {
		handler.ErrorResponse(ctx, c, err)
		return
	}
	ehttp.DoResponse(ctx, c, result)
}

func (h *ActionHandler) Detail20250501(ctx context.Context, c *app.RequestContext) {
	req := &discourseHertz.DetailRequest{}
	if err := binding.BindAndValidate(c, req); err != nil {
		handler.ErrorResponse(ctx, c, err)
		return
	}
	result, err := discourseSvc.GetDiscourseService().Detail(ctx, req)
	if err != nil {
		handler.ErrorResponse(ctx, c, err)
		return
	}
	ehttp.DoResponse(ctx, c, result)
}

func (h *ActionHandler) History20250501(ctx context.Context, c *app.RequestContext) {
	req := &discourseHertz.HistoryRequest{}
	if err := binding.BindAndValidate(c, req); err != nil {
		handler.ErrorResponse(ctx, c, err)
		return
	}
	result, err := discourseSvc.GetDiscourseService().History(ctx, req)
	if err != nil {
		handler.ErrorResponse(ctx, c, err)
		return
	}
	ehttp.DoResponse(ctx, c, result)
}

func (h *ActionHandler) Stage20250501(ctx context.Context, c *app.RequestContext) {
	req := &discourseHertz.StageRequest{}
	if err := binding.BindAndValidate(c, req); err != nil {
		handler.ErrorResponse(ctx, c, err)
	}
	result, err := discourseSvc.GetDiscourseService().Stage(ctx, req)
	if err != nil {
		handler.ErrorResponse(ctx, c, err)
		return
	}
	ehttp.DoResponse(ctx, c, result)
}
