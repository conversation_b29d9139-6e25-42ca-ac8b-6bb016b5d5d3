package feedback

import (
	"code.byted.org/middleware/hertz/pkg/app"
	"code.byted.org/middleware/hertz_ext/v2/binding"
	"code.byted.org/volcengine-support/cloud-sherlock/biz/errorcode"
	"code.byted.org/volcengine-support/cloud-sherlock/biz/handler"
	"code.byted.org/volcengine-support/cloud-sherlock/biz/middleware"
	feedbackSvc "code.byted.org/volcengine-support/cloud-sherlock/biz/service/feedback"
	"code.byted.org/volcengine-support/cloud-sherlock/gen/hertz/feedback"
	ehttp "code.byted.org/volcengine-support/gopkg/hertzext"
	"context"
	"github.com/samber/lo"
)

type ActionHandler struct {
	ehttp.CommonActionHandler
}

func (h *ActionHandler) SubmitDiagnoseTaskFeedback20241201(ctx context.Context, c *app.RequestContext) {
	req := &feedback.SubmitDiagnoseTaskFeedbackReq{}
	if err := binding.BindAndValidate(c, req); err != nil {
		handler.ErrorResponse(ctx, c, err)
		return
	}
	if userEmail, ok := c.Get(middleware.Email); ok {
		if userEmailStr, ok := userEmail.(string); ok {
			req.FeedbackUserID = lo.ToPtr(userEmailStr)
		}
	}
	if req.DiagnoseTaskRunID == 0 {
		handler.ErrorResponse(ctx, c, errorcode.ErrRequestParamInvalid.WithArgs("DiagnoseTaskRunID"))
		return
	}
	err := feedbackSvc.GetFeedbackService().SubmitDiagnoseTaskFeedback(ctx, *req)
	if err != nil {
		handler.ErrorResponse(ctx, c, err)
		return
	}
	ehttp.DoResponse(ctx, c, nil)
}

func (h *ActionHandler) SubmitFeedback20241201(ctx context.Context, c *app.RequestContext) {
	req := &feedback.SubmitFeedbackReq{}
	if err := binding.BindAndValidate(c, req); err != nil {
		handler.ErrorResponse(ctx, c, err)
		return
	}
	if userEmail, ok := c.Get(middleware.Email); ok {
		if userEmailStr, ok := userEmail.(string); ok {
			req.FeedbackUserID = lo.ToPtr(userEmailStr)
		}
	}
	result, err := feedbackSvc.GetFeedbackService().SubmitFeedback(ctx, req)
	if err != nil {
		handler.ErrorResponse(ctx, c, err)
		return
	}
	ehttp.DoResponse(ctx, c, result)
}

func (h *ActionHandler) QueryFeedback20241201(ctx context.Context, c *app.RequestContext) {
	req := &feedback.QueryFeedbackReq{}
	if err := binding.BindAndValidate(c, req); err != nil {
		handler.ErrorResponse(ctx, c, err)
		return
	}
	result, err := feedbackSvc.GetFeedbackService().QueryFeedback(ctx, req)
	if err != nil {
		handler.ErrorResponse(ctx, c, err)
		return
	}
	ehttp.DoResponse(ctx, c, result)
}

func (h *ActionHandler) QueryFeedbackLabel20241201(ctx context.Context, c *app.RequestContext) {
	req := &feedback.QueryFeedbackLabelReq{}
	if err := binding.BindAndValidate(c, req); err != nil {
		handler.ErrorResponse(ctx, c, err)
		return
	}
	result, err := feedbackSvc.GetFeedbackService().QueryFeedbackLabel(ctx, req)
	if err != nil {
		handler.ErrorResponse(ctx, c, err)
		return
	}
	ehttp.DoResponse(ctx, c, result)
}

func (h *ActionHandler) StartHandle20241201(ctx context.Context, c *app.RequestContext) {
	req := &feedback.StartHandleReq{}
	if err := binding.BindAndValidate(c, req); err != nil {
		handler.ErrorResponse(ctx, c, err)
		return
	}
	if userEmail, ok := c.Get(middleware.Email); ok {
		if userEmailStr, ok := userEmail.(string); ok {
			req.HandleUserID = lo.ToPtr(userEmailStr)
		}
	}
	err := feedbackSvc.GetFeedbackService().StartHandle(ctx, req)
	if err != nil {
		handler.ErrorResponse(ctx, c, err)
		return
	}
	ehttp.DoResponse(ctx, c, nil)
}

func (h *ActionHandler) EndHandle20241201(ctx context.Context, c *app.RequestContext) {
	req := &feedback.EndHandleReq{}
	if err := binding.BindAndValidate(c, req); err != nil {
		handler.ErrorResponse(ctx, c, err)
		return
	}
	if userEmail, ok := c.Get(middleware.Email); ok {
		if userEmailStr, ok := userEmail.(string); ok {
			req.HandleUserID = lo.ToPtr(userEmailStr)
		}
	}
	err := feedbackSvc.GetFeedbackService().EndHandle(ctx, req)
	if err != nil {
		handler.ErrorResponse(ctx, c, err)
		return
	}
	ehttp.DoResponse(ctx, c, nil)
}

func (h *ActionHandler) AddFeedbackLabel20241201(ctx context.Context, c *app.RequestContext) {
	req := &feedback.AddFeedbackLabelReq{}
	if err := binding.BindAndValidate(c, req); err != nil {
		handler.ErrorResponse(ctx, c, err)
		return
	}
	err := feedbackSvc.GetFeedbackService().AddFeedbackLabel(ctx, req)
	if err != nil {
		handler.ErrorResponse(ctx, c, err)
		return
	}
	ehttp.DoResponse(ctx, c, nil)
}
func (h *ActionHandler) DeleteFeedbackLabel20241201(ctx context.Context, c *app.RequestContext) {
	req := &feedback.DeleteFeedbackLabelReq{}
	if err := binding.BindAndValidate(c, req); err != nil {
		handler.ErrorResponse(ctx, c, err)
		return
	}
	err := feedbackSvc.GetFeedbackService().DeleteFeedbackLabel(ctx, req)
	if err != nil {
		handler.ErrorResponse(ctx, c, err)
		return
	}
	ehttp.DoResponse(ctx, c, nil)
}

func (h *ActionHandler) QueryResultLabels20241201(ctx context.Context, c *app.RequestContext) {
	req := &feedback.QueryResultLabelsReq{}
	if err := binding.BindAndValidate(c, req); err != nil {
		handler.ErrorResponse(ctx, c, err)
		return
	}
	result, err := feedbackSvc.GetFeedbackService().QueryResultLabels(ctx)
	if err != nil {
		handler.ErrorResponse(ctx, c, err)
		return
	}
	ehttp.DoResponse(ctx, c, result)

}

func (h *ActionHandler) PagePermission20241201(ctx context.Context, c *app.RequestContext) {
	req := &feedback.PagePermissionReq{}
	if err := binding.BindAndValidate(c, req); err != nil {
		handler.ErrorResponse(ctx, c, err)
		return
	}
	if userEmail, ok := c.Get(middleware.Email); ok {
		if userEmailStr, ok := userEmail.(string); ok {
			req.UserID = lo.ToPtr(userEmailStr)
		}
	}
	err := feedbackSvc.GetFeedbackService().PagePermission(ctx, req)
	if err != nil {
		handler.ErrorResponse(ctx, c, err)
		return
	}
	ehttp.DoResponse(ctx, c, nil)
}
