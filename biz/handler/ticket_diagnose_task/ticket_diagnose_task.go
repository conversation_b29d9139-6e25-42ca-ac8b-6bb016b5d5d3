package ticket_diagnose_task

import (
	"code.byted.org/middleware/hertz/pkg/app"
	"code.byted.org/middleware/hertz_ext/v2/binding"
	"code.byted.org/volcengine-support/cloud-sherlock/biz/errorcode"
	"code.byted.org/volcengine-support/cloud-sherlock/biz/handler"
	"code.byted.org/volcengine-support/cloud-sherlock/biz/middleware"
	ticketDiagnoseTaskSrvc "code.byted.org/volcengine-support/cloud-sherlock/biz/service/ticket_diagnose_task"
	"code.byted.org/volcengine-support/cloud-sherlock/gen/hertz/ticket_diagnose_task"
	ehttp "code.byted.org/volcengine-support/gopkg/hertzext"
	"context"
	"strings"
)

type ActionHandler struct {
	ehttp.CommonActionHandler
}

func (h *ActionHandler) DescribeTaskDiagnoseItem20241201(ctx context.Context, c *app.RequestContext) {
	req := &ticket_diagnose_task.DescribeTaskDiagnoseItemReq{}
	if err := binding.BindAndValidate(c, req); err != nil {
		handler.ErrorResponse(ctx, c, err)
		return
	}
	if req.DiagnoseTaskID == 0 {
		handler.ErrorResponse(ctx, c, errorcode.ErrRequestParamInvalid.WithArgs("DiagnoseTaskID"))
		return
	}
	if req.DiagnoseItemID == 0 {
		handler.ErrorResponse(ctx, c, errorcode.ErrRequestParamInvalid.WithArgs("DiagnoseItemID"))
		return
	}
	result, err := ticketDiagnoseTaskSrvc.GetTicketDiagnoseTaskService().DescribeTaskDiagnoseItem(ctx, *req)
	if err != nil {
		handler.ErrorResponse(ctx, c, err)
		return
	}
	ehttp.DoResponse(ctx, c, result)
}

func (h *ActionHandler) ListTicketDiagnoseTask20241201(ctx context.Context, c *app.RequestContext) {
	req := &ticket_diagnose_task.ListTicketDiagnoseTaskReq{}
	if err := binding.BindAndValidate(c, req); err != nil {
		handler.ErrorResponse(ctx, c, err)
		return
	}
	if req.TicketID == "" {
		handler.ErrorResponse(ctx, c, errorcode.ErrRequestParamInvalid.WithArgs("TicketID"))
		return
	}
	result, err := ticketDiagnoseTaskSrvc.GetTicketDiagnoseTaskService().ListTicketDiagnoseTask(ctx, *req)
	if err != nil {
		handler.ErrorResponse(ctx, c, err)
		return
	}
	ehttp.DoResponse(ctx, c, result)
}

func (h *ActionHandler) ListDiagnoseTask20241201(ctx context.Context, c *app.RequestContext) {
	req := &ticket_diagnose_task.ListDiagnoseTaskReq{}
	if err := binding.BindAndValidate(c, req); err != nil {
		handler.ErrorResponse(ctx, c, err)
		return
	}
	result, err := ticketDiagnoseTaskSrvc.GetTicketDiagnoseTaskService().ListDiagnoseTask(ctx, *req)
	if err != nil {
		handler.ErrorResponse(ctx, c, err)
		return
	}
	ehttp.DoResponse(ctx, c, result)
}

func (h *ActionHandler) QueryDiagnoseTaskCategory20241201(ctx context.Context, c *app.RequestContext) {
	req := &ticket_diagnose_task.QueryDiagnoseTaskCategoryReq{}
	if err := binding.BindAndValidate(c, req); err != nil {
		handler.ErrorResponse(ctx, c, err)
		return
	}
	if req.DiagnoseTaskID == 0 {
		handler.ErrorResponse(ctx, c, errorcode.ErrRequestParamInvalid.WithArgs("DiagnoseTaskID"))
		return
	}
	result, err := ticketDiagnoseTaskSrvc.GetTicketDiagnoseTaskService().QueryDiagnoseTaskCategory(ctx, *req)
	if err != nil {
		handler.ErrorResponse(ctx, c, err)
		return
	}
	ehttp.DoResponse(ctx, c, result)
}

func (h *ActionHandler) QueryDiagnoseTaskSummary20241201(ctx context.Context, c *app.RequestContext) {
	req := &ticket_diagnose_task.QueryDiagnoseTaskSummaryReq{}
	if err := binding.BindAndValidate(c, req); err != nil {
		handler.ErrorResponse(ctx, c, err)
		return
	}
	if req.DiagnoseTaskID == 0 {
		handler.ErrorResponse(ctx, c, errorcode.ErrRequestParamInvalid.WithArgs("DiagnoseTaskID"))
		return
	}
	result, err := ticketDiagnoseTaskSrvc.GetTicketDiagnoseTaskService().QueryDiagnoseTaskSummary(ctx, *req)
	if err != nil {
		handler.ErrorResponse(ctx, c, err)
		return
	}
	ehttp.DoResponse(ctx, c, result)
}
func (h *ActionHandler) CreateDiagnoseTask20241201(ctx context.Context, c *app.RequestContext) {
	req := &ticket_diagnose_task.CreateDiagnoseTaskReq{}
	if err := binding.BindAndValidate(c, req); err != nil {
		handler.ErrorResponse(ctx, c, err)
		return
	}
	if userEmail, ok := c.Get(middleware.Email); ok {
		if userEmailStr, ok := userEmail.(string); ok {
			req.CreateUserID = userEmailStr
		}
	}
	if req.TicketID == "" {
		handler.ErrorResponse(ctx, c, errorcode.ErrRequestParamInvalid.WithArgs("TicketID"))
		return
	}
	//删除req资源的实例的前后空格
	for i, resource := range req.DiagnoseResources {
		for j, instance := range resource.Instances {
			req.DiagnoseResources[i].Instances[j] = strings.TrimSpace(instance)
		}
	}
	result, err := ticketDiagnoseTaskSrvc.GetTicketDiagnoseTaskService().CreateDiagnoseTask(ctx, *req)
	if err != nil {
		handler.ErrorResponse(ctx, c, err)
		return
	}
	ehttp.DoResponse(ctx, c, result)
}

func (h *ActionHandler) QueryDiagnoseTaskItems20241201(ctx context.Context, c *app.RequestContext) {
	req := &ticket_diagnose_task.QueryDiagnoseTaskItemsReq{}
	if err := binding.BindAndValidate(c, req); err != nil {
		handler.ErrorResponse(ctx, c, err)
		return
	}
	if req.DiagnoseTaskID == 0 {
		handler.ErrorResponse(ctx, c, errorcode.ErrRequestParamInvalid.WithArgs("DiagnoseTaskID"))
		return
	}
	result, err := ticketDiagnoseTaskSrvc.GetTicketDiagnoseTaskService().QueryDiagnoseTaskItems(ctx, *req)
	if err != nil {
		handler.ErrorResponse(ctx, c, err)
		return
	}
	ehttp.DoResponse(ctx, c, result)
}

func (h *ActionHandler) QueryDiagnoseTaskResources20241201(ctx context.Context, c *app.RequestContext) {
	req := &ticket_diagnose_task.QueryDiagnoseTaskResourcesReq{}
	if err := binding.BindAndValidate(c, req); err != nil {
		handler.ErrorResponse(ctx, c, err)
		return
	}
	if req.DiagnoseTaskID == 0 {
		handler.ErrorResponse(ctx, c, errorcode.ErrRequestParamInvalid.WithArgs("DiagnoseTaskID"))
		return
	}
	result, err := ticketDiagnoseTaskSrvc.GetTicketDiagnoseTaskService().QueryDiagnoseTaskResources(ctx, *req)
	if err != nil {
		handler.ErrorResponse(ctx, c, err)
		return
	}
	ehttp.DoResponse(ctx, c, result)
}
