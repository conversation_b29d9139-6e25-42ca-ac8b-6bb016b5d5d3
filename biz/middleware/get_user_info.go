package middleware

import (
	"context"
	"strings"

	"code.byted.org/middleware/hertz/pkg/app"

	"code.byted.org/volcengine-support/cloud-sherlock/biz/errorcode"
	"code.byted.org/volcengine-support/cloud-sherlock/biz/handler"
	"code.byted.org/volcengine-support/cloud-sherlock/pkg/sso"
	"code.byted.org/volcengine-support/cloud-sherlock/utils"
)

const Email = "email"

func GetUserInfo() app.HandlerFunc {
	return func(ctx context.Context, c *app.RequestContext) {
		oauthLogger := utils.NewModuleLogger("GetUserInfo")
		// 如果是/login, 或者 /oauth2/access_token ，则允许访问，否则会发生重定向循环
		for _, v := range WhiteList {
			if strings.Contains(c.FullPath(), v) {
				c.Next(ctx)
				return
			}
		}

		token := c.Query("Token")
		// 如果token为空，继续判断是否存在 epsauth 的 cookie
		if token == "" {
			oauthLogger.CtxWarn(ctx, "byted sso token is empty")

			// 直接跳转到 epsauth 认证
			goto EPSAUTH
		} else {
			userInfo, err := sso.GetUserInfoByTokenFromRedis(token)
			if err != nil {
				handler.ErrorResponse(ctx, c, errorcode.ErrorCodeAuthTokenInvalid)
				c.Abort()
				return
			}
			c.Set("email", userInfo.Email)
			c.Next(ctx)
			return
		}

	EPSAUTH:
		epsToken := string(c.Cookie("X-EPS-AUTH-TOKEN"))
		if epsToken == "" {
			oauthLogger.CtxWarn(ctx, "eps sso token is empty")
			// 如果 epsToken 为空，到此，两个认证方式的 token 都缺失，则返回缺失 token 的错误信息
			handler.ErrorResponse(ctx, c, errorcode.ErrorCodeAuthTokenMissing)
			c.Abort()
			return
		} else {
			userInfo, err := sso.GetUserInfoByTokenFromRedis(epsToken)
			if err != nil {
				handler.ErrorResponse(ctx, c, errorcode.ErrorCodeAuthTokenInvalid)
				c.Abort()
				return
			}
			c.Set(Email, userInfo.Email)
			c.Next(ctx)
		}
	}
}
