package middleware

import (
	"context"
	"strings"
	"time"

	"code.byted.org/eps-platform/biz_gopkg/epsauth"
	"code.byted.org/middleware/hertz/pkg/app"

	"code.byted.org/volcengine-support/cloud-sherlock/biz/errorcode"
	"code.byted.org/volcengine-support/cloud-sherlock/biz/handler"
	redisStorage "code.byted.org/volcengine-support/cloud-sherlock/pkg/rds_redis"
	"code.byted.org/volcengine-support/cloud-sherlock/pkg/sso"
	"code.byted.org/volcengine-support/cloud-sherlock/utils"
)

// 这个变量表示 para 参数的名称，用来存放本次访问的原始path
var OriginalPathParamName = "originalPath"

// 1、检查用户的请求 path 是否包含在白名单中，如果是，则直接放行
// 2、如果用户的请求 path 不在白名单中，则检查用户的请求中是否存在 byte sso token，如果不存在，继续
// 检查用户的请求中是否存在 epsauth 的 cookie，如果不存在，则返回未登录错误信息，如果存在，
// 则使用 epsauth 进行认证
// 3、如果用户的请求中存在 token，则先查询 redis 中是否存在 token，如果存在，则直接放行，如果不存在，则使用 token查询 sso userinfo 接口，获取用户信息，如果获取用户信息失败，则返回错误信息，如果获取用户信息成功，则将用户信息写入 redis 中，过期时间为7天，然后放行
func OAuth2() app.HandlerFunc {
	return func(ctx context.Context, c *app.RequestContext) {
		oauthLogger := utils.NewModuleLogger("OAuth2")
		// 如果是/login, 或者 /oauth2/access_token ，则允许访问，否则会发生重定向循环
		for _, v := range WhiteList {
			if strings.Contains(c.FullPath(), v) {
				c.Next(ctx)
				return
			}
		}

		// 首先，尝试使用 byte sso token 进行认证
		token := c.Query("Token")
		// 如果token为空，继续判断是否存在 epsauth 的 cookie
		if token == "" {
			oauthLogger.CtxWarn(ctx, "byted sso token is empty")

			// 直接跳转到 epsauth 认证
			goto EPSAUTH
		} else {
			// 如果token不为空，则查询redis中是否存在token
			keyName := "token_" + utils.Sha256(token)
			oauthLogger.CtxInfo(ctx, "tokenKeyName: %s", keyName)
			ret, err := redisStorage.RedisClient.Exists(keyName).Result()
			// 如果redis中存在token，则直接放行
			if err == nil && ret == 1 {
				c.Next(ctx)
				return
			}
			// 如果redis中不存在token，则使用token查询sso userinfo 接口，获取用户信息
			ssoUserInfo, err := sso.GetUserInfoByToken(token)

			// 如果获取用户信息失败，这里不能直接返回错误，继续尝试使用 epsauth 进行认证
			if err != nil {
				oauthLogger.CtxWarn(ctx, "byted sso 认证失败 %+v\n", err)
				// 直接跳转到 epsauth 认证
				goto EPSAUTH
			}

			// 即使 sso 返回了过期信息，json 反序列号也不会报错，只是字段为空，
			// 这里不能直接返回错误，继续尝试使用 epsauth 进行认证
			if ssoUserInfo.Name == "" {
				oauthLogger.CtxWarn(ctx, "byted sso userinfo 认证失败 %+v\n", err)
				// 直接跳转到 epsauth 认证
				goto EPSAUTH
			}
			// 如果获取用户信息成功，则将用户信息写入 redis 中
			// 如果写入 redis 失败，则返回错误信息, 同时 abort，不再继续执行 epsauth 认证
			if ssoUserInfo != nil {
				err = ssoUserInfo.StoreToRedis(keyName, time.Duration(24*time.Hour*7).Nanoseconds())
				if err != nil {
					handler.ErrorResponse(ctx, c, errorcode.ErrorCodeStoreRedisError)
					c.Abort()
					return
				}
			}

			// 获取用户信息成功，且成功写入了 redis，则放行
			oauthLogger.CtxInfo(ctx, "byted sso 认证成功 %+v\n", ssoUserInfo.Name)
			// todo:这里将用户信息写入到了ctx 中，后续可以从 ctx 中获取用户信息
			c.Next(ctx)
			return
		}

	EPSAUTH:
		// 尝试使用 epsauth 进行认证
		epsToken := string(c.Cookie("X-EPS-AUTH-TOKEN"))
		if epsToken == "" {
			oauthLogger.CtxWarn(ctx, "eps sso token is empty")
			// 如果 epsToken 为空，到此，两个认证方式的 token 都缺失，则返回缺失 token 的错误信息
			handler.ErrorResponse(ctx, c, errorcode.ErrorCodeAuthTokenMissing)
			c.Abort()
			return
		} else {
			// 使用 epstoken 生成 redis 中的 keyName
			keyName := "token_" + utils.Sha256(epsToken)
			oauthLogger.CtxInfo(ctx, "epsTokenKeyName: %s", keyName)

			// 查询 redis 中是否存在 token
			ret, err := redisStorage.RedisClient.Exists(keyName).Result()
			// 如果redis中存在token，则直接放行
			if err == nil && ret == 1 {
				c.Next(ctx)
				return
			}

			// 如果redis中不存在token，则使用epsauth进行认证
			epsUser, err := epsauth.Authentication(ctx, c)
			// 如果 eps 认证失败，则返回错误信息, 同时 abort
			if err != nil {
				oauthLogger.CtxWarn(ctx, "eps token 认证失败 %+v\n", err)
				handler.ErrorResponse(ctx, c, errorcode.ErrorCodeAuthTokenInvalid)
				c.Abort()
				return
			}
			// 如果 eps 认证成功，则将用户信息写入 redis 中
			oauthLogger.CtxInfo(ctx, "eps token 认证成功 %+v\n", epsUser.Name)
			ssoUserInfo := sso.UserInfo{
				Name:  epsUser.Name,
				Email: epsUser.Email,
			}
			// 将 ssoUserInfo 的 RedisClient 设置为 redisStorage.RedisClient
			// 如果不设置，下一步会 panic
			ssoUserInfo.RedisClient = redisStorage.RedisClient
			err = ssoUserInfo.StoreToRedis(keyName, time.Duration(24*time.Hour*7).Nanoseconds())
			// 如果写入 redis 失败，则返回错误信息, 同时 abort
			if err != nil {
				oauthLogger.CtxWarn(ctx, "eps token 保存到 redis 失败 %+v\n", err)
				handler.ErrorResponse(ctx, c, errorcode.ErrorCodeStoreRedisError)
				c.Abort()
				return
			}

			// 如果 epsauth 认证成功，且成功写入了 redis，则放行
			c.Next(ctx)
			return
		}
	}
}
