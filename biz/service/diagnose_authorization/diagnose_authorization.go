package diagnose_authorization

import (
	"code.byted.org/eps-platform/volcengine-innersdk-golang/service/sop"
	"code.byted.org/volcengine-support/cloud-sherlock/biz/dal"
	"code.byted.org/volcengine-support/cloud-sherlock/gen/gorm/model"
	"code.byted.org/volcengine-support/cloud-sherlock/gen/hertz/common"
	"code.byted.org/volcengine-support/cloud-sherlock/gen/hertz/diagnose_authorization"
	"code.byted.org/volcengine-support/cloud-sherlock/pkg/official_auth_sdk"
	"code.byted.org/volcengine-support/cloud-sherlock/utils"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/samber/lo"
	"strings"
	"sync"
	"time"
)

var (
	service *DiagnoseAuthService
	once    sync.Once
)

type DiagnoseAuthService struct {
	logger *utils.ModuleLogger
}

func GetDiagnoseAuthService() *DiagnoseAuthService {
	once.Do(func() {
		service = &DiagnoseAuthService{
			logger: utils.NewModuleLogger("DiagnoseAuthService"),
		}
	})
	return service
}

func (s *DiagnoseAuthService) ApplyDiagnoseAuthorization(ctx context.Context, email string, req *diagnose_authorization.ApplyDiagnoseAuthorizationRequest) (*diagnose_authorization.ApplyDiagnoseAuthorizationResult, error) {

	s.logger.CtxInfo(ctx, "ApplyDiagnoseAuthorization req: %v", req)
	// 处理请求逻辑
	// 调用官方授权接口
	policyKeys := make([]string, 0)

	//构造applyReq的ApplicationPolicy参数
	applyPolicies := make([]*official_auth_sdk.ApplicationPolicy, 0)
	for id, instanceIds := range req.InstanceIDs {
		//根据id查询policy
		pd, err := dal.GetPolicyKeyByID(ctx, int64(id))
		if err != nil {
			s.logger.CtxError(ctx, "QueryProductCategoryByID err:%v", err)
			return nil, err
		}
		if pd.PolicyParams == nil || pd.PolicyKey == nil {
			s.logger.CtxInfo(ctx, "QueryProductCategoryByID policy:%v", pd)
			return nil, errors.New("policies is nil")
		}
		applyPolicy := &official_auth_sdk.ApplicationPolicy{}
		err = json.Unmarshal([]byte(*pd.PolicyParams), &applyPolicy)
		if err != nil {
			s.logger.CtxError(ctx, "Unmarshal err:%v", err)
			return nil, err
		}

		for _, content := range applyPolicy.ContentParams {
			if content.Key == "instance_ids" {
				content.Value = instanceIds
			}
		}
		applyPolicies = append(applyPolicies, applyPolicy)
		policyKeys = append(policyKeys, *pd.PolicyKey)
	}

	resp, err := official_auth_sdk.GetClient().CreateDiagnosticAuthorizationCommonWithContext(ctx, &map[string]interface{}{
		"ApplicantDuration": req.ApplicationDuration * 24 * 60,
		"ApplicationPolicy": applyPolicies,
		"ApplicationReason": req.ApplicationReason,
		"TicketID":          req.TicketID,
		"Creator":           email,
	})
	if err != nil {
		s.logger.CtxError(ctx, "CreateDiagnosticAuthorization err:%v", err)
		return nil, err
	}
	s.logger.CtxInfo(ctx, "CreateDiagnosticAuthorization resp: %v", resp)
	result := (*resp)["Result"].(map[string]interface{})
	authID := result["AuthorizationID"].(string)
	accountID := result["AccountID"].(string)
	endTime := result["EndTime"].(float64)
	// 将数据入库
	insIds, err := json.Marshal(req.InstanceIDs)
	if err != nil {
		s.logger.CtxError(ctx, "Marshal ProductCategoryIDs err:%v", err)
		return nil, errors.New(fmt.Sprintf("Marshal ProductCategoryIDs err:%v", err.Error()))
	}
	jsonPdIds, err := json.Marshal(req.ProductCategoryIDs)
	if err != nil {
		s.logger.CtxError(ctx, "Marshal ProductCategoryIDs err:%v", err)
		return nil, errors.New(fmt.Sprintf("Marshal ProductCategoryIDs err:%v", err.Error()))
	}
	var status int32 = 1
	auth := model.DiagnoseAuthorization{
		AuthorizationID:      lo.ToPtr(authID),
		AccountID:            lo.ToPtr(accountID),
		TicketID:             lo.ToPtr(req.TicketID),
		DiagnoseTemplateName: lo.ToPtr(req.DiagnoseTemplateName),
		ApplyUser:            lo.ToPtr(email),
		CreatedTime:          lo.ToPtr(int32(time.Now().Unix())),
		UpdateTime:           lo.ToPtr(int32(time.Now().Unix())),
		ExpiredTime:          lo.ToPtr(req.ApplicationDuration),
		StartTime:            lo.ToPtr(req.StartTime),
		EndTime:              official_auth_sdk.I64ToInt32(lo.ToPtr(int64(endTime))),
		ProductCategoryIds:   lo.ToPtr(string(jsonPdIds)),
		InstanceIds:          lo.ToPtr(string(insIds)),
		Status:               lo.ToPtr(status),
		ApplicationPolicy:    lo.ToPtr(utils.SliceJoin(policyKeys, ",")),
	}

	err = dal.AddDiagnoseAuth(ctx, &auth)
	if err != nil {
		s.logger.CtxError(ctx, "AddDiagnoseAuth err:%v", err)
		return nil, err
	}

	return &diagnose_authorization.ApplyDiagnoseAuthorizationResult{
		Id: lo.ToPtr(auth.ID),
	}, nil
}

func (s *DiagnoseAuthService) ListDiagnoseAuthorization(ctx context.Context, email string, req *diagnose_authorization.ListDiagnoseAuthorizationRequest) (*diagnose_authorization.ListDiagnoseAuthorizationResult, error) {

	s.logger.CtxInfo(ctx, "ListDiagnoseAuthorization req: %v", req)
	// 处理请求逻辑
	auths, count, err := dal.ListDiagnoseAuth(ctx, req)
	if err != nil {
		s.logger.CtxError(ctx, "ListDiagnoseAuthorization err:%v", err)
		return nil, err
	}
	diagnoseAuths := make([]*diagnose_authorization.DiagnoseAuthorization, 0)
	for _, auth := range auths {
		pcIds := make([]int32, 0)
		err := json.Unmarshal([]byte(*auth.ProductCategoryIds), &pcIds)
		if err != nil {
			s.logger.CtxError(ctx, "Unmarshal ProductCategoryIDs err:%v", err)
			return nil, err
		}

		diagnoseAuths = append(diagnoseAuths, &diagnose_authorization.DiagnoseAuthorization{
			Id:                lo.ToPtr(auth.ID),
			AuthorizationID:   auth.AuthorizationID,
			ApplyUser:         auth.ApplyUser,
			ApplicationPolicy: strings.Split(*auth.ApplicationPolicy, ","),
			AccountID:         auth.AccountID,
			CreatedTime: lo.ToPtr(time.Unix(int64(*auth.CreatedTime), 0).
				Format("2006-01-02 15:04:05")),
			UpdateTime: lo.ToPtr(time.Unix(int64(*auth.UpdateTime), 0).
				Format("2006-01-02 15:04:05")),
			StartTime: lo.ToPtr(time.Unix(int64(*auth.StartTime), 0).
				Format("2006-01-02 15:04:05")),
			EndTime: lo.ToPtr(time.Unix(int64(*auth.EndTime), 0).
				Format("2006-01-02 15:04:05")),
			ExpiredTime:          auth.ExpiredTime,
			Status:               auth.Status,
			TicketID:             auth.TicketID,
			DiagnoseTemplateName: auth.DiagnoseTemplateName,
			ProductCategoryID:    pcIds,
		})
	}
	return &diagnose_authorization.ListDiagnoseAuthorizationResult{
		Pagination: &common.Pagination{
			PageNumber: req.PageNumber,
			PageSize:   req.PageSize,
			TotalCount: count,
		},
		DiagnoseAuthorizations: diagnoseAuths,
	}, nil
}

func (s *DiagnoseAuthService) TerminateDiagnoseAuthorization(ctx context.Context, email string, req *diagnose_authorization.TerminateDiagnoseAuthorizationRequest) (*diagnose_authorization.TerminateDiagnoseAuthorizationResult, error) {
	s.logger.CtxInfo(ctx, "TerminateDiagnoseAuthorization req: %v", req)
	resp, err := official_auth_sdk.GetClient().TerminateDiagnosticAuthorization(&sop.TerminateDiagnosticAuthorizationInput{
		AuthorizationID: lo.ToPtr(req.AuthorizationID),
		Terminator:      lo.ToPtr(email),
	})
	if err != nil {
		s.logger.CtxError(ctx, "official_auth_sdk TerminateDiagnoseAuthorization err:%v", err)
		return nil, err
	}
	if resp.DiagnosticAuthorization == nil {
		s.logger.CtxError(ctx, "official_auth_sdk TerminateDiagnoseAuthorization err :%v", errors.New(resp.Metadata.Error.Message))
		return nil, errors.New(resp.Metadata.Error.Message)
	}
	//更改授权表中的状态
	status := int32(4)
	err = dal.TerminateDiagnoseAuth(ctx, &model.DiagnoseAuthorization{
		AuthorizationID: lo.ToPtr(req.AuthorizationID),
		Status:          lo.ToPtr(status),
		EndTime:         lo.ToPtr(int32(time.Now().Unix())),
	})
	if err != nil {
		s.logger.CtxError(ctx, "mysql TerminateDiagnoseAuthorization err:%v", err)
		return nil, err
	}
	return &diagnose_authorization.TerminateDiagnoseAuthorizationResult{
		Result: true,
	}, nil
}
