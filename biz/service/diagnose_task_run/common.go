package diagnose_task_run

import (
	"code.byted.org/volcengine-support/cloud-sherlock/biz/errorcode"
	"code.byted.org/volcengine-support/cloud-sherlock/biz/service/diagnose"
	diagnosetask "code.byted.org/volcengine-support/cloud-sherlock/biz/service/diagnose_task_v2"
	"code.byted.org/volcengine-support/cloud-sherlock/gen/gorm/model"
	"code.byted.org/volcengine-support/cloud-sherlock/gen/hertz/diagnose_task_v2"
	"code.byted.org/volcengine-support/cloud-sherlock/gen/hertz/product_category"
	"code.byted.org/volcengine-support/cloud-sherlock/pkg/official_auth_api"
	"code.byted.org/volcengine-support/cloud-sherlock/pkg/statemachines"
	"code.byted.org/volcengine-support/cloud-sherlock/pkg/ticket_openapi"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/samber/lo"
	"gorm.io/gorm"
	"time"

	"code.byted.org/volcengine-support/cloud-sherlock/biz/dal"
	"code.byted.org/volcengine-support/cloud-sherlock/gen/hertz/diagnose_result"
	"code.byted.org/volcengine-support/cloud-sherlock/gen/hertz/diagnose_task_run"
	"code.byted.org/volcengine-support/cloud-sherlock/utils"
)

const (
	DiagnoseResultLevelFailed   = "failed"
	DiagnoseResultLevelCritical = "critical"
	DiagnoseResultLevelError    = "error"
	DiagnoseResultLevelWarning  = "warning"
	DiagnoseResultLevelInfo     = "info"
	DiagnoseResultLevelUnknown  = "unknown"

	DiagnoseResultLevelFailedNum   = int32(15)
	DiagnoseResultLevelCriticalNum = int32(11)
	DiagnoseResultLevelErrorNum    = int32(7)
	DiagnoseResultLevelWarningNum  = int32(3)
	DiagnoseResultLevelInfoNum     = int32(1)
	DiagnoseResultLevelUnknownNum  = int32(0)

	DiagnoseTaskStatusWaiting    = "waiting"
	DiagnoseTaskStatusRunning    = "running"
	DiagnoseTaskStatusFinish     = "finish"
	DiagnoseTaskStatusFailed     = "failed"
	DiagnoseTaskStatusTerminated = "terminated"

	DiagnoseItemStatusWaiting    = "waiting"
	DiagnoseItemStatusRunning    = "running"
	DiagnoseItemStatusFinish     = "finish"
	DiagnoseItemStatusFailed     = "failed"
	DiagnoseItemStatusTerminated = "terminated"
)

func TransferToResultLevel(levelCounts []*dal.DiagnoseResultLevelCountGroupBy) diagnose_result.DiagnoseResultLevel {
	diagnoseResultLevel := diagnose_result.DiagnoseResultLevel{}
	for _, levelCount := range levelCounts {
		switch levelCount.DiagnoseResultLevel {
		case DiagnoseResultLevelCritical:
			diagnoseResultLevel.Critical = levelCount.Count
		case DiagnoseResultLevelError:
			diagnoseResultLevel.Error = levelCount.Count
		case DiagnoseResultLevelWarning:
			diagnoseResultLevel.Warning = levelCount.Count
		case DiagnoseResultLevelFailed:
			diagnoseResultLevel.Failed = levelCount.Count
		case DiagnoseResultLevelInfo:
			diagnoseResultLevel.Info = levelCount.Count
		default:
			diagnoseResultLevel.Unknown = levelCount.Count
		}
	}
	return diagnoseResultLevel
}

// TransferStringToDiagnoseResource converts a JSON string to a slice of *diagnose_task_run.DiagnoseResource
func TransferStringToDiagnoseResource(DiagnoseResource *string) ([]*diagnose_result.DiagnoseResource, error) {
	if DiagnoseResource == nil {
		return nil, nil
	}
	diagnoseResourceArray := make([]*diagnose_result.DiagnoseResource, 0)
	err := json.Unmarshal([]byte(*DiagnoseResource), &diagnoseResourceArray)
	if err != nil {
		return nil, err
	}
	return diagnoseResourceArray, nil
}

// TransferDiagnoseResourceToString 将 []*diagnose_task_run.DiagnoseResource 转换为 JSON 字符串
func TransferDiagnoseResourceToString(resources []*diagnose_result.DiagnoseResource) (string, error) {
	if resources == nil {
		return "", nil
	}
	jsonBytes, err := json.Marshal(resources)
	if err != nil {
		return "", err
	}
	return string(jsonBytes), nil
}

func TransferStringToArrayInt(result *string) ([]int64, error) {
	if result == nil {
		return nil, nil
	}
	intArray := make([]int64, 0)
	err := json.Unmarshal([]byte(*result), &intArray)
	if err != nil {
		return nil, err
	}
	return intArray, nil
}

func TransferStringToArrayString(str *string) ([]string, error) {
	if str == nil {
		return nil, nil
	}
	stringArray := make([]string, 0)
	err := json.Unmarshal([]byte(*str), &stringArray)
	if err != nil {
		return nil, err
	}
	return stringArray, nil
}

// TransferDiagnoseItemStatusToString 将 map[int64]*diagnose_task_run.DiagnoseItemStatus 转换为 JSON 字符串
func TransferDiagnoseItemStatusToString(statuses map[int64]*diagnose_task_run.DiagnoseItemStatus) (string, error) {
	if statuses == nil {
		return "", nil
	}
	jsonBytes, err := json.Marshal(statuses)
	if err != nil {
		return "", err
	}
	return string(jsonBytes), nil
}

// TransferStringToDiagnoseItemStatus 将 JSON 字符串转换为 map[int64]*diagnose_task_run.DiagnoseItemStatus
func TransferStringToDiagnoseItemStatus(statusStr *string) (map[int64]*diagnose_task_run.DiagnoseItemStatus, error) {
	if statusStr == nil {
		return nil, nil
	}
	statusMap := make(map[int64]*diagnose_task_run.DiagnoseItemStatus)
	err := json.Unmarshal([]byte(*statusStr), &statusMap)
	if err != nil {
		return nil, err
	}
	return statusMap, nil
}

func GetLevelByTaskRunIDItemID(ctx context.Context, taskRunID int64, itemID int64) (*diagnose_result.DiagnoseResultLevel, error) {
	itemGroupByLevel, err := dal.DiagnoseResultsGroupByLevelByTaskRunIDItemID(ctx, taskRunID, itemID)
	if err != nil {
		return nil, err
	}
	itemLevel := TransferToResultLevel(itemGroupByLevel)
	return &itemLevel, nil
}

func GetLevelByTaskRunID(ctx context.Context, taskRunID int64) (*diagnose_result.DiagnoseResultLevel, error) {
	itemGroupByLevel, err := dal.DiagnoseResultsGroupByLevelByDiagnoseTaskRunID(ctx, taskRunID)
	if err != nil {
		return nil, err
	}
	itemLevel := TransferToResultLevel(itemGroupByLevel)
	return &itemLevel, nil
}

func GetLevelByTaskRunIDProductCategoryID(ctx context.Context, taskRunID, productCategoryID int64) (*diagnose_result.DiagnoseResultLevel, error) {
	itemGroupByLevel, err := dal.DiagnoseResultsGroupByLevelByDiagnoseTaskRunIDProductCategoryID(ctx, taskRunID, productCategoryID)
	if err != nil {
		return nil, err
	}
	itemLevel := TransferToResultLevel(itemGroupByLevel)
	return &itemLevel, nil
}

func GetLevelByTaskRunIDItemIDInstanceIDsLevels(ctx context.Context, taskRunID int64, itemID int64, instanceIDs, levels []string) (*diagnose_result.DiagnoseResultLevel, error) {
	itemGroupByLevel, err := dal.DiagnoseResultsGroupByLevelByTaskRunIDItemIDInstanceIDsLevels(ctx, taskRunID, itemID, instanceIDs, levels)
	if err != nil {
		return nil, err
	}
	itemLevel := TransferToResultLevel(itemGroupByLevel)
	return &itemLevel, nil
}

// FilterDiagnoseResourceByDiagnoseItems 通过诊断项过滤资源
func FilterDiagnoseResourceByDiagnoseItems(resource []*diagnose_result.DiagnoseResource, diagnoseItems []*diagnose_task_v2.DiagnoseItem) []*diagnose_result.DiagnoseResource {
	diagnoseResource := make([]*diagnose_result.DiagnoseResource, 0)
	resourceMap := make(map[int64]struct{})
	for _, item := range diagnoseItems {
		resourceMap[item.ProductCategory.Id] = struct{}{}
	}
	for _, item := range resource {
		if _, ok := resourceMap[item.ProductCategoryID]; ok && len(item.Instances) > 0 {
			diagnoseResource = append(diagnoseResource, item)
		}
	}
	return diagnoseResource
}

func GetDiagnoseResultLevelNum(level string) int32 {
	switch level {
	case DiagnoseResultLevelFailed:
		return DiagnoseResultLevelFailedNum
	case DiagnoseResultLevelCritical:
		return DiagnoseResultLevelCriticalNum
	case DiagnoseResultLevelError:
		return DiagnoseResultLevelErrorNum
	case DiagnoseResultLevelWarning:
		return DiagnoseResultLevelWarningNum
	case DiagnoseResultLevelInfo:
		return DiagnoseResultLevelInfoNum
	default:
		return DiagnoseResultLevelUnknownNum
	}
}

func TransferModelProductCategoryToHertzProductCategory(productCategory *model.ProductCategory) *product_category.ProductCategory {
	return &product_category.ProductCategory{
		Id:             productCategory.ID,
		Product:        productCategory.Product,
		SubProduct:     productCategory.SubProduct,
		ResourceType:   productCategory.ResourceType,
		ProductCn:      productCategory.ProductCn,
		SubProductCn:   productCategory.SubProductCn,
		ResourceTypeCn: productCategory.ResourceTypeCn,
	}
}

func GetProductCategoryByID(ctx context.Context, productCategoryID int64) (*product_category.ProductCategory, error) {
	productCategory, err := dal.QueryProductCategoryByID(ctx, productCategoryID)
	if err != nil {
		return nil, err
	}
	return TransferModelProductCategoryToHertzProductCategory(productCategory), nil
}

// MonitorTimeIsValid 验证监控时间是否符合限制
func MonitorTimeIsValid(ctx context.Context, start, end, productCategoryID int64) error {
	// 将 int64 类型的时间戳转换为 time.Time 类型
	startTime := time.Unix(start, 0).UTC()
	endTime := time.Unix(end, 0).UTC()
	// 获取产品分类的监控时间限制
	p, err := dal.QueryProductCategoryByID(ctx, productCategoryID)
	if err != nil {
		return err
	}
	maxHoursBeforeNow := p.MaxStartTimeBeforeNowHour
	maxDurationHours := p.MaxDurationHour
	productName := fmt.Sprintf("%s-%s-%s", p.ProductCn, p.SubProductCn, p.ResourceTypeCn)

	now := time.Now().UTC()
	// 检查开始时间是否超过当前时间指定小时数之前
	if maxHoursBeforeNow != nil {
		maxBeforeTime := now.Add(-time.Duration(*maxHoursBeforeNow) * time.Hour)
		if startTime.Before(maxBeforeTime) {
			return errorcode.ErrMonitorTimeBeforeNow.WithArgs(productName, *maxHoursBeforeNow)
		}
	}

	// 检查结束时间是否早于开始时间
	if endTime.Before(startTime) {
		return errorcode.ErrMonitorEndTimeBeforeStartTime
	}
	// 检查时间跨度是否超过最大允许小时数
	if maxDurationHours != nil {
		actualDuration := endTime.Sub(startTime).Hours()
		if actualDuration > float64(*maxDurationHours) {
			return errorcode.ErrMonitorTimeDuration.WithArgs(productName, *maxDurationHours)
		}
	}
	return nil
}

// 判断是否已经授权
func isAuthorized(ctx context.Context, userID, ticketID string, ins []string) error {

	//授权需要提供ticketID
	if ticketID == "" {
		return errorcode.ErrTicketIDIsEmpty
	}
	//判断userID是否是工单协作人
	isCollaborator := false
	ticketInfo, err := ticket_openapi.GetTicketSensitiveInfo(&ticket_openapi.GetTicketSensitiveInfoRequest{
		TicketID: ticketID,
	})
	if err != nil {
		return err
	}
	for _, coll := range ticketInfo.Result.Data.CurrentFlowContent.CollaboratorList {
		if userID == coll {
			isCollaborator = true
		}
	}
	if !isCollaborator {
		return errorcode.ErrUserNotCollaborator
	}
	//查询数据库，是否该工单包含了instanceID的申请
	diagnoseAuth, err := dal.FindLastDiagnoseAuthorizationByTicketID(ctx, ticketID)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return err
	}
	if errors.Is(err, gorm.ErrRecordNotFound) || diagnoseAuth == nil {
		return errorcode.ErrUserNotAuthorized
	}
	authIns, err := TransferStringToArrayString(diagnoseAuth.InstanceIds)
	if err != nil {
		return err
	}
	unAuthIns := make([]string, 0)
	for _, in := range ins {
		if utils.ContainsString(authIns, in) {
			continue
		}
		unAuthIns = append(unAuthIns, in)
	}
	if len(unAuthIns) > 0 {
		return errorcode.ErrInstanceNotAuthorized.WithArgs(unAuthIns)
	}

	//todo：判断角色是否已经被删除

	//查询官网，该工单申请是否已经通过
	diagnoseAuthInfo, err := official_auth_api.GetDiagnosticAuthorization(&official_auth_api.GetDiagnosticAuthorizationRequest{
		Email:           userID,
		AuthorizationID: fmt.Sprintf("%d", lo.FromPtr(diagnoseAuth.AuthorizationID)),
	})
	if err != nil {
		return err
	}
	//1. 1 ：待审批2. 2 ：生效中3. 3 ：拒绝4. 4 ：结束5. 5 ：过期
	switch diagnoseAuthInfo.Result.Data.Status {
	case 1:
		return errorcode.ErrUserAuthPending
	case 2:
		return nil
	case 3:
		return errorcode.ErrUserAuthReject
	case 4:
		return errorcode.ErrUserAuthEnd
	case 5:
		return errorcode.ErrUserAuthExpire
	default:
		return errorcode.ErrUserNotAuthorized
	}
}

// 获取需要授权的实例
func needAuthIns(ctx context.Context, diagnoseItemStatus map[int64]*diagnose_task_run.DiagnoseItemStatus) ([]string, error) {
	authIns := make([]string, 0)
	for itemID, itemStatus := range diagnoseItemStatus {
		//通过ID查询诊断项
		diagnoseItem, err := dal.DiagnoseItemFindByID(ctx, itemID)
		if err != nil {
			return nil, err
		}
		if diagnoseItem.NeedAuth {
			for _, resource := range itemStatus.DiagnoseResources {
				authIns = append(authIns, resource.Instances...)
			}
		}
	}
	return authIns, nil
}

func getDiagnoseItemStatus(ctx context.Context, diagnoseTask *model.DiagnoseTaskV2, mTaskRun *model.DiagnoseTaskRun,
	DiagnoseResources []*diagnose_result.DiagnoseResource) (map[int64]*diagnose_task_run.DiagnoseItemStatus,
	map[int64]*model.DiagnoseItem, []*model.ItemTemplate, error) {
	// 不同的诊断类型，获取不同的诊断项列表
	diagnoseItemIDs := make([]int64, 0)
	relations := make([]*model.ItemTemplate, 0)
	if *diagnoseTask.DiagnoseType == diagnosetask.DiagnoseTypeItem {
		ItemIDs, err := TransferStringToArrayInt(diagnoseTask.DiagnoseItemIds)
		if err != nil {
			return nil, nil, nil, err
		}
		diagnoseItemIDs = ItemIDs
	} else if *diagnoseTask.DiagnoseType == diagnosetask.DiagnoseTypeTemplate {
		rels, err := dal.FindRelationByTemplateID(ctx, *diagnoseTask.DiagnoseTemplateID)
		if err != nil {
			return nil, nil, nil, err
		}
		relations = rels
		for _, relation := range relations {
			diagnoseItemIDs = append(diagnoseItemIDs, relation.DiagnoseItemID)
		}
		//查询StateMachineInfo
		tem, err := dal.DiagnoseTemplateFindByID(ctx, *diagnoseTask.DiagnoseTemplateID)
		if err != nil {
			return nil, nil, nil, err
		}
		mTaskRun.StateMachineInfo = tem.StateMachineInfo
		mTaskRun.DiagnoseTemplateName = tem.DiagnoseTemplateName
	}

	//判断诊断项ID是否存在
	diagnoseItemMap := make(map[int64]*model.DiagnoseItem)
	for _, diagnoseItemID := range diagnoseItemIDs {
		diagnoseItem, err := dal.DiagnoseItemFindByID(ctx, diagnoseItemID)
		if err != nil {
			return nil, nil, nil, err
		}
		diagnoseItemMap[diagnoseItemID] = diagnoseItem
	}
	//添加DiagnoseItemStatus
	// 定义 diagnoseItemStatus 为 map 类型
	diagnoseItemStatus := make(map[int64]*diagnose_task_run.DiagnoseItemStatus)

	var err error
	for diagnoseItemID, diagnoseItem := range diagnoseItemMap {
		runDiagnoseItem := &diagnose_task_v2.DiagnoseItem{
			Id:   diagnoseItem.DiagnoseItemID,
			Name: diagnoseItem.DiagnoseItemName,
		}
		//获取诊断项的产品、子产品、资源类型
		runDiagnoseItem.ProductCategory, err = GetProductCategoryByID(ctx, diagnoseItem.ProductCategoryID)
		if err != nil {
			return nil, nil, nil, err
		}
		//根据诊断项过滤资源
		itemResource := FilterDiagnoseResourceByDiagnoseItems(DiagnoseResources, []*diagnose_task_v2.DiagnoseItem{runDiagnoseItem})
		diagnoseItemStatus[diagnoseItemID] = &diagnose_task_run.DiagnoseItemStatus{
			DiagnoseItem:      runDiagnoseItem,
			Status:            DiagnoseItemStatusWaiting,
			DiagnoseResources: itemResource,
		}
	}
	return diagnoseItemStatus, diagnoseItemMap, relations, nil
}

// 执行诊断项
func executeDiagnoseItem(ctx context.Context, logger *utils.ModuleLoggerWithFunc, diagnoseTask *model.DiagnoseTaskV2, diagnoseItemStatus map[int64]*diagnose_task_run.DiagnoseItemStatus,
	req *diagnose_task_run.RunDiagnoseTaskReq, mTaskRun *model.DiagnoseTaskRun, diagnoseItemMap map[int64]*model.DiagnoseItem, relations []*model.ItemTemplate) error {
	switch *diagnoseTask.DiagnoseType {
	case diagnosetask.DiagnoseTypeItem: //原子诊断
		//遍历diagnoseItemStatus，获取诊断项ID，和每个诊断项对应的资源列表和每个诊断项的asl语言，以诊断项的维度发起诊断
		for itemId, itemStatus := range diagnoseItemStatus {
			resources := make(map[int64][]*diagnose_result.DiagnoseResource)
			resources[itemId] = itemStatus.DiagnoseResources
			diagnoseInput := &statemachines.DiagnoseRequest{
				AccountID:         req.AccountID,
				Resources:         resources,
				DiagnoseItemIDs:   []int64{itemId},
				DiagnoseTaskRunID: mTaskRun.ID,
				DiagnoseStartTime: req.DiagnoseStartTime,
				DiagnoseEndTime:   req.DiagnoseEndTime,
			}
			if diagnoseTask.TicketID != nil {
				diagnoseInput.TicketID = *diagnoseTask.TicketID
			}
			detail, err := diagnose.TriggerDiagnoseTask(ctx, diagnoseItemMap[itemId].StateMachineInfo, diagnoseInput, nil)
			if err != nil {
				logger.CtxError(ctx, "TriggerDiagnoseTask err:%v", err)
				return err
			}
			logger.CtxInfo(ctx, "[CreateDiagnoseTask] diagnose.TriggerDiagnoseTask success, response:%v", detail)
		}
	case diagnosetask.DiagnoseTypeTemplate: //编排诊断
		//获取诊断模版信息
		templateInfo, err := dal.DiagnoseTemplateFindByID(ctx, *diagnoseTask.DiagnoseTemplateID)
		if err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				logger.CtxError(ctx, "Diagnose template not found for ID: %d", *diagnoseTask.DiagnoseTemplateID)
				return err
			}
			logger.CtxError(ctx, "DiagnoseTemplateFindByID err:%v", err)
			return err
		}
		if templateInfo.StateMachineInfo == nil {
			logger.CtxError(ctx, "StateMachineInfo is nil")
			return fmt.Errorf("StateMachineInfo is nil")
		}
		resources := make(map[int64][]*diagnose_result.DiagnoseResource)
		for itemId, itemStatus := range diagnoseItemStatus {
			resources[itemId] = itemStatus.DiagnoseResources
		}
		diagnoseItemIDs := make([]int64, 0)
		for _, relation := range relations {
			diagnoseItemIDs = append(diagnoseItemIDs, relation.DiagnoseItemID)
		}

		diagnoseInput := &statemachines.DiagnoseRequest{
			AccountID:         req.AccountID,
			Resources:         resources,
			DiagnoseItemIDs:   diagnoseItemIDs,
			DiagnoseTaskRunID: mTaskRun.ID,
			DiagnoseStartTime: req.DiagnoseStartTime,
			DiagnoseEndTime:   req.DiagnoseEndTime,
		}
		if diagnoseTask.TicketID != nil {
			diagnoseInput.TicketID = *diagnoseTask.TicketID
		}
		detail, err := diagnose.TriggerDiagnoseTask(ctx, *templateInfo.StateMachineInfo, diagnoseInput, nil)
		if err != nil {
			logger.CtxError(ctx, "TriggerDiagnoseTask err:%v", err)
			return err
		}
		logger.CtxInfo(ctx, "[CreateDiagnoseTask] diagnose.TriggerDiagnoseTask success, response:%v", detail)
	}
	return nil
}
