package diagnose_task_run

import (
	"code.byted.org/volcengine-support/cloud-sherlock/biz/errorcode"
	diagnosetask "code.byted.org/volcengine-support/cloud-sherlock/biz/service/diagnose_task_v2"
	"code.byted.org/volcengine-support/cloud-sherlock/gen/hertz/diagnose_result"
	"code.byted.org/volcengine-support/cloud-sherlock/gen/hertz/diagnose_task_v2"
	"code.byted.org/volcengine-support/cloud-sherlock/gen/hertz/product_category"
	"context"
	"errors"
	"fmt"
	"sync"
	"time"

	"code.byted.org/volcengine-support/cloud-sherlock/biz/dal"
	"code.byted.org/volcengine-support/cloud-sherlock/gen/gorm/model"
	"code.byted.org/volcengine-support/cloud-sherlock/gen/hertz/common"
	"code.byted.org/volcengine-support/cloud-sherlock/gen/hertz/diagnose_task_run"
	"code.byted.org/volcengine-support/cloud-sherlock/utils"
	"github.com/samber/lo"
	"gorm.io/gorm"
)

var (
	service *Service
	once    sync.Once
)

const (
	defaultPageSize = int32(10)
	defaultPageNum  = int32(1)
)

type Service struct {
	logger *utils.ModuleLogger
}

func GetDiagnoseTaskRunService() *Service {
	once.Do(func() {
		service = &Service{
			logger: utils.NewModuleLogger("GetDiagnoseTaskRunService"),
		}
	})
	return service
}

func (s *Service) QueryDiagnoseTaskRunList(ctx context.Context,
	req *diagnose_task_run.QueryDiagnoseTaskRunListReq) (*diagnose_task_run.QueryDiagnoseTaskRunListResult, error) {
	logger := s.logger.WithFunc("QueryDiagnoseTaskRunList")
	if req.PageNumber == nil {
		req.PageNumber = lo.ToPtr(defaultPageNum)
	}
	if req.PageSize == nil {
		req.PageSize = lo.ToPtr(defaultPageSize)
	}
	mTaskRun := &model.DiagnoseTaskRun{
		Status:   req.Status,
		TicketID: req.TicketID,
		Name:     req.DiagnoseTaskRunName,
	}
	if req.Origin != nil {
		mTaskRun.Origin = *req.Origin
	}
	if req.DiagnoseTaskID != nil {
		mTaskRun.DiagnoseTaskID = *req.DiagnoseTaskID
	}
	if req.StartTime != nil {
		startTime, err := utils.StringToTime(*req.StartTime, utils.DateTime, "")
		if err != nil {
			logger.CtxError(ctx, "StringToTime err:%v", err)
			return nil, err
		}
		mTaskRun.StartTime = &startTime
	}
	if req.EndTime != nil {
		endTime, err := utils.StringToTime(*req.EndTime, utils.DateTime, "")
		if err != nil {
			logger.CtxError(ctx, "StringToTime err:%v", err)
			return nil, err
		}
		mTaskRun.EndTime = &endTime
	}
	asc := false
	if req.AscSortByEndTime != nil {
		asc = *req.AscSortByEndTime
	}
	// 调用数据访问层方法时传入模糊查询的名称
	diagnoseTaskRuns, total, err := dal.QueryDiagnoseTaskRunList(ctx, mTaskRun, req.DiagnoseTemplateIDs,
		req.DiagnoseTaskRunIDs, req.ProductCategoryIDs, req.DiagnoseResultLevels, asc, int(*req.PageNumber), int(*req.PageSize))
	if err != nil {
		logger.CtxError(ctx, "QueryDiagnoseTaskRunList err:%v", err)
		return nil, err
	}
	diagnoseTaskRunList := make([]*diagnose_task_run.DiagnoseTaskRun, 0)
	for _, taskRun := range diagnoseTaskRuns {
		diagnoseTaskRun := &diagnose_task_run.DiagnoseTaskRun{
			Id:                   taskRun.ID,
			DiagnoseTaskID:       taskRun.DiagnoseTaskID,
			Name:                 taskRun.Name,
			Status:               taskRun.Status,
			RunUserID:            taskRun.CreateUserID,
			DiagnoseType:         taskRun.DiagnoseType,
			DiagnoseStartTime:    taskRun.DiagnoseStartTime,
			DiagnoseEndTime:      taskRun.DiagnoseEndTime,
			DiagnoseTemplateName: taskRun.DiagnoseTemplateName,
			Origin:               taskRun.Origin,
			TicketID:             taskRun.TicketID,
		}
		//查询产品类型（通过资源查询）
		productCategories := make([]*product_category.ProductCategory, 0)
		resources, err := TransferStringToDiagnoseResource(taskRun.Resources)
		if err != nil {
			logger.CtxError(ctx, "TransferStringToDiagnoseResource err:%v", err)
			return nil, err
		}
		for _, resource := range resources {
			productCategory, err := GetProductCategoryByID(ctx, resource.ProductCategoryID)
			if err != nil {
				logger.CtxError(ctx, "GetProductCategoryByID err:%v", err)
				return nil, err
			}
			productCategories = append(productCategories, productCategory)
		}
		diagnoseTaskRun.ProductCategory = productCategories
		if taskRun.StartTime != nil {
			startTime, err := utils.TimeToString(*taskRun.StartTime, "")
			if err != nil {
				logger.CtxError(ctx, "TimeToString err:%v", err)
				return nil, err
			}
			diagnoseTaskRun.StartTime = lo.ToPtr(startTime)
		}
		if taskRun.EndTime != nil {
			endTime, err := utils.TimeToString(*taskRun.EndTime, "")
			if err != nil {
				logger.CtxError(ctx, "TimeToString err:%v", err)
				return nil, err
			}
			diagnoseTaskRun.EndTime = lo.ToPtr(endTime)
		}
		if taskRun.FeedbackID != nil {
			diagnoseTaskRun.Feedback = lo.ToPtr(true)
		} else {
			diagnoseTaskRun.Feedback = lo.ToPtr(false)
		}
		//计算level
		level, err := GetLevelByTaskRunID(ctx, taskRun.ID)
		if err != nil {
			logger.CtxError(ctx, "GetDiagnoseTaskRunLevel err:%v", err)
			return nil, err
		}
		diagnoseTaskRun.DiagnoseResultLevel = level
		diagnoseTaskRunList = append(diagnoseTaskRunList, diagnoseTaskRun)
	}

	return &diagnose_task_run.QueryDiagnoseTaskRunListResult{
		DiagnoseTaskRunList: diagnoseTaskRunList,
		Pagination: &common.Pagination{
			PageNumber: *req.PageNumber,
			PageSize:   *req.PageSize,
			TotalCount: total,
		},
	}, nil
}

// DeleteDiagnoseTaskRun 根据诊断任务运行 ID 删除对应的诊断任务运行记录
func (s *Service) DeleteDiagnoseTaskRun(ctx context.Context, req *diagnose_task_run.DeleteDiagnoseTaskRunReq) error {
	logger := s.logger.WithFunc("DeleteDiagnoseTaskRun")

	// 先查询任务的状态
	taskRun, err := dal.QueryDiagnoseTaskRunByID(ctx, req.Id)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			// 如果记录没找到，直接返回
			logger.CtxInfo(ctx, "Diagnose task run record not found for ID: %d", req.Id)
			return nil
		}
		logger.CtxError(ctx, "QueryDiagnoseTaskRunByID err:%v", err)
		return err
	}

	// 检查任务状态是否为 finished
	if taskRun.Status == nil || *taskRun.Status != DiagnoseTaskStatusFinish {
		logger.CtxError(ctx, "Cannot delete task run with status %v, only finished tasks can be deleted", taskRun.Status)
		return fmt.Errorf("cannot delete task run with status %v, only finished tasks can be deleted", taskRun.Status)
	}

	// 调用数据访问层的删除方法
	err = dal.DeleteDiagnoseTaskRunByID(ctx, req.Id)
	if err != nil {
		logger.CtxError(ctx, "DeleteDiagnoseTaskRunByID err:%v", err)
		return err
	}

	return nil
}

// RunDiagnoseTask 运行诊断任务
func (s *Service) RunDiagnoseTask(ctx context.Context,
	req *diagnose_task_run.RunDiagnoseTaskReq) (*diagnose_task_run.RunDiagnoseTaskResult, error) {
	logger := s.logger.WithFunc("RunDiagnoseTask")
	// 检查诊断任务是否存在
	diagnoseTask, err := dal.DiagnoseTaskV2FindByID(ctx, req.DiagnoseTaskID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			logger.CtxError(ctx, "Diagnose task not found for ID: %d", req.DiagnoseTaskID)
			return nil, err
		}
		logger.CtxError(ctx, "DiagnoseTaskV2FindByID err: %v", err)
		return nil, err
	}
	//判断监控时间是否符合限制
	productCategoryIDs, err := TransferStringToArrayInt(diagnoseTask.ProductCategoryIds)
	if err != nil {
		logger.CtxError(ctx, "TransferStringToArrayInt err:%v", err)
		return nil, err
	}
	for _, productCategoryID := range productCategoryIDs {
		err = MonitorTimeIsValid(ctx, req.DiagnoseStartTime, req.DiagnoseEndTime, productCategoryID)
		if err != nil {
			logger.CtxError(ctx, "MonitorTimeIsValid err:%v", err)
			return nil, err
		}
	}

	// 创建诊断任务运行记录
	startTime := time.Now()
	mTaskRun := &model.DiagnoseTaskRun{
		Name:               req.Name,
		DiagnoseTaskID:     req.DiagnoseTaskID,
		AccountID:          req.AccountID,
		CreateUserID:       lo.ToPtr(req.RunUserID),
		StartTime:          &startTime,
		Status:             lo.ToPtr(DiagnoseTaskStatusWaiting),
		DiagnoseStartTime:  lo.ToPtr(req.DiagnoseStartTime),
		DiagnoseEndTime:    lo.ToPtr(req.DiagnoseEndTime),
		TicketID:           diagnoseTask.TicketID,
		DiagnoseType:       diagnoseTask.DiagnoseType,
		DiagnoseTemplateID: diagnoseTask.DiagnoseTemplateID,
	}
	if diagnoseTask.Origin != nil {
		mTaskRun.Origin = *diagnoseTask.Origin
	} else {
		mTaskRun.Origin = diagnosetask.DiagnoseTaskOriginPlatform
	}
	if mTaskRun.Name == nil {
		mTaskRun.Name = diagnoseTask.Name
	}
	diagnoseResources, err := TransferDiagnoseResourceToString(req.DiagnoseResources)
	if err != nil {
		logger.CtxError(ctx, "TransferStringToDiagnoseResource err:%v", err)
		return nil, err
	}
	mTaskRun.Resources = lo.ToPtr(diagnoseResources)
	//保存诊断项运行流程快照

	// 不同的诊断类型，获取不同的诊断项列表
	diagnoseItemStatus, diagnoseItemMap, relations, err := getDiagnoseItemStatus(ctx, diagnoseTask, mTaskRun, req.DiagnoseResources)
	if err != nil {
		logger.CtxError(ctx, "getDiagnoseItemStatus err:%v", err)
		return nil, err
	}
	diagnoseItemStatusStr, err := TransferDiagnoseItemStatusToString(diagnoseItemStatus)
	if err != nil {
		logger.CtxError(ctx, "TransferDiagnoseItemStatusToString err:%v", err)
		return nil, err
	}
	mTaskRun.DiagnoseItemStatus = lo.ToPtr(diagnoseItemStatusStr)

	//判断是否需要授权
	authIns, err := needAuthIns(ctx, diagnoseItemStatus)
	if err != nil {
		logger.CtxError(ctx, "NeedAuthIns err:%v", err)
		return nil, err
	}
	if len(authIns) > 0 {
		err = isAuthorized(ctx, req.RunUserID, lo.FromPtr(diagnoseTask.TicketID), authIns)
		if err != nil {
			logger.CtxError(ctx, "IsAuthorized err:%v", err)
			return nil, err
		}
	}

	// 保存诊断任务运行记录
	err = dal.CreateDiagnoseTaskRun(ctx, mTaskRun)
	if err != nil {
		logger.CtxError(ctx, "CreateDiagnoseTaskRun err: %v", err)
		return nil, err
	}

	//运行诊断任务
	err = executeDiagnoseItem(ctx, logger, diagnoseTask, diagnoseItemStatus, req, mTaskRun, diagnoseItemMap, relations)
	if err != nil {
		logger.CtxError(ctx, "executeDiagnoseItem err:%v", err)
		return nil, err
	}

	//更新诊断任务lastRunTime
	err = dal.UpdateDiagnoseTaskV2LastRunTimeByID(ctx, diagnoseTask.ID, lo.ToPtr(startTime))
	if err != nil {
		logger.CtxError(ctx, "UpdateDiagnoseTaskV2LastRunTimeByID err:%v", err)
		return nil, err
	}
	return &diagnose_task_run.RunDiagnoseTaskResult{
		DiagnoseTaskRunID: mTaskRun.ID,
	}, nil
}

// QueryDiagnoseTaskRunDetail 根据诊断任务运行 ID 查询执行结果详情
func (s *Service) QueryDiagnoseTaskRunDetail(ctx context.Context,
	req *diagnose_task_run.QueryDiagnoseTaskRunDetailReq) (*diagnose_task_run.QueryDiagnoseTaskRunDetailResult, error) {
	logger := s.logger.WithFunc("QueryDiagnoseTaskRunDetail")

	// 根据诊断任务运行 ID 查询任务运行记录
	taskRun, err := dal.QueryDiagnoseTaskRunByID(ctx, req.DiagnoseTaskRunID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			logger.CtxInfo(ctx, "Diagnose task run record not found for ID: %d", req.DiagnoseTaskRunID)
			return nil, err
		}
		logger.CtxError(ctx, "QueryDiagnoseTaskRunByID err:%v", err)
		return nil, err
	}

	// 构建返回的诊断任务运行详情对象
	diagnoseTaskRunDetail := &diagnose_task_run.DiagnoseTaskRun{
		Id:                taskRun.ID,
		DiagnoseTaskID:    taskRun.DiagnoseTaskID,
		Name:              taskRun.Name,
		Status:            taskRun.Status,
		RunUserID:         taskRun.CreateUserID,
		DiagnoseType:      taskRun.DiagnoseType,
		TicketID:          taskRun.TicketID,
		DiagnoseStartTime: taskRun.DiagnoseStartTime,
		DiagnoseEndTime:   taskRun.DiagnoseEndTime,
		Origin:            taskRun.Origin,
	}

	// 处理开始时间
	if taskRun.StartTime != nil {
		startTime, err := utils.TimeToString(*taskRun.StartTime, "")
		if err != nil {
			logger.CtxError(ctx, "TimeToString err:%v", err)
			return nil, err
		}
		diagnoseTaskRunDetail.StartTime = lo.ToPtr(startTime)
	}

	// 处理结束时间
	if taskRun.EndTime != nil {
		endTime, err := utils.TimeToString(*taskRun.EndTime, "")
		if err != nil {
			logger.CtxError(ctx, "TimeToString err:%v", err)
			return nil, err
		}
		diagnoseTaskRunDetail.EndTime = lo.ToPtr(endTime)
	}

	// 处理反馈信息
	if taskRun.FeedbackID != nil {
		diagnoseTaskRunDetail.Feedback = lo.ToPtr(true)
	} else {
		diagnoseTaskRunDetail.Feedback = lo.ToPtr(false)
	}

	// 计算诊断结果等级
	level, err := GetLevelByTaskRunID(ctx, taskRun.ID)
	if err != nil {
		logger.CtxError(ctx, "GetLevelByTaskRunID err:%v", err)
		return nil, err
	}
	diagnoseTaskRunDetail.DiagnoseResultLevel = level

	// 诊断项运行状态查询
	diagnoseItemStatusList, err := TransferStringToDiagnoseItemStatus(taskRun.DiagnoseItemStatus)
	if err != nil {
		logger.CtxError(ctx, "TransferStringToDiagnoseItemStatus err:%v", err)
		return nil, err
	}
	//计算诊断项结果等级
	for i, status := range diagnoseItemStatusList {
		itemLevel, err := GetLevelByTaskRunIDItemID(ctx, taskRun.ID, status.DiagnoseItem.Id)
		if err != nil {
			logger.CtxError(ctx, "GetLevelByTaskRunIDItemID err:%v", err)
			return nil, err
		}
		diagnoseItemStatusList[i].DiagnoseResultLevel = itemLevel
	}
	////查询activityDiagnose
	//_, err = TransferStringToActivityDiagnose(taskRun.ActivityDiagnoses)
	//if err != nil {
	//	logger.CtxError(ctx, "TransferStringToActivityDiagnose err:%v", err)
	//	return nil, err
	//}
	return &diagnose_task_run.QueryDiagnoseTaskRunDetailResult{
		Information: diagnoseTaskRunDetail,
		DiagnoseTaskRunByteFlow: &diagnose_task_run.DiagnoseTaskRunByteFlow{
			StateMachineInfo:       taskRun.StateMachineInfo,
			DiagnoseItemStatusList: diagnoseItemStatusList,
		},
	}, nil
}

// QueryDiagnoseTaskRunItemDetail 查询执行结果某个诊断项详情
func (s *Service) QueryDiagnoseTaskRunItemDetail(ctx context.Context,
	req *diagnose_task_run.QueryDiagnoseTaskRunItemDetailReq) (*diagnose_task_run.DiagnoseTaskRunItemDetailResult, error) {
	logger := s.logger.WithFunc("QueryDiagnoseTaskRunItemDetail")

	if req.PageNumber == nil {
		req.PageNumber = lo.ToPtr(defaultPageNum)
	}
	if req.PageSize == nil {
		req.PageSize = lo.ToPtr(defaultPageSize)
	}
	// 计算诊断项结果等级
	itemGroupByLevel, err := GetLevelByTaskRunIDItemIDInstanceIDsLevels(ctx, req.DiagnoseTaskRunID, req.DiagnoseItemID, req.InstanceID, req.DiagnoseResultLevel)
	if err != nil {
		logger.CtxError(ctx, "GetLevelByTaskRunIDItemIDInstanceIDsLevels err:%v", err)
		return nil, err
	}
	diagnoseResults, total, err := dal.DiagnoseResultsFindByConSortByLevel(ctx, req.DiagnoseTaskRunID, req.DiagnoseItemID,
		req.InstanceID, req.DiagnoseResultLevel, int(*req.PageNumber), int(*req.PageSize))
	if err != nil {
		logger.CtxError(ctx, "DiagnoseResultsFindByConSortByLevel err:%v", err)
		return nil, err
	}
	diagnoseResultList := make([]*diagnose_result.DiagnoseResult, 0)
	for _, diagnoseResult := range diagnoseResults {
		diagnoseResultList = append(diagnoseResultList, &diagnose_result.DiagnoseResult{
			InstanceID:          diagnoseResult.InstanceID,
			InstanceName:        diagnoseResult.InstanceName,
			Region:              diagnoseResult.Region,
			Message:             diagnoseResult.DiagnoseMessage,
			DiagnoseResultLevel: diagnoseResult.DiagnoseResultLevel,
			Suggestion:          diagnoseResult.DiagnoseSuggestion,
			Operate:             diagnoseResult.DiagnoseOperate,
		})
	}
	//查询diagnoseItem
	diagnoseItem, err := dal.DiagnoseItemFindByID(ctx, req.DiagnoseItemID)
	if err != nil {
		logger.CtxError(ctx, "DiagnoseItemFindByID err:%v", err)
		return nil, err
	}
	//查询productCategory
	productCategory, err := GetProductCategoryByID(ctx, diagnoseItem.ProductCategoryID)
	if err != nil {
		logger.CtxError(ctx, "GetProductCategoryByID err:%v", err)
		return nil, err
	}
	return &diagnose_task_run.DiagnoseTaskRunItemDetailResult{
		DiagnoseItem: &diagnose_task_v2.DiagnoseItem{
			Id:              diagnoseItem.DiagnoseItemID,
			Name:            diagnoseItem.DiagnoseItemName,
			ProductCategory: productCategory,
		},
		DiagnoseResultLevel: itemGroupByLevel,
		DiagnoseResults:     diagnoseResultList,
		Pagination: &common.Pagination{
			PageNumber: *req.PageNumber,
			PageSize:   *req.PageSize,
			TotalCount: total,
		},
	}, nil
}

func (s *Service) QueryDiagnoseTaskRunItems(ctx context.Context,
	req *diagnose_task_run.QueryDiagnoseTaskRunItemsReq) (*diagnose_task_run.QueryDiagnoseTaskRunItemsResult, error) {
	logger := s.logger.WithFunc("QueryDiagnoseTaskRunItems")
	//查询诊断任务运行详情
	diagnoseTaskRun, err := dal.QueryDiagnoseTaskRunByID(ctx, req.DiagnoseTaskRunID)
	if err != nil {
		logger.CtxError(ctx, "QueryDiagnoseTaskRunByID err:%v", err)
		return nil, err
	}
	diagnoseItemStatusList, err := TransferStringToDiagnoseItemStatus(diagnoseTaskRun.DiagnoseItemStatus)
	if err != nil {
		logger.CtxError(ctx, "TransferStringToDiagnoseItemStatus err:%v", err)
		return nil, err
	}
	items := make([]*diagnose_task_v2.DiagnoseItem, 0)
	for _, item := range diagnoseItemStatusList {
		if len(req.ProductCategoryIDs) > 0 && !lo.Contains(req.ProductCategoryIDs, item.DiagnoseItem.ProductCategory.Id) {
			continue
		}
		items = append(items, item.DiagnoseItem)
	}
	return &diagnose_task_run.QueryDiagnoseTaskRunItemsResult{
		DiagnoseItems: items,
	}, nil
}

func (s *Service) QueryDiagnoseTaskRunResource(ctx context.Context,
	req *diagnose_task_run.QueryDiagnoseTaskRunResourceReq) (*diagnose_task_run.QueryDiagnoseTaskRunResourceResult, error) {
	logger := s.logger.WithFunc("QueryDiagnoseTaskRunResource")
	//查询诊断任务运行详情
	diagnoseTaskRun, err := dal.QueryDiagnoseTaskRunByID(ctx, req.DiagnoseTaskRunID)
	if err != nil {
		logger.CtxError(ctx, "QueryDiagnoseTaskRunByID err:%v", err)
		return nil, err
	}
	resources, err := TransferStringToDiagnoseResource(diagnoseTaskRun.Resources)
	if err != nil {
		logger.CtxError(ctx, "TransferStringToDiagnoseResource err:%v", err)
		return nil, err
	}
	//过滤product和subProduct
	filteredResources := make([]*diagnose_result.DiagnoseResource, 0)
	for _, resource := range resources {
		if len(req.ProductCategoryIDs) > 0 && !lo.Contains(req.ProductCategoryIDs, resource.ProductCategoryID) {
			continue
		}
		filteredResources = append(filteredResources, resource)
	}
	// 过滤诊断项对应的资源
	if len(req.DiagnoseItemIDs) > 0 {
		diagnoseItems, err := dal.DiagnoseItemFindByIDs(ctx, req.DiagnoseItemIDs)
		if err != nil {
			logger.CtxError(ctx, "DiagnoseItemFindByIDs err:%v", err)
			return nil, err
		}
		items := make([]*diagnose_task_v2.DiagnoseItem, 0)
		for _, item := range diagnoseItems {
			//查询productCategory
			productCategory, err := GetProductCategoryByID(ctx, item.ProductCategoryID)
			if err != nil {
				logger.CtxError(ctx, "GetProductCategoryByID err:%v", err)
				return nil, err
			}
			items = append(items, &diagnose_task_v2.DiagnoseItem{
				Id:              item.DiagnoseItemID,
				Name:            item.DiagnoseItemName,
				ProductCategory: productCategory,
			})
		}
		filteredResources = FilterDiagnoseResourceByDiagnoseItems(filteredResources, items)
	}
	return &diagnose_task_run.QueryDiagnoseTaskRunResourceResult{
		DiagnoseResources: filteredResources,
	}, nil
}

func (s *Service) QueryDiagnoseTaskRunCategory(ctx context.Context,
	req *diagnose_task_run.QueryDiagnoseTaskRunCategoryReq) (*diagnose_task_run.QueryDiagnoseTaskRunCategoryResult, error) {
	logger := s.logger.WithFunc("QueryDiagnoseTaskRunCategory")
	diagnoseResults, err := dal.DiagnoseResultsFindByDiagnoseTaskRunID(ctx, req.DiagnoseTaskRunID)
	if err != nil {
		logger.CtxError(ctx, "DiagnoseResultsFindByDiagnoseTaskRunID err:%v", err)
		return nil, err
	}
	diagnoseCategoryList := make([]*diagnose_result.DiagnoseTaskCategory, 0)
	productCategoryAbnormalMap := make(map[int64]int32)
	idProductCategoryMap := make(map[int64]*product_category.ProductCategory)
	itemProductCategoryMap := make(map[int64]*product_category.ProductCategory)
	for _, result := range diagnoseResults {
		prc := &product_category.ProductCategory{}
		if _, ok := itemProductCategoryMap[result.DiagnoseItemID]; ok {
			prc = itemProductCategoryMap[result.DiagnoseItemID]
		} else {
			diagnoseItem, err := dal.DiagnoseItemFindByID(ctx, result.DiagnoseItemID)
			if err != nil {
				logger.CtxError(ctx, "FindDiagnoseItemByID err:%v", err)
				return nil, err
			}
			prc, err = GetProductCategoryByID(ctx, diagnoseItem.ProductCategoryID)
			if err != nil {
				logger.CtxError(ctx, "GetProductCategoryByID err:%v", err)
				return nil, err
			}
			itemProductCategoryMap[result.DiagnoseItemID] = prc
		}
		idProductCategoryMap[prc.Id] = prc
		if _, ok := productCategoryAbnormalMap[prc.Id]; !ok {
			productCategoryAbnormalMap[prc.Id] = 0
		}
		if result.DiagnoseResultLevel != DiagnoseResultLevelInfo {
			productCategoryAbnormalMap[prc.Id]++
		}
	}
	for id, abnormalNum := range productCategoryAbnormalMap {
		diagnoseCategoryList = append(diagnoseCategoryList, &diagnose_result.DiagnoseTaskCategory{
			ProductCategory: idProductCategoryMap[id],
			AbnormalNum:     abnormalNum,
		})
	}
	return &diagnose_task_run.QueryDiagnoseTaskRunCategoryResult{
		DiagnoseTaskRunCategoryList: diagnoseCategoryList,
	}, nil
}

func (s *Service) QueryDiagnoseTaskRunSummary(ctx context.Context,
	req *diagnose_task_run.QueryDiagnoseTaskRunSummaryReq) (*diagnose_task_run.QueryDiagnoseTaskRunSummaryResult, error) {
	logger := s.logger.WithFunc("QueryDiagnoseTaskRunSummary")
	//查询诊断任务运行详情
	diagnoseTaskRun, err := dal.QueryDiagnoseTaskRunByID(ctx, req.DiagnoseTaskRunID)
	if err != nil {
		logger.CtxError(ctx, "QueryDiagnoseTaskRunByID err:%v", err)
		return nil, err
	}

	result := &diagnose_task_run.QueryDiagnoseTaskRunSummaryResult{
		DiagnoseResultLevel:  nil,
		DiagnoseTemplateName: diagnoseTaskRun.DiagnoseTemplateName,
		CreateUserID:         diagnoseTaskRun.CreateUserID,
		CreateUserName:       diagnoseTaskRun.CreateUserName,
		Progress:             100,
		Feedback:             false,
	}
	if diagnoseTaskRun.StartTime != nil {
		startTime, err := utils.TimeToString(*diagnoseTaskRun.StartTime, "")
		if err != nil {
			logger.CtxError(ctx, "TimeToString err:%v", err)
			return nil, err
		}
		result.StartTime = lo.ToPtr(startTime)
	}
	if diagnoseTaskRun.EndTime != nil {
		endTime, err := utils.TimeToString(*diagnoseTaskRun.EndTime, "")
		if err != nil {
			logger.CtxError(ctx, "TimeToString err:%v", err)
			return nil, err
		}
		result.EndTime = lo.ToPtr(endTime)
	}
	if diagnoseTaskRun.DiagnoseStartTime != nil {
		result.DiagnoseStartTime = *diagnoseTaskRun.DiagnoseStartTime
	}
	if diagnoseTaskRun.DiagnoseEndTime != nil {
		result.DiagnoseEndTime = *diagnoseTaskRun.DiagnoseEndTime
	}
	if diagnoseTaskRun.FeedbackID != nil {
		result.Feedback = true
	}

	//计算进度
	if diagnoseTaskRun.Status != nil {
		switch *diagnoseTaskRun.Status {
		case DiagnoseTaskStatusFinish:
			result.Progress = 100
		case DiagnoseTaskStatusWaiting:
			result.Progress = 0
		default:
			diagnoseItemStatusList, err := TransferStringToDiagnoseItemStatus(diagnoseTaskRun.DiagnoseItemStatus)
			if err != nil {
				logger.CtxError(ctx, "TransferStringToDiagnoseItemStatus err:%v", err)
				return nil, err
			}
			total := 0
			finished := 0
			for _, diagnoseItemStatus := range diagnoseItemStatusList {
				if diagnoseItemStatus.Status != DiagnoseItemStatusWaiting && diagnoseItemStatus.Status != DiagnoseItemStatusRunning {
					finished++
				}
				total++
			}
			result.Progress = int32(float64(finished) / float64(total) * 100)
		}
	}

	//计算level
	groupLevel, err := GetLevelByTaskRunID(ctx, req.DiagnoseTaskRunID)
	if err != nil {
		logger.CtxError(ctx, "GetLevelByTaskRunID err:%v", err)
		return nil, err
	}
	result.DiagnoseResultLevel = groupLevel

	//计算productCategoryLevel
	//对productCategory进行分组
	productCategoryIdList, err := dal.DiagnoseResultsGroupByProductCategoryIDByDiagnoseTaskRunID(ctx, req.DiagnoseTaskRunID)
	if err != nil {
		logger.CtxError(ctx, "DiagnoseResultsGroupByProductCategoryIDByDiagnoseTaskRunID err:%v", err)
		return nil, err
	}
	for _, categoryIDGroupBy := range productCategoryIdList {
		productGroupLevel, err := GetLevelByTaskRunIDProductCategoryID(ctx, req.DiagnoseTaskRunID, categoryIDGroupBy.ProductCategoryID)
		if err != nil {
			logger.CtxError(ctx, "GetLevelByTaskRunIDProductCategoryID err:%v", err)
			return nil, err
		}
		//查询productCategory
		productCategory, err := GetProductCategoryByID(ctx, categoryIDGroupBy.ProductCategoryID)
		if err != nil {
			logger.CtxError(ctx, "GetProductCategoryByID err:%v", err)
			return nil, err
		}
		result.ProductCategoryLevels = append(result.ProductCategoryLevels, &diagnose_task_run.ProductCategoryLevel{
			ProductCategory:     productCategory,
			DiagnoseResultLevel: productGroupLevel,
		})

	}
	return result, nil
}

func (s *Service) ValidateDiagnoseTaskRun(ctx context.Context,
	req *diagnose_task_run.RunDiagnoseTaskReq) error {
	logger := s.logger.WithFunc("RunDiagnoseTask")
	// 检查诊断任务是否存在
	diagnoseTask, err := dal.DiagnoseTaskV2FindByID(ctx, req.DiagnoseTaskID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			logger.CtxError(ctx, "Diagnose task not found for ID: %d", req.DiagnoseTaskID)
			return errorcode.ErrDataNotFound
		}
		logger.CtxError(ctx, "DiagnoseTaskV2FindByID err: %v", err)
		return err
	}
	//判断监控时间是否符合限制
	productCategoryIDs, err := TransferStringToArrayInt(diagnoseTask.ProductCategoryIds)
	if err != nil {
		logger.CtxError(ctx, "TransferStringToArrayInt err:%v", err)
		return err
	}
	for _, productCategoryID := range productCategoryIDs {
		err = MonitorTimeIsValid(ctx, req.DiagnoseStartTime, req.DiagnoseEndTime, productCategoryID)
		if err != nil {
			logger.CtxError(ctx, "MonitorTimeIsValid err:%v", err)
			return err
		}
	}

	// 不同的诊断类型，获取不同的诊断项列表
	diagnoseItemStatus, _, _, err := getDiagnoseItemStatus(ctx, diagnoseTask, &model.DiagnoseTaskRun{}, req.DiagnoseResources)
	if err != nil {
		logger.CtxError(ctx, "getDiagnoseItemStatus err:%v", err)
		return err
	}

	//判断是否需要授权
	authIns, err := needAuthIns(ctx, diagnoseItemStatus)
	if err != nil {
		logger.CtxError(ctx, "NeedAuthIns err:%v", err)
		return err
	}
	if len(authIns) > 0 {
		err = isAuthorized(ctx, req.RunUserID, lo.FromPtr(diagnoseTask.TicketID), authIns)
		if err != nil {
			logger.CtxError(ctx, "IsAuthorized err:%v", err)
			return err
		}
	}
	return nil
}
