package discourse

import (
	"code.byted.org/middleware/hertz/pkg/app"
	hertzClient "code.byted.org/middleware/hertz/pkg/app/client"
	httpRetry "code.byted.org/middleware/hertz/pkg/app/client/retry"
	"code.byted.org/volcengine-support/cloud-sherlock/gen/hertz/diagnose_result"
	"code.byted.org/volcengine-support/cloud-sherlock/gen/hertz/ticket_diagnose_task"
	"code.byted.org/volcengine-support/cloud-sherlock/pkg/sso"
	"context"
	"errors"
	"fmt"
	"github.com/cloudwego/hertz/pkg/network/standard"
	"github.com/mitchellh/mapstructure"
	"github.com/samber/lo"
	"runtime"
	"time"

	"code.byted.org/gopkg/logs"
	"code.byted.org/kv/redis-v6"
	"code.byted.org/middleware/hertz/byted"
	"code.byted.org/volcengine-support/cloud-sherlock/biz/dal"
	"code.byted.org/volcengine-support/cloud-sherlock/biz/errorcode"
	discourseHertz "code.byted.org/volcengine-support/cloud-sherlock/gen/hertz/discourse"
	"code.byted.org/volcengine-support/cloud-sherlock/pkg/rds_redis"
	"code.byted.org/volcengine-support/cloud-sherlock/pkg/tcc"
	"code.byted.org/volcengine-support/cloud-sherlock/utils"
	"github.com/hertz-contrib/sse"
)

var DIAGNOSETASK int32 = 1
var AGENTASK int32 = 2

// time_enum
const (
	TTL            = 24 * time.Hour
	TICKET_TTL     = 24 * time.Hour
	DefaultTimeout = 11 * time.Minute
	Header         = 1000 * 60 * 4
)

// action_enum
const (
	CREATE = "create"
	DETAIL = "detail"
)

// sse_event
const (
	MESSAGE_START        = "message_start"
	MESSAGE_OUTPUT_START = "message_output_start"
	MESSAGE              = "message"
	MESSAGE_OUTPUT_END   = "message_output_end"
	MESSAGE_TYPE         = "message_type"
	MESSAGE_FAILED       = "message_failed"
	MESSAGE_END          = "message_end"
	MESSAGE_COMPLETION   = "message_completion"
	TEXT                 = "text"
	TICKET               = "ticket"
	REPORT               = "report"
	TASK                 = "task"
	RECOMMEND            = "recommend"
)

// tcc_key
const (
	TCC_KEY                  = "agent-api-key"
	TCC_KEY_INTENTION        = "intention"
	TCC_KEY_TICKET_INTENTION = "ticket_intention"
	TCC_KEY_DIAGNOSE         = "diagnose"
	TCC_KEY_SUMMARY          = "summary"
	TCC_KEY_FORM             = "form"
	TCC_KEY_BACKSTOP         = "backstop"
)

var hiAgentConfig *tcc.HiAgentConfig

func makeServerSSE(c *app.RequestContext) *sse.Stream {
	stream := sse.NewStream(c)
	{
		c.SetContentType("text/event-stream; charset=utf-8")
		c.Response.Header.Set("Cache-Control", "no-cache")
		c.Response.Header.Set("Connection", "keep-alive")
	}
	return stream
}

func getUserInfo(token string) (*sso.UserInfo, error) {
	return sso.GetUserInfoByTokenFromRedis(token)
}

func getSession(ctx context.Context, Userinfo *sso.UserInfo, uid string) (*discourseHertz.SessionRedisObj, error) {
	// 访问 redis
	session := &discourseHertz.SessionRedisObj{}
	err := rds_redis.GetObj[*discourseHertz.SessionRedisObj](uid, session)
	if err != nil {
		if errors.Is(err, redis.Nil) {
			if ss, err := dal.QueryLatestDiscourseSessionByUUID(ctx, Userinfo.Email, uid); err != nil {
				return nil, err
			} else {
				session.ID = ss.ID
				session.SessionID = ss.SessionID
				session.IntentionSession = ss.IntentionSession
				session.DiagnoseSession = ss.DiagnoseSession
				session.SummarySession = ss.SummarySession

				_ = rds_redis.StoreObj(uid, session, TTL)
			}
		} else {
			return nil, errorcode.RedisConnectError
		}
	}
	return session, nil
}

func sseMsg2AnswerInfo(msg *discourseHertz.SSEMessage) *discourseHertz.AnswerInfo {
	result := &discourseHertz.AnswerInfo{
		MessageID:   msg.ID,
		CreatedTime: msg.CreatedTime,
		TaskRunID:   msg.TaskRunID,
		ID:          msg.ID,
		Status:      msg.Status,
		Progress:    msg.Progress,
	}

	if msg.Answer != nil {
		result.Answer = utils.JsonToString(msg.Answer)
	}

	return result
}

// sendNormalEvent 发送普通事件到客户端
func sendNormalEvent(stream *sse.Stream, types string, message *discourseHertz.SSEMessage) {

	msgBytes := utils.JsonToString(message)

	event := &sse.Event{
		Event: types,
		Data:  []byte(msgBytes),
	}
	// 忽略发送错误，因为这已经是错误处理路径
	_ = stream.Publish(event)
}

// sendErrorEvent 发送错误事件到客户端
func sendErrorEvent(stream *sse.Stream, message string, err error) {
	logs.CtxError(context.Background(), "sendErrorEvent: %v", err)
	errMsg := message
	if err != nil {
		errMsg = fmt.Sprintf("%s: %v", message, err)
	} else {
		errMsg = fmt.Sprintf("%s", message)
	}
	errSSEMsg := &discourseHertz.SSEMessage{
		Event:       MESSAGE_FAILED,
		Answer:      errMsg,
		CreatedTime: time.Now().Unix(),
	}
	errBytes, _ := utils.JsonMarshalToBytes(errSSEMsg)
	event := &sse.Event{
		Event: TEXT,
		Data:  errBytes,
	}
	// 忽略发送错误，因为这已经是错误处理路径
	_ = stream.Publish(event)
}

// 发送报告生成时间
func sendReportLoadingEvent(stream *sse.Stream) {
	_ = stream.Publish(&sse.Event{
		Event: "report_loading",
		Data:  nil,
	})
}

// sendCompletionEvent 发送完成事件到客户端
func sendCompletionEvent(stream *sse.Stream) {
	doneMsg := &discourseHertz.SSEMessage{
		Event:       MESSAGE_COMPLETION,
		CreatedTime: time.Now().Unix(),
	}
	doneBytes, _ := utils.JsonMarshalToBytes(doneMsg)
	event := &sse.Event{
		Event: TEXT,
		Data:  doneBytes,
	}
	// 忽略发送错误，因为这是最终的清理路径
	_ = stream.Publish(event)
}

// tcc获取 hiAgentConfig
func getApiKey(key string) (string, error) {
	switch key {
	case TCC_KEY_INTENTION:
		return hiAgentConfig.Intention, nil
	case TCC_KEY_DIAGNOSE:
		return hiAgentConfig.Diagnose, nil
	case TCC_KEY_SUMMARY:
		return hiAgentConfig.Summary, nil
	case TCC_KEY_TICKET_INTENTION:
		return hiAgentConfig.TicketIntention, nil
	default:
		return "", errors.New("key not found")
	}
}

func getAgentProductInfo(ctx context.Context, function string) (*discourseHertz.MCPJson, error) {
	res, err := hiAgentTccClient(ctx)

	if err != nil {
		return nil, err
	}

	arr := res.([]discourseHertz.MCPJson)

	for _, product := range arr {
		if product.Name == function {
			return &product, nil
		}
	}

	return nil, errors.New("product not found")
}

func getAgentProductInfoV2(ctx context.Context, function string) (*discourseHertz.MCPJson, error) {
	// 使用 Redis hash 获取产品信息
	product := &discourseHertz.MCPJson{}
	err := rds_redis.GetObj[*discourseHertz.MCPJson](function, product)
	if err != nil {
		if errors.Is(err, redis.Nil) {
			return nil, errors.New("product not found")
		} else {
			return nil, errorcode.RedisConnectError
		}
	}
	return product, nil
}

func doubleEncodeJson(v interface{}) string {
	// 再次编码
	str := utils.JsonToString(utils.JsonToString(v))
	// 去掉收尾引号
	return str[1 : len(str)-1]
}

func interfaceToStr(query interface{}) (string, error) {
	// 如果是字符串，则原样返回
	if str, ok := query.(string); ok {
		return str, nil
	}
	// 否则进行编码
	return utils.JsonMarshal(query)
}

// 打印 panic日志
func printStackRunTimeError(ctx context.Context, panic interface{}) {
	logs.CtxError(ctx, "[discourse/printStackRunTimeError]")
	buf := make([]byte, 4096)
	stackSize := runtime.Stack(buf, false)
	logs.CtxError(ctx, "panic recovered: %v\n%s", panic, string(buf[:stackSize]))
}

func str2TicketInfo(ctx context.Context, msg string) (*discourseHertz.FromMsgResp, error) {
	form := &discourseHertz.FromMsgResp{}
	err := utils.JsonUnmarshalString(msg, form)
	if err != nil {
		return nil, err
	}
	instance := &discourseHertz.DoInstances{}
	// 提取
	if mm, ok := form.Arguments.(map[string]interface{}); ok {
		err = mapstructure.Decode(mm, instance)
		if err != nil {
			return nil, err
		}
	} else {
		err = utils.JsonUnmarshalString(form.Arguments.(string), instance)
		if err != nil {
			return nil, err
		}
	}
	form.Instances = instance
	product, err := getAgentProductInfo(ctx, form.Function)
	if err != nil {
		return nil, err
	}
	form.Id = product.ID
	return form, nil
}

func ticket2Diagnose(ctx context.Context, ticket *discourseHertz.FromMsgResp) (*ticket_diagnose_task.CreateDiagnoseTaskReq, error) {
	template, err := dal.FindDiagnoseTemplateByName(ctx, ticket.Function)

	if err != nil {
		logs.CtxError(ctx, "FindDiagnoseTemplateByName err:%v", err)
	}

	task := &ticket_diagnose_task.CreateDiagnoseTaskReq{
		TicketID:           ticket.TickId,
		DiagnoseTemplateID: &template.DiagnoseTemplateID,
		Dimension:          "instance",
		DiagnoseItemIDs:    nil,
		DiagnoseResources: []*diagnose_result.DiagnoseResource{
			{
				Instances: []string{lo.Ternary(ticket.Instances.ClusterID != "",
					fmt.Sprintf("%s:%s:%s", ticket.Instances.ClusterID, ticket.Instances.Namespace, ticket.Instances.PodName),
					ticket.Instances.Instance)},
				ProductCategoryID: ticket.Id,
			},
		},
		CreateUserID:       "",
		CreateUserName:     nil,
		AccountID:          &ticket.AccountId,
		DiagnoseStartTime:  time.Now().Add(-6 * time.Hour).Unix(),
		DiagnoseEndTime:    time.Now().Unix(),
		DiagnoseType:       2,
		ProductCategoryIDs: []int64{ticket.Id},
		Action:             "",
		Version:            nil,
	}

	return task, nil
}

func ticket2ADiagnose(ticket *discourseHertz.FromMsgResp) (*discourseHertz.RunRequest, error) {
	return &discourseHertz.RunRequest{
		Product:   ticket.Product,
		Function:  ticket.Function,
		AccountID: ticket.AccountId,
		TicketID:  ticket.TickId,
		Arguments: utils.JsonToString(ticket.Arguments),
		Query:     ticket.Query,
	}, nil
}

func summary2MD(summary string, url string) (string, error) {
	str := &discourseHertz.SuggestResp{}
	err := utils.JsonUnmarshalString(summary, str)
	if err != nil {
		return "", err
	}
	md := fmt.Sprintf("【问题分析】:\n%s \n[(打开火速智能诊断)](https://hs-boe.bytedance.net/) \n\n【下一步建议】:\n%s", str.Reason, str.Suggest)
	if url != "" {
		md = md + "\n" + fmt.Sprintf("[查看诊断报告详情](%s)", url)
	}
	return md, nil
}

// todo: 线上会一直 pending，后续优化
// ------------------------------------ 以下是 httpclient

// GetHttpClient 从连接池获取HTTP客户端
func GetHttpClient() (*byted.Client, error) {
	/*if defaultHttpClientPool := http_client.GetHttpClientPool(); defaultHttpClientPool != nil {
		client, err := defaultHttpClientPool.HttpPoolGet()
		if err != nil || client == nil {
			return nil, lo.Ternary(err == nil, errors.New("get http client error"), err)
		}
		return client, nil
	}
	return nil, errors.New("no http client pool")*/

	return byted.NewClient(byted.WithAppClientOptions(
		hertzClient.WithDialer(standard.NewDialer()),
		hertzClient.WithRetryConfig(httpRetry.WithMaxAttemptTimes(3)), // 重试
		hertzClient.WithMaxConnDuration(15*time.Minute),               // 连接最大持续时间
		hertzClient.WithDialTimeout(3*time.Minute),
		hertzClient.WithClientReadTimeout(3*time.Minute),
		hertzClient.WithWriteTimeout(3*time.Minute),
		hertzClient.WithMaxConnWaitTimeout(2*time.Minute),
	))

}

// PutHttpClient 将HTTP客户端归还到连接池
func PutHttpClient(client *byted.Client) {
	if client != nil {
		logs.CtxInfo(context.Background(), "[discourse/putDiscourseClient] put http client success")
		client.CloseIdleConnections()
		//http_client.GetHttpClientPool().HttpPoolPut(client)
	}
}
