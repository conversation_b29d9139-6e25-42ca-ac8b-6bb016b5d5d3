package discourse

import (
	resourceHandler "code.byted.org/volcengine-support/cloud-sherlock/biz/handler/describe_resource"
	"code.byted.org/volcengine-support/cloud-sherlock/gen/hertz/describe_resource"
	"code.byted.org/volcengine-support/cloud-sherlock/gen/hertz/diagnose_result"
	"code.byted.org/volcengine-support/cloud-sherlock/pkg/http_client"
	"code.byted.org/volcengine-support/cloud-sherlock/pkg/sso"
	"context"
	"errors"
	"fmt"
	"github.com/bytedance/sonic"
	"github.com/google/uuid"
	"github.com/hertz-contrib/sse"
	"regexp"
	"sort"
	"strconv"
	"strings"
	"sync"
	"time"

	"code.byted.org/gopkg/logs/v2"
	"code.byted.org/gopkg/tccclient"
	"code.byted.org/volcengine-support/cloud-sherlock/biz/dal"
	"code.byted.org/volcengine-support/cloud-sherlock/gen/gorm/model"
	"code.byted.org/volcengine-support/cloud-sherlock/gen/hertz/diagnose_task_run"
	discourseHertz "code.byted.org/volcengine-support/cloud-sherlock/gen/hertz/discourse"
	"code.byted.org/volcengine-support/cloud-sherlock/pkg/rds_redis"
	"code.byted.org/volcengine-support/cloud-sherlock/pkg/tcc"
	"code.byted.org/volcengine-support/cloud-sherlock/utils"
	"github.com/cloudwego/hertz/pkg/app"
	"github.com/samber/lo"
)

var (
	service          *Service
	sOnce            sync.Once
	tagRegex         = regexp.MustCompile(`^tag\n*`)
	updateRegex      = regexp.MustCompile(`\\*"Update\\*":true`)
	hiAgentTccClient tccclient.Getter
)

func init() {
	sOnce.Do(func() {
		service = &Service{
			logger: utils.NewModuleLogger("DiscourseService"),
		}
		var err error
		hiAgentConfig, err = tcc.GetHiagentConfig(context.Background())
		if err != nil {
			panic(err)
		}
		// hi agent-tcc
		config := tccclient.NewConfigV2()
		logs.Infof("init tcc client, config: %v", config)
		config.FirstGetTimeout = time.Second * 10

		client, err := tccclient.NewClientV2("volcengine.support.llm_toolkit", config)

		if err != nil {
			logs.Errorf("init tcc client error: %v", err)
			panic(err)
		}

		hiAgentTccClient = client.NewGetter("hi_agent", sonic.Unmarshal, []discourseHertz.MCPJson{})

		logs.Infof("init discourse service success")
	})
}

func GetDiscourseService() *Service {
	sOnce.Do(func() {
		service = &Service{
			logger: utils.NewModuleLogger("DiscourseService"),
		}
		var err error
		hiAgentConfig, err = tcc.GetHiagentConfig(context.Background())
		if err != nil {
			panic(err)
		}
		// hi agent-tcc
		config := tccclient.NewConfigV2()
		logs.Infof("init tcc client, config: %v", config)
		config.FirstGetTimeout = time.Second * 10

		client, err := tccclient.NewClientV2("volcengine.support.llm_toolkit", config)

		if err != nil {
			logs.Errorf("init tcc client error: %v", err)
			panic(err)
		}

		hiAgentTccClient = client.NewGetter("hi_agent", sonic.Unmarshal, []discourseHertz.MCPJson{})

		logs.Infof("init discourse service success")
	})
	return service
}

type Service struct {
	logger *utils.ModuleLogger
}

// Create 新建对话
func (s *Service) Create(ctx context.Context, req *discourseHertz.CreateReq) (*discourseHertz.CreateResp, error) {
	if req.Query == "" {
		return nil, errors.New("query is empty")
	}
	user, err := getUserInfo(req.Token)
	if err != nil {
		return nil, err
	}
	uid := strings.ReplaceAll(uuid.New().String(), "-", "")
	session := &model.DiscourseSession{
		UserID:    user.Email,
		SessionID: uid,
		IsHistory: lo.ToPtr(false),
		StartDate: lo.ToPtr(time.Now()),
		Title:     req.Query,
	}
	// 创建 session
	intention, err := CreateSession(ctx, &CreateStruct{
		Key:    TCC_KEY_INTENTION,
		UserID: user.Email,
		Inputs: map[string]string{"user_id": user.Email, "department": user.Department.Name, "report": " ", "product_id": req.Info.SubProductID, "product_name": req.Info.SubProductName},
	})
	if err != nil || intention == "" {
		return nil, lo.Ternary(err == nil, errors.New("session创建失败"), err)
	}
	session.IntentionSession = &intention

	diagnose, err := CreateSession(ctx, &CreateStruct{
		Key:    TCC_KEY_DIAGNOSE,
		UserID: user.Email,
		Inputs: map[string]string{"user_id": user.Email, "department": user.Department.Name, "product_id": req.Info.SubProductID, "product_name": req.Info.SubProductName},
	})
	if err != nil || diagnose == "" {
		return nil, lo.Ternary(err == nil, errors.New("session创建失败"), err)
	}
	session.DiagnoseSession = &diagnose

	summary, err := CreateSession(ctx, &CreateStruct{
		Key:    TCC_KEY_SUMMARY,
		UserID: user.Email,
		Inputs: map[string]string{"user_id": user.Email, "department": user.Department.Name, "report": " ", "product_id": req.Info.SubProductID, "product_name": req.Info.SubProductName},
	})
	if err != nil || summary == "" {
		return nil, lo.Ternary(err == nil, errors.New("session创建失败"), err)
	}
	session.SummarySession = &summary

	if err := dal.CreateDiscourseSession(ctx, session); err != nil {
		return nil, err
	} else {
		// 更新 redis
		_ = rds_redis.StoreObj(uid, discourseHertz.SessionRedisObj{
			ID:               session.ID,
			SessionID:        session.SessionID,
			IntentionSession: &intention,
			DiagnoseSession:  &diagnose,
			SummarySession:   &summary,
		}, TTL)
		return &discourseHertz.CreateResp{
			SessionID: uid,
		}, nil
	}
}

// History 历史列表
func (s *Service) History(ctx context.Context, req *discourseHertz.HistoryRequest) (*discourseHertz.HistoryResp, error) {
	user, err := getUserInfo(req.Token)
	if err != nil {
		return nil, err
	}
	userId := user.Email
	// 返回一个月内的对话记录
	discourse, total, err := dal.HistoryDiscourseList(ctx, userId, req.PageNumber, req.PageSize)
	if err != nil {
		return nil, err
	}
	resp := lo.Map(discourse, func(item *model.DiscourseSession, index int) *discourseHertz.QueryDiscourseSessionDo {
		return &discourseHertz.QueryDiscourseSessionDo{
			SessionID: item.SessionID,
			Title:     item.Title,
			Date:      item.StartDate,
		}
	})
	return &discourseHertz.HistoryResp{
		Items:      resp,
		PageNumber: req.PageNumber,
		PageSize:   req.PageSize,
		TotalCount: total,
	}, nil
}

// Report 查询诊断报告
func (s *Service) Report(ctx context.Context, req *discourseHertz.QueryDiagnoseTaskRunDetailReq) (*discourseHertz.QueryDiagnoseTaskRunDetailResp, error) {
	logger := s.logger.WithFunc("Report")

	session, err := dal.QueryDiscourseSessionByTaskRunID(ctx, req.DiagnoseTaskRunID)

	if err != nil {
		logger.CtxError(ctx, "QueryDiscourseMessageByTaskRunID err:%v", err)
		return nil, err
	}

	result, err := s.report(ctx, req.DiagnoseTaskRunID, &discourseHertz.SessionRedisObj{
		ID:               session.ID,
		SessionID:        session.SessionID,
		IntentionSession: session.IntentionSession,
		DiagnoseSession:  session.DiagnoseSession,
		SummarySession:   session.SummarySession,
	}, &sso.UserInfo{
		Email: session.UserID,
	}, true)

	if err != nil {
		return nil, err
	}

	return &discourseHertz.QueryDiagnoseTaskRunDetailResp{
		Data:      result,
		Status:    (*result)[0].Status,
		Progress:  (*result)[0].Progress,
		TaskRunID: (*result)[0].TaskRunID,
	}, nil

}

// ReportMirror 历史记录侧查询报告
func (s *Service) ReportMirror(ctx context.Context, req *discourseHertz.QueryDiagnoseTaskRunDetailReq) (*discourseHertz.QueryDiagnoseTaskRunDetailResp, error) {
	logger := s.logger.WithFunc("Report")

	message, err := dal.QueryDiscourseMessageByTaskRunID(ctx, req.DiagnoseTaskRunID)

	if err != nil || message.Summary == nil {
		logger.CtxError(ctx, "QueryDiscourseMessageByTaskRunID err:%v", err)
		return nil, lo.Ternary(err == nil, errors.New("no summary"), err)
	}

	var session *model.DiscourseSession

	// 官网侧
	if message.SessionID == 0 {
		session = &model.DiscourseSession{
			UserID: "ticket",
		}
	} else {
		session, err = dal.QueryDiscourseSessionByID(ctx, message.SessionID)
		if err != nil {
			logger.CtxError(ctx, "QueryDiscourseSessionByID err:%v", err)
			return nil, err
		}
	}

	api, err := getApiKey(TCC_KEY_SUMMARY)
	if err != nil {
		return nil, err
	}

	// 查询 hi agent
	detailMessage, err := GetConversationMessage(ctx, api, *message.Summary, session.UserID)

	if err != nil {
		return nil, err
	}

	return &discourseHertz.QueryDiagnoseTaskRunDetailResp{
		Data: lo.ToPtr([]*discourseHertz.SSEMessage{{
			Answer: detailMessage.AnswerInfo.Answer,
		}}),
	}, nil

}

// Discourse 对话
func (s *Service) Discourse(ctx context.Context, c *app.RequestContext, req *discourseHertz.HandleDiscourseRequest) {
	logger := s.logger.WithFunc("Diagnose")
	start := time.Now()
	stream := makeServerSSE(c)
	userInfo, err := getUserInfo(req.Token)
	if err != nil {
		sendErrorEvent(stream, "获取用户信息失败，请检查网络", err)
		return
	}
	// 获取 session
	session, err := getSession(ctx, userInfo, req.SessionID)

	if err != nil {
		logger.CtxError(ctx, "getSession err:%v", err)
		sendErrorEvent(stream, "获取会话失败", err)
		return
	}

	data := make(chan string, 1)
	done := make(chan struct{}, 1)

	// 关闭通道
	defer func() {
		close(data)
		close(done)
		if pan := recover(); pan != nil {
			printStackRunTimeError(ctx, pan)
		}
	}()

	go func() {
		defer func() {
			select {
			case done <- struct{}{}:
			default:

			}
		}()

		ChatV2(ctx, &ChatV2Struct{
			Query:     lo.Ternary[interface{}](req.Query == "", req.ReQuery, req.Query),
			Session:   *session.IntentionSession,
			UserID:    userInfo.Email,
			Key:       TCC_KEY_INTENTION,
			Data:      data,
			Stream:    stream,
			NeedBlock: false,
		})
	}()

	// 处理事件循环
	for {
		select {
		// 补全下表单抽取的 id
		case dataMsg := <-data:
			if msg, err := s.form(ctx, dataMsg); err != nil {
				sendErrorEvent(stream, "工单抽取出现错误", err)
			} else {
				sendNormalEvent(stream, TICKET, msg)
			}

		case <-done:
			logger.CtxInfo(ctx, "Discourse cost time: %v", time.Since(start))
			sendCompletionEvent(stream)
			return
		}
	}
}

// Detail 查看历史对话
func (s *Service) Detail(ctx context.Context, req *discourseHertz.DetailRequest) (*discourseHertz.SSEDetail, error) {
	logger := s.logger.WithFunc("Detail")

	var userInfo *sso.UserInfo
	var err error

	if req.UserID == "" {
		userInfo, err = getUserInfo(req.Token)
	} else {
		userInfo = &sso.UserInfo{
			Email: req.UserID,
		}
	}

	if err != nil {
		return nil, err
	}
	session, err := getSession(ctx, userInfo, req.SessionID)
	if err != nil {
		return nil, err
	}

	details := make([]discourseHertz.DetailMessage, 0)

	// 对话不为空
	if session.IntentionSession != nil {
		api, err := getApiKey(TCC_KEY_INTENTION)
		if err != nil {
			return nil, err
		}
		msg, err := GetConversationMessages(ctx, api, *session.IntentionSession, userInfo.Email)
		if err != nil {
			return nil, err
		}

		if msg != nil && msg.Messages != nil {
			lo.ForEach(msg.Messages, func(item discourseHertz.DetailMessage, index int) {
				// 去掉 tag
				if tagRegex.MatchString(item.AnswerInfo.Answer.(string)) {
					item.Type = TICKET
					item.AnswerInfo.Answer = strings.Replace(item.AnswerInfo.Answer.(string), "tag\n", "", 1)
					var tmp discourseHertz.FromMsgResp
					err = utils.JsonUnmarshalString(item.AnswerInfo.Answer.(string), &tmp)
					if err != nil {
						logger.CtxError(ctx, "JsonUnmarshalString error:%v", err)
					} else {
						product, err := getAgentProductInfo(ctx, tmp.Function)
						if err != nil {
							logger.CtxError(ctx, "getAgentProductInfo error:%v", err)
						}
						tmp.Id = product.ID
						tmpAnswer, err := utils.JsonMarshal(tmp)
						if err == nil {
							item.AnswerInfo.Answer = tmpAnswer
						}
					}
				}
				// 去掉转发的工单信息
				if updateRegex.MatchString(item.Query) {
					item.Query = ""
				}
				item.AnswerInfo.ID = item.AnswerInfo.MessageID
				details = append(details, item)
			})
		}

	}

	// 自建诊断不为空
	if session.DiagnoseSession != nil {
		api, err := getApiKey(TCC_KEY_DIAGNOSE)

		if err != nil {
			return nil, err
		}

		msg, err := GetConversationMessages(ctx, api, *session.DiagnoseSession, userInfo.Email)

		if err != nil {
			return nil, err
		}

		if msg != nil && msg.Messages != nil && len(msg.Messages) > 0 {
			for _, m := range msg.Messages {
				m.Query = ""
				m.AnswerInfo.ID = m.AnswerInfo.MessageID
				details = append(details, m)
			}
		}
	}

	// 报告不为空
	messages, err := dal.QueryDiscourseMessageBySessionID(ctx, session.ID)

	if err != nil {
		logger.CtxError(ctx, "QueryDiscourseMessageBySessionID error:%v", err)
		return nil, err
	}

	if session.SummarySession != nil {
		api, err := getApiKey(TCC_KEY_SUMMARY)

		if err != nil {
			return nil, err
		}

		msg, err := GetConversationMessages(ctx, api, *session.SummarySession, userInfo.Email)

		for _, m := range messages {
			// 中台诊断
			if m.TaskRunID != nil {
				// 拼接
				if m.Summary != nil && msg != nil {
					item := lo.FindOrElse(msg.Messages, discourseHertz.DetailMessage{}, func(item discourseHertz.DetailMessage) bool {
						return item.AnswerInfo.MessageID == *m.Summary
					})

					if item.AnswerInfo == nil {
						continue
					}

					item.Query = ""
					item.Type = REPORT

					suggest := &discourseHertz.SuggestResp{}

					if err := utils.JsonUnmarshalString(item.AnswerInfo.Answer.(string), suggest); err != nil {
						logger.CtxError(ctx, "JsonUnmarshalString error:%v", err)
						continue
					}

					runDetail, err := s.queryDiagnoseTaskRunDetailBot(ctx,
						&diagnose_task_run.QueryDiagnoseTaskRunDetailReq{
							DiagnoseTaskRunID: *m.TaskRunID,
							Action:            "",
						},
					)

					if err != nil {
						logger.CtxError(ctx, "QueryDiagnoseTaskRunDetail error:%v", err)
						continue
					}

					suggest.Information = runDetail.Information

					item.AnswerInfo.TaskRunID = *m.TaskRunID
					item.AnswerInfo.ID = item.AnswerInfo.MessageID
					item.AnswerInfo.Answer = suggest
					details = append(details, item)

					// 删除
					msg.Messages = lo.Filter(msg.Messages, func(item discourseHertz.DetailMessage, index int) bool {
						return item.AnswerInfo.MessageID != *m.Summary
					})

				} else if req.UserID == "" {
					// 主动发起调用
					report, err := s.report(ctx, *m.TaskRunID, session, userInfo, false)
					if err != nil {
						logs.CtxError(ctx, "ReportTaskRunID error:%v", err)
						continue
					}
					// 转为 answer info
					lo.ForEach(*report, func(item *discourseHertz.SSEMessage, index int) {
						answerInfo := sseMsg2AnswerInfo(item)
						details = append(details, discourseHertz.DetailMessage{
							Type:       REPORT,
							AnswerInfo: answerInfo,
						})
					})
				}
			} /*else if m.Summary != nil && m.ConversationID == nil {
				//  主动触发 maas方诊断不自动触发
				report, err := s.aReport(ctx, *m.Summary, "", session, userInfo, update)
				if err != nil {
					logs.CtxError(ctx, "ReportAgentDiagnoseDetail error:%v", err)
					continue
				}
				// 转为 answer info
				lo.ForEach(*report, func(item *discourseHertz.SSEMessage, index int) {
					answerInfo := sseMsg2AnswerInfo(item)
					details = append(details, discourseHertz.DetailMessage{
						Type:       REPORT,
						AnswerInfo: answerInfo,
					})
				})
			}*/
		}

		if msg != nil {
			lo.ForEach(msg.Messages, func(item discourseHertz.DetailMessage, index int) {
				item.Query = ""
				item.AnswerInfo.ID = item.AnswerInfo.MessageID
				item.Type = REPORT
				details = append(details, item)
			})
		}
	}

	// 按照时间降序
	sort.Slice(details, func(i, j int) bool {
		// || (messages[i].AnswerInfo.CreatedTime == messages[j].AnswerInfo.CreatedTime && messages[i].Weight > messages[j].Weight)
		return details[i].AnswerInfo.CreatedTime > details[j].AnswerInfo.CreatedTime
	})

	return &discourseHertz.SSEDetail{
		Messages: details,
	}, nil
}

// Run 执行
func (s *Service) Run(c context.Context, ctx *app.RequestContext, req *discourseHertz.RunRequest) {
	// 如果是自建诊断，直接转发
	if req.ID == 0 || req.ID < 0 {
		s.agentDiagnoseTask(c, ctx, &discourseHertz.CreateAgentDiagnoseTaskReq{
			Token:     req.Token,
			SessionID: req.SessionID,
			ReQuery: &discourseHertz.RunRequest{
				Product:   req.Product,
				Function:  req.Function,
				Query:     req.Query,
				Arguments: req.Arguments,
				TicketID:  req.TicketID,
				AccountID: req.AccountID,
			},
		})
		return
	}

	// 如果是中台诊断，查询 product，template并封装数据
	if !req.Update {
		logger := s.logger.WithFunc("Run")
		// 开启 sse
		stream := makeServerSSE(ctx)
		userInfo, err := getUserInfo(req.Token)
		if err != nil {
			logger.CtxError(c, "getUserInfo err:%v", err)
			sendErrorEvent(stream, "获取用户信息失败", err)
			return
		}
		start := time.Now()
		session, err := getSession(c, userInfo, req.SessionID)
		if err != nil {
			s.logger.CtxError(c, "getSession err:%v", err)
			sendErrorEvent(stream, "获取会话失败", err)
			return
		}
		user, err := getUserInfo(req.Token)
		if err != nil {
			s.logger.CtxError(c, "getUserInfo err:%v", err)
			sendErrorEvent(stream, "获取用户信息失败", err)
			return
		}

		template, err := dal.FindDiagnoseTemplateByName(c, req.Function)
		if err != nil {
			s.logger.CtxError(c, "FindDiagnoseTemplateByName err:%v", err)
			sendErrorEvent(stream, "诊断场景不支持", err)
			return
		}
		// 检查资源 id是否正确
		// 根据产品 ID查询产品信息
		product, err := dal.QueryProductCategoryByID(c, req.ID)
		if err != nil {
			s.logger.CtxError(c, "QueryProductCategoryByID err:%v", err)
			sendErrorEvent(stream, "产品信息不存在", err)
			return
		}
		body := describe_resource.DescribeResourceRequest{
			Dimension: "instance",
			Resources: &describe_resource.Resources{
				ResourceType: product.ResourceType,
				Product:      product.Product,
				SubProduct:   product.SubProduct,
				Instances:    req.Instances,
			},
		}

		// 创建一个app.RequestContext
		resourceCtx := ctx.Copy()
		resourceCtx.Request.SetBodyString(utils.JsonToString(body))

		if err = resourceHandler.NewResourceHandlerFactory().HandlerResource(c, resourceCtx); err != nil {
			// 现在只有实例 iD不对的情况下直接走保底问答
			s.logger.CtxError(c, "NewResourceHandler err:%v", err)
			s.backStop(c, stream, req.Query, session, user)
			return
		} else if _, ok := resourceCtx.Keys["__has_error__"]; ok {
			s.logger.CtxError(c, "NewResourceHandler err:%v", err)
			s.backStop(c, stream, req.Query, session, user)
			return
		}

		task := &discourseHertz.CreateDiagnoseTaskReq{}
		{
			task.TicketID = req.TicketID
			task.DiagnoseType = 2
			task.DiagnoseTemplateID = &template.DiagnoseTemplateID
			task.Dimension = "instance"
			task.CreateUserID = user.Email
			task.DiagnoseStartTime = lo.Ternary(req.DiagnoseStartTime == 0, time.Now().Add(-6*time.Hour).Unix(), req.DiagnoseStartTime)
			task.DiagnoseEndTime = lo.Ternary(req.DiagnoseEndTime == 0, time.Now().Unix(), req.DiagnoseStartTime)
			task.DiagnoseResources = []*diagnose_result.DiagnoseResource{{
				ProductCategoryID: req.ID,
				Instances:         req.Instances,
			}}
			task.ProductCategoryIDs = []int64{req.ID}
			task.AccountID = &req.AccountID
			task.ReQuery = req.Query
		}

		if taskRunId, err := s.diagnoseTask(c, userInfo, session, task); err != nil {
			s.logger.CtxError(c, "CreateDiagnoseTask err:%v", err)
			sendErrorEvent(stream, "执行诊断任务失败", err)
			return
		} else {
			_ = stream.Publish(&sse.Event{
				Event: TASK,
				Data:  []byte(utils.JsonToString(map[string]string{"answer": strconv.FormatInt(*taskRunId, 10)})),
			})
		}
		logger.CtxInfo(c, "Run total time: %v", time.Since(start).Milliseconds())
		sendCompletionEvent(stream)
	} else {
		// 重新走对话
		s.Discourse(c, ctx, &discourseHertz.HandleDiscourseRequest{
			ReQuery: &discourseHertz.RunRequest{
				Update:    req.Update,
				Product:   req.Product,
				Function:  req.Function,
				AccountID: req.AccountID,
				TicketID:  req.TicketID,
				Arguments: req.Arguments,
				Query:     req.Query,
				Instances: req.Instances,
			},
			Token:     req.Token,
			SessionID: req.SessionID,
		})
	}
}

func (s *Service) Stage(ctx context.Context, req *discourseHertz.StageRequest) (*discourseHertz.StageResponse, error) {
	userInfo, err := getUserInfo(req.Token)
	if err != nil {
		return nil, err
	}
	resp, err := http_client.DoRequest(ctx, http_client.RequestParams{
		Host:   hiAgentConfig.StageUrl,
		Path:   "/api/get_message",
		Method: "GET",
		Query:  fmt.Sprintf("message_id=%s&user_id=%s", req.MessageID, userInfo.Email),
	})
	if err != nil || resp.StatusCode() != 200 || resp.Body() == nil {
		return nil, lo.Ternary(err == nil, errors.New("get message failed"), err)
	}

	stageResp := &discourseHertz.StageResp{}

	if err = utils.JsonUnmarshalString(string(resp.Body()), stageResp); err != nil {
		return nil, err
	}

	var stage string

	switch stageResp.MessageStage {
	case "诊断阶段":
		stage = "diagnose"
	case "根因分析和恢复建议":
		stage = "summary"
		if stageResp.PID == "" && stageResp.PName == "" {
			// 尝试去中台侧找到数据
			if message, err := dal.QueryDiscourseMessageByMessageID(ctx, req.MessageID, "summary"); err == nil {
				// 查询诊断报告概况信息
				if taskDetail, err := s.queryDiagnoseTaskRunDetailBot(ctx,
					&diagnose_task_run.QueryDiagnoseTaskRunDetailReq{
						DiagnoseTaskRunID: *message.TaskRunID,
						Action:            "",
					},
				); err == nil {
					stageResp.PID = (*taskDetail.Information.ProductCategory)[0].SubProduct
				}
			}
		}
	case "相似工单推荐":
		stage = "recommend"
		// todo : 优化
		if stageResp.PID == "" && stageResp.PName == "" {
			// 尝试去中台侧找到数据
			if message, err := dal.QueryDiscourseMessageByMessageID(ctx, req.MessageID, "recommend"); err == nil {
				// 查询诊断报告概况信息
				if taskDetail, err := s.queryDiagnoseTaskRunDetailBot(ctx,
					&diagnose_task_run.QueryDiagnoseTaskRunDetailReq{
						DiagnoseTaskRunID: *message.TaskRunID,
						Action:            "",
					},
				); err == nil {
					stageResp.PID = (*taskDetail.Information.ProductCategory)[0].SubProduct
				}
			}
		}
	case "问答结果":
		stage = "normal"
	case "意图识别":
		stage = "intention"
	default:
		return nil, errors.New("unknown stage")
	}

	return &discourseHertz.StageResponse{
		Stage:       stage,
		ProductID:   stageResp.PID,
		ProductName: stageResp.PName,
	}, nil
}
