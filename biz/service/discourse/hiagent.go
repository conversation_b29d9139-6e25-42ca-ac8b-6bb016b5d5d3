package discourse

import (
	"code.byted.org/gopkg/logs/v2"
	"code.byted.org/middleware/hertz/byted/consts"
	"code.byted.org/middleware/hertz/pkg/protocol"
	discourseHertz "code.byted.org/volcengine-support/cloud-sherlock/gen/hertz/discourse"
	"code.byted.org/volcengine-support/cloud-sherlock/utils"
	"context"
	"errors"
	"fmt"
	"github.com/hertz-contrib/sse"
	"strconv"
	"strings"
	"time"
)

type QueryReq struct {
	Query             string      `json:"Query"`
	AppConversationID string      `json:"AppConversationID"`
	ResponseMode      string      `json:"ResponseMode"`
	UserID            string      `json:"UserID"`
	QueryExtends      interface{} `json:"QueryExtends"`
}

type ChatV2Struct struct {
	Query     interface{} `json:"Query"`
	Session   string      `json:"Session"`
	UserID    string      `json:"UserId"`
	Key       string      `json:"SessionKey"`
	Data      chan string `json:"Data"`
	errs      chan error
	Stream    *sse.Stream `json:"Stream"`
	NeedBlock bool        `json:"NeedBlock"`
}

type CreateStruct struct {
	Key, UserID string
	Inputs      interface{}
}

type UpdateStruct struct {
	Session, Key, UserID string
	Inputs               map[string]interface{}
	First                bool
}

type FeedbackReq struct {
	AppKey    string `json:"AppKey"`
	UserId    string `json:"UserId"`
	MessageID string `json:"MessageID"`
	LikeType  int    `json:"LikeType"`
}

var (
	BLOCK  = "blocking"
	STREAM = "streaming"
)

// CreateSession 创建 session
func CreateSession(c context.Context, req *CreateStruct) (string, error) {

	hcli, err := GetHttpClient()

	if err != nil {
		logs.CtxError(c, "[discourse/CreateSession] GetHttpClient err:%v", err)
		return "", err
	}

	defer func() {
		PutHttpClient(hcli)
	}()

	apikey, err := getApiKey(req.Key)

	if err != nil {
		return "", err
	}

	hreq := &protocol.Request{}
	hreq.SetHeader("apikey", apikey)
	input := struct {
		Inputs interface{}
		UserID string
	}{
		req.Inputs,
		req.UserID,
	}

	{
		hreq.SetBody([]byte(utils.JsonToString(input)))
		hreq.SetHeader("Content-Type", "application/json")
		hreq.SetRequestURI(hiAgentConfig.BaseUrl + "/create_conversation")
		hreq.SetMethod("POST")
	}

	resp := &protocol.Response{}
	err = hcli.DoTimeout(c, hreq, resp, DefaultTimeout)

	if resp.StatusCode() == 200 && len(resp.Body()) > 0 {
		msg := &discourseHertz.CreateSessionResp{}
		err = utils.JsonUnmarshal(resp.Body(), msg)
		if err != nil {
			return "", err
		}
		return msg.Conversation.AppConversationID, nil
	} else {
		return "", errors.New("create session fail")
	}
}

func UpdateAgentVar(c context.Context, req *UpdateStruct) error {
	hcli, err := GetHttpClient()
	if err != nil {
		logs.CtxError(c, "[discourse/UpdateAgentVar] GetHttpClient err:%v", err)
		return err
	}

	defer func() {
		PutHttpClient(hcli)
	}()

	apikey, err := getApiKey(req.Key)
	hreq := &protocol.Request{}
	if err != nil {
		return err
	}
	{
		hreq.SetHeader("apikey", apikey)
		hreq.SetHeader("Content-Type", "application/json")
		hreq.SetHeader(consts.HeaderTimeout, strconv.Itoa(Header))
		hreq.SetMethod("POST")
	}

	if !req.First {
		// 获取原有的数据
		up := &struct {
			AppConversationID string `json:"AppConversationID"`
			UserID            string `json:"UserID"`
		}{
			AppConversationID: req.Session,
			UserID:            req.UserID,
		}
		body, err := utils.JsonMarshalToBytes(up)
		if err != nil {
			return err
		}
		{
			hreq.SetBody(body)
			hreq.SetRequestURI(hiAgentConfig.BaseUrl + "/get_conversation_inputs")
		}
		resp := &protocol.Response{}
		if err = hcli.DoTimeout(c, hreq, resp, DefaultTimeout); err != nil {
			return err
		}

		if resp.StatusCode() != 200 {
			return errors.New("update agent var failed")
		}

		before := &struct {
			Inputs map[string]interface{} `json:"Inputs"`
		}{}

		err = utils.JsonUnmarshal(resp.Body(), before)

		if err != nil {
			return err
		}

		// 保留无需替换的变量
		for k, v := range req.Inputs {
			before.Inputs[k] = v
		}

		req.Inputs = before.Inputs
	}

	up1 := &struct {
		AppConversationID string                 `json:"AppConversationID"`
		Inputs            map[string]interface{} `json:"Inputs"`
		UserID            string                 `json:"UserID"`
	}{
		AppConversationID: req.Session,
		Inputs:            req.Inputs,
		UserID:            req.UserID,
	}

	body, err := utils.JsonMarshalToBytes(up1)
	if err != nil {
		return err
	}

	{
		hreq.SetBody(body)
		hreq.SetRequestURI(hiAgentConfig.BaseUrl + "/update_conversation")
	}

	resp := &protocol.Response{}
	if err = hcli.DoTimeout(c, hreq, resp, DefaultTimeout); err != nil {
		return err
	}

	if resp.StatusCode() != 200 {
		return errors.New("update agent var failed")
	}
	return nil
}

// ChatV2 对话流程
func ChatV2(ctx context.Context, req *ChatV2Struct) {
	hcli, err := GetHttpClient()

	if err != nil {
		logs.CtxError(ctx, "ChatV2 GetHttpClient err:%v", err)
		sendErrorEvent(req.Stream, "网络链接错误", err)
		return
	}

	defer func() {
		PutHttpClient(hcli)
	}()

	start := time.Now()

	cli, err := sse.NewClientWithOptions(sse.WithHertzClient(hcli))
	if err != nil {
		sendErrorEvent(req.Stream, "网络链接错误", err)
		return
	}

	apikey, err := getApiKey(req.Key)

	if err != nil {
		sendErrorEvent(req.Stream, "网络链接错误", err)
		return
	}

	cli.SetDisconnectCallback(func(ctx context.Context, client *sse.Client) {
		sendErrorEvent(req.Stream, "连接关闭，请稍候再试", ctx.Err())
	})

	// 准备请求
	hreq := &protocol.Request{}
	{
		// 构造查询请求结构体，避免字符串转义问题
		queryReq := QueryReq{
			AppConversationID: req.Session,
			ResponseMode:      STREAM,
			UserID:            req.UserID,
			QueryExtends:      map[string]interface{}{}, // 使用空的map而不是空的interface{}
		}
		if _, ok := req.Query.(string); ok {
			queryReq.Query = fmt.Sprintf("%v", req.Query) // 将interface{}转换为字符串
		} else {
			queryReq.Query = utils.JsonToString(req.Query)
		}

		logs.CtxInfo(ctx, "ChatV2 queryReq:%v", utils.JsonToString(queryReq))

		requestBody, err := utils.JsonMarshalToBytes(queryReq)
		if err != nil {
			logs.CtxError(ctx, "ChatV2 JsonMarshal err:%v", err)
			sendErrorEvent(req.Stream, "请求构造错误", err)
			return
		}

		{
			hreq.SetRequestURI(hiAgentConfig.BaseUrl + "/chat_query_v2")
			hreq.SetHeader("apikey", apikey)
			hreq.SetHeader("Content-Type", "application/json")
			hreq.SetHeader(consts.HeaderTimeout, strconv.Itoa(Header))
			hreq.SetHeader("Connection", "keep-alive")
			hreq.SetHeader("Accept", "text/event-stream")
			hreq.SetBodyRaw(requestBody)
		}
	}

	dataMsg := strings.Builder{}
	first := true
	if err = cli.Subscribe(func(msg *sse.Event) {
		if msg.Data != nil {
			resp := string(msg.Data)
			// 需要拦截所有 message数据
			if req.NeedBlock && strings.HasPrefix(resp, "{\"event\": \"message\"") {
				sseMsg := &discourseHertz.SSEMessage{}
				err = utils.JsonUnmarshal(msg.Data, sseMsg)
				if err != nil {
					logs.CtxError(ctx, "ChatV2 JsonUnmarshal err:%v", err)
				}
				dataMsg.WriteString(sseMsg.Answer.(string))
				// 只拦截首个 message且开头为 tag的数据
			} else if first && strings.HasPrefix(resp, "{\"event\": \"message\"") {
				first = false
				sseMsg := &discourseHertz.SSEMessage{}
				err = utils.JsonUnmarshal(msg.Data, sseMsg)
				if err != nil {
					logs.CtxError(ctx, "ChatV2 JsonUnmarshal err:%v", err)
				}
				if strings.HasPrefix(sseMsg.Answer.(string), "tag\n") {
					sseMsg.Answer = strings.Replace(sseMsg.Answer.(string), "tag\n", "", 1)
					dataMsg.WriteString(utils.JsonToString(sseMsg))
					return // 不能转发这个包
				}
			}
			if err = req.Stream.Publish(msg); err != nil {
				logs.CtxError(ctx, "ChatV2 Info %s Publish err:%v", msg, err)
			}
		}
	}, sse.WithRequest(hreq)); err != nil {
		sendErrorEvent(req.Stream, "网络链接错误", err)
	} else if dataMsg.Len() > 0 {
		select {
		case req.Data <- dataMsg.String():
		default:

		}
	}

	logs.CtxInfo(ctx, "hi_agent discourse cost time: %v", time.Now().Sub(start))
}

func ChatV2Stream(ctx context.Context, req *ChatV2Struct) {
	hcli, err := GetHttpClient()

	if err != nil {
		logs.CtxError(ctx, "ChatV2 GetHttpClient err:%v", err)
		if req.errs != nil {
			select {
			case req.errs <- err:
			default:
			}
			return
		}
	}

	defer func() {
		PutHttpClient(hcli)
	}()

	cli, err := sse.NewClientWithOptions(sse.WithHertzClient(hcli))

	if err != nil {
		logs.CtxError(ctx, "ChatV2 NewClientWithOptions err:%v", err)
		if req.errs != nil {
			select {
			case req.errs <- err:
			default:
			}
			return
		}
	}

	apikey, err := getApiKey(req.Key)

	if err != nil {
		logs.CtxError(ctx, "ChatV2Stream GetApiKey err:%v", err)
		if req.errs != nil {
			select {
			case req.errs <- err:
			default:
			}
			return
		}
	}

	cli.SetDisconnectCallback(func(ctx context.Context, client *sse.Client) {
		logs.CtxInfo(ctx, "ChatV2Stream Connection Close")
		if req.errs != nil {
			select {
			case req.errs <- ctx.Err():
			default:
			}
		}
	})

	// 准备请求
	hreq := &protocol.Request{}
	{
		// 构造查询请求结构体，避免字符串转义问题
		queryReq := QueryReq{
			AppConversationID: req.Session,
			ResponseMode:      STREAM,
			UserID:            req.UserID,
			QueryExtends:      map[string]interface{}{}, // 使用空的map而不是空的interface{}
		}
		if _, ok := req.Query.(string); ok {
			queryReq.Query = fmt.Sprintf("%v", req.Query) // 将interface{}转换为字符串
		} else {
			queryReq.Query = utils.JsonToString(req.Query)
		}

		logs.CtxInfo(ctx, "ChatV2 queryReq:%v", utils.JsonToString(queryReq))
		// 使用JSON序列化来安全地构造请求体
		requestBody, err := utils.JsonMarshalToBytes(queryReq)

		if err != nil {
			logs.CtxError(ctx, "ChatV2 JsonMarshal err:%v", err)
			if req.errs != nil {
				select {
				case req.errs <- err:
				default:
				}
			}
			return
		}

		{
			hreq.SetRequestURI(hiAgentConfig.BaseUrl + "/chat_query_v2")
			hreq.SetHeader("apikey", apikey)
			hreq.SetHeader("Content-Type", "application/json")
			hreq.SetHeader(consts.HeaderTimeout, strconv.Itoa(Header))
			hreq.SetHeader("Connection", "keep-alive")
			hreq.SetHeader("Accept", "text/event-stream")
			hreq.SetBodyRaw(requestBody)
		}
	}

	if err = cli.Subscribe(func(msg *sse.Event) {
		if req.Data != nil && msg.Data != nil {
			req.Data <- string(msg.Data)
		}
	}, sse.WithRequest(hreq)); err != nil {
		logs.CtxError(ctx, "ChatV2 Info %s Subscribe err:%v", req.Session, err)
		if req.errs != nil {
			select {
			case req.errs <- err:
			default:
			}
		}
	}
}

func ChatV2Block(ctx context.Context, req *ChatV2Struct) string {
	hcli, err := GetHttpClient()

	if err != nil {
		logs.CtxError(ctx, "ChatV2 GetHttpClient err:%v", err)
		return ""
	}

	defer func() {
		PutHttpClient(hcli)
	}()

	cli, err := sse.NewClientWithOptions(sse.WithHertzClient(hcli))
	if err != nil {
		return ""
	}

	apikey, err := getApiKey(req.Key)

	if err != nil {
		logs.CtxError(ctx, "ChatV2 GetApiKey err:%v", err)
		return ""
	}

	cli.SetDisconnectCallback(func(ctx context.Context, client *sse.Client) {
		logs.CtxInfo(ctx, "ChatV2Block Connection Close")
	})

	// 准备请求
	hreq := &protocol.Request{}
	{
		// 构造查询请求结构体，避免字符串转义问题
		queryReq := QueryReq{
			AppConversationID: req.Session,
			ResponseMode:      BLOCK,
			UserID:            req.UserID,
			QueryExtends:      map[string]interface{}{}, // 使用空的map而不是空的interface{}
		}
		if _, ok := req.Query.(string); ok {
			queryReq.Query = fmt.Sprintf("%v", req.Query) // 将interface{}转换为字符串
		} else {
			queryReq.Query = utils.JsonToString(req.Query)
		}

		logs.CtxInfo(ctx, "ChatV2 queryReq:%v", utils.JsonToString(queryReq))
		// 使用JSON序列化来安全地构造请求体
		requestBody, err := utils.JsonMarshalToBytes(queryReq)
		if err != nil {
			logs.CtxError(ctx, "ChatV2 JsonMarshal err:%v", err)
			return ""
		}

		{
			hreq.SetRequestURI(hiAgentConfig.BaseUrl + "/chat_query_v2")
			hreq.SetHeader("apikey", apikey)
			hreq.SetHeader("Content-Type", "application/json")
			hreq.SetHeader(consts.HeaderTimeout, strconv.Itoa(Header))
			hreq.SetBodyRaw(requestBody)
		}
	}

	resp := protocol.Response{}
	err = hcli.DoTimeout(ctx, hreq, &resp, DefaultTimeout)
	if err != nil || resp.StatusCode() != 200 || resp.Body() == nil || len(resp.Body()) == 0 {
		logs.CtxError(ctx, "ChatV2 Info %s Subscribe err:%v", req.Session, err)
		return ""
	}
	return string(resp.Body())
}

// GetConversationMessages 查询记录
func GetConversationMessages(c context.Context, apikey, session, userId string) (*discourseHertz.SSEDetail, error) {
	hcli, err := GetHttpClient()

	if err != nil {
		logs.CtxError(c, "[discourse/GetConversationMessages] GetHttpClient err:%v", err)
		return nil, err
	}
	defer func() {
		PutHttpClient(hcli)
	}()

	hreq := &protocol.Request{}
	body, err := utils.JsonMarshalToBytes(&struct {
		AppConversationID string `json:"AppConversationID"`
		UserID            string `json:"UserID"`
		Limit             int    `json:"Limit"`
	}{
		AppConversationID: session,
		UserID:            userId,
		Limit:             100,
	})

	if err != nil {
		return nil, err
	}
	hreq.SetHeader("apikey", apikey)
	hreq.SetHeader("Content-Type", "application/json")
	hreq.SetRequestURI(hiAgentConfig.BaseUrl + "/get_conversation_messages")
	hreq.SetBody(body)
	hreq.SetMethod("POST")

	resp := &protocol.Response{}
	err = hcli.DoTimeout(c, hreq, resp, DefaultTimeout)

	if resp.StatusCode() == 200 && len(resp.Body()) > 0 {
		msg := &discourseHertz.SSEDetail{}
		_ = utils.JsonUnmarshal(resp.Body(), msg)
		return msg, nil
	}

	return nil, err
}

// GetConversationMessage 查询单条记录
func GetConversationMessage(c context.Context, apikey, messageID, userId string) (*discourseHertz.DetailMessage, error) {

	hcli, err := GetHttpClient()

	if err != nil {
		logs.CtxError(c, "[discourse/GetConversationMessages] GetHttpClient err:%v", err)
		return nil, err
	}
	defer func() {
		PutHttpClient(hcli)
	}()

	hreq := &protocol.Request{}
	body, err := utils.JsonMarshalToBytes(&struct {
		MessageID string `json:"MessageID"`
		UserID    string `json:"UserID"`
	}{
		MessageID: messageID,
		UserID:    userId,
	})
	if err != nil {
		return nil, err
	}
	hreq.SetHeader("apikey", apikey)
	hreq.SetHeader("Content-Type", "application/json")
	hreq.SetRequestURI(hiAgentConfig.BaseUrl + "/get_message_info")
	hreq.SetBody(body)
	hreq.SetMethod("POST")

	resp := &protocol.Response{}
	err = hcli.DoTimeout(c, hreq, resp, DefaultTimeout)

	if resp.StatusCode() == 200 && len(resp.Body()) > 0 {
		msg := &struct {
			MessageInfo *discourseHertz.DetailMessage
		}{}
		_ = utils.JsonUnmarshal(resp.Body(), msg)
		return (*msg).MessageInfo, nil
	}

	return nil, err
}

func GetTccKeyByStage(ctx context.Context, stage string) (string, error) {
	switch stage {
	case "intention", "normal":
		return getApiKey(TCC_KEY_INTENTION)
	case "diagnose":
		return getApiKey(TCC_KEY_DIAGNOSE)
	case "summary", "recommend":
		return getApiKey(TCC_KEY_SUMMARY)
	}
	logs.CtxError(ctx, "%s : stage not found", stage)
	return "", errors.New("stage not found")
}

func handlerSuggest(ctx context.Context, session, userID, query, reQuery, productID string) (*discourseHertz.SSEMessage, error) {
	// 更新变量
	err := UpdateAgentVar(ctx, &UpdateStruct{
		Session: session,
		Key:     TCC_KEY_SUMMARY,
		UserID:  userID,
		Inputs:  map[string]interface{}{"report": query, "requery": reQuery, "product_id": productID},
	})

	if err != nil {
		logs.CtxError(ctx, "Error from SSE: %v", err)
		return nil, err
	}

	suggest := ChatV2Block(ctx, &ChatV2Struct{
		Query:   "请调用【智能总结】工作流",
		Session: session,
		UserID:  userID,
		Key:     TCC_KEY_SUMMARY,
	})

	if suggest == "" {
		logs.CtxError(ctx, "suggest err:%v", err)
		return nil, err
	}

	suggestMsg := &discourseHertz.SSEMessage{}

	if err = utils.JsonUnmarshalString(suggest, suggestMsg); err != nil {
		logs.CtxError(ctx, "suggest trans error: %v", err)
		return nil, err
	}

	return suggestMsg, nil
}

func handlerRecommend(ctx context.Context, session, userID, query, productID string) (*discourseHertz.SSEMessage, error) {

	err := UpdateAgentVar(ctx, &UpdateStruct{
		Session: session,
		Key:     TCC_KEY_SUMMARY,
		UserID:  userID,
		Inputs:  map[string]interface{}{"reason": query, "report": " ", "requery": " ", "product_id": productID},
	})

	if err != nil {
		return nil, err
	}

	recommend := ChatV2Block(ctx, &ChatV2Struct{
		Query:   "请调用【智能推荐】工作流",
		Session: session,
		UserID:  userID,
		Key:     TCC_KEY_SUMMARY,
	})

	if recommend == "" {
		logs.CtxError(ctx, "recommend err:%v", err)
		return nil, err
	}

	recommendMsg := &discourseHertz.SSEMessage{}

	if err = utils.JsonUnmarshalString(recommend, recommendMsg); err != nil {
		logs.CtxError(ctx, "recommend trans error: %v", err)
		return nil, err
	}

	return recommendMsg, nil
}

// FeedbackRequest 回答反馈评价（赞或踩）
// likeType 1 赞 -1 踩 0 取消
func FeedbackRequest(c context.Context, stage, messageID, userId string, likeType int) error {

	hcli, err := GetHttpClient()

	if err != nil {
		logs.CtxError(c, "[discourse/FeedbackRequest] GetHttpClient err:%v", err)
		return err
	}
	defer func() {
		PutHttpClient(hcli)
	}()

	apiKey, err := GetTccKeyByStage(c, stage)
	if err != nil {
		return err
	}

	hreq := &protocol.Request{}
	body, err := utils.JsonMarshalToBytes(&FeedbackReq{
		MessageID: messageID,
		UserId:    userId,
		LikeType:  likeType,
	})
	if err != nil {
		return err
	}
	hreq.SetHeader("apikey", apiKey)
	hreq.SetHeader("Content-Type", "application/json")
	hreq.SetRequestURI(hiAgentConfig.BaseUrl + "/feedback")
	hreq.SetBody(body)
	hreq.SetMethod("POST")

	resp := &protocol.Response{}
	err = hcli.DoTimeout(c, hreq, resp, DefaultTimeout)
	if err != nil || resp == nil {
		return err
	}
	if resp.StatusCode() == 200 {
		return nil
	} else {
		logs.CtxError(c, "[discourse/FeedbackRequest] FeedbackRequest Failed Status Code: %d,body:%v", resp.StatusCode(), string(resp.Body()))
		return errors.New(fmt.Sprintf("HiAgent Feedback Request Failed Status Code: %d", resp.StatusCode()))
	}
}
