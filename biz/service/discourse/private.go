package discourse

import (
	"code.byted.org/middleware/hertz/pkg/app"
	"code.byted.org/volcengine-support/cloud-sherlock/biz/dal"
	"code.byted.org/volcengine-support/cloud-sherlock/biz/errorcode"
	diagnoseTaskRunSvc "code.byted.org/volcengine-support/cloud-sherlock/biz/service/diagnose_task_run"
	diagnoseTaskV2Svc "code.byted.org/volcengine-support/cloud-sherlock/biz/service/diagnose_task_v2"
	"code.byted.org/volcengine-support/cloud-sherlock/gen/gorm/model"
	"code.byted.org/volcengine-support/cloud-sherlock/gen/hertz/diagnose_task_run"
	"code.byted.org/volcengine-support/cloud-sherlock/gen/hertz/diagnose_task_v2"
	discourseHertz "code.byted.org/volcengine-support/cloud-sherlock/gen/hertz/discourse"
	"code.byted.org/volcengine-support/cloud-sherlock/gen/hertz/product_category"
	"code.byted.org/volcengine-support/cloud-sherlock/pkg/rds_redis"
	"code.byted.org/volcengine-support/cloud-sherlock/pkg/sso"
	"code.byted.org/volcengine-support/cloud-sherlock/utils"
	"context"
	"errors"
	"github.com/hertz-contrib/sse"
	"github.com/samber/lo"
	"gorm.io/gorm"
	"strconv"
	"time"
)

type Suggest struct {
	query, reQuery, productID string
	session                   *discourseHertz.SessionRedisObj
	user                      *sso.UserInfo
}

// 表单抽取信息补全
func (s *Service) form(ctx context.Context, sseMsgStr string) (*discourseHertz.SSEMessage, error) {
	logger := s.logger.WithFunc("handlerForm")
	start := time.Now()
	// 查询资源数据
	sseMsg := &discourseHertz.SSEMessage{}
	if err := utils.JsonUnmarshalString(sseMsgStr, sseMsg); err != nil {
		logger.CtxError(ctx, "Error from SSE: %v", err)
		return nil, err
	}
	formMsg := discourseHertz.FromMsgResp{}
	if err := utils.JsonUnmarshalString(sseMsg.Answer.(string), &formMsg); err != nil {
		logger.CtxError(ctx, "Error from SSE: %v", err)
		return nil, err
	}
	product, err := getAgentProductInfo(ctx, formMsg.Function)
	if err != nil {
		logger.CtxError(ctx, "Error from SSE: %v", err)
		return nil, err
	}

	formMsg.Id = product.ID

	logger.CtxInfo(ctx, "handlerForm total time: %v", time.Since(start).Milliseconds())

	sseMsg.Answer = utils.JsonToString(formMsg)

	return sseMsg, nil
}

// AI 总结
func (s *Service) summary(ctx context.Context, req *Suggest) (*[]*discourseHertz.SSEMessage, error) {
	start := time.Now()

	logger := s.logger.WithFunc("handleAISummary")

	// 更新变量
	suggest, err := handlerSuggest(ctx, *req.session.SummarySession, req.user.Email, req.query, req.reQuery, req.productID)

	if err != nil {
		s.logger.CtxError(ctx, "Error from SSE: %v", err)
		return nil, err
	}

	recommend, err := handlerRecommend(ctx, *req.session.SummarySession, req.user.Email, suggest.Answer.(string), req.productID)

	if err != nil {
		logger.CtxError(ctx, "Error from SSE: %v", err)
		return nil, err
	}

	logger.CtxInfo(ctx, "handleAISummary total time: %v", time.Since(start).Milliseconds())

	summary := &[]*discourseHertz.SSEMessage{suggest, recommend}

	return summary, nil
}

// agent自建诊断
func (s *Service) agentDiagnoseTask(ctx context.Context, c *app.RequestContext, req *discourseHertz.CreateAgentDiagnoseTaskReq) {
	start := time.Now()
	logger := s.logger.WithFunc("CreateAgentDiagnoseTask")
	stream := makeServerSSE(c)
	user, err := getUserInfo(req.Token)

	if err != nil {
		sendErrorEvent(stream, "获取用户信息失败，请检查网络", err)
		return
	}

	defer func() {
		if pan := recover(); pan != nil {
			printStackRunTimeError(ctx, pan)
		}
	}()

	session, err := getSession(ctx, user, req.SessionID)

	if err != nil {
		sendErrorEvent(stream, "获取用户信息失败，请检查网络", err)
		return
	}

	// 创建通道
	done := make(chan struct{}, 1)
	data := make(chan string, 1)

	defer func() {
		close(data)
		close(done)
		if pan := recover(); pan != nil {
			printStackRunTimeError(ctx, pan)
		}
	}()

	// 发起sse请求
	go func() {
		defer func() {
			select {
			case done <- struct{}{}:
			default:

			}
		}()

		// 自建诊断需要拦截所有 message.answer包
		ChatV2(ctx, &ChatV2Struct{
			Query:     req.ReQuery,
			Session:   *session.DiagnoseSession,
			UserID:    user.Email,
			Key:       TCC_KEY_DIAGNOSE,
			Data:      data,
			Stream:    stream,
			NeedBlock: true,
		})
	}()

	// 处理事件循环
	for {
		select {
		// 开始总结
		case dataMsg := <-data:
			sendCompletionEvent(stream)
			sendReportLoadingEvent(stream)
			logger.CtxInfo(ctx, "dataMsg : %v", dataMsg)
			if summaryResult, err := s.aReport(ctx, req.ReQuery.(*discourseHertz.RunRequest), dataMsg, session, user, true); err != nil {
				logger.CtxError(ctx, "Error from SSE: %v", err)
				sendErrorEvent(stream, "报告生成错误，请稍后再试", err)
				return
			} else {
				sendNormalEvent(stream, REPORT, (*summaryResult)[0])
				sendReportLoadingEvent(stream)
				time.Sleep(1 * time.Second)
				sendNormalEvent(stream, REPORT, (*summaryResult)[1])
			}

		case <-done:
			logger.CtxInfo(ctx, "CreateAgentDiagnoseTask cost time: %v", time.Since(start))
			sendCompletionEvent(stream)
			return
		}
	}
}

// 中台诊断
func (s *Service) diagnoseTask(ctx context.Context, user *sso.UserInfo, session *discourseHertz.SessionRedisObj, req *discourseHertz.CreateDiagnoseTaskReq) (*int64, error) {
	logger := s.logger.WithFunc("CreateDiagnoseTask")

	// 是否存在未完成且已经经过 5 分钟的中台诊断任务
	if task, err := dal.QuerySessionRunningTask(ctx, session.ID); err != nil {
		if !errors.Is(err, gorm.ErrRecordNotFound) {
			logger.CtxError(ctx, "QuerySessionRunningTask err:%v", err)
			return nil, err
		}
	} else if task != nil && *task.Status != "finish" && time.Unix(*task.DiagnoseStartTime, 0).After(time.Now().Add(-5*time.Minute)) {
		return nil, errorcode.ErrRunTaskExist
	}

	createDiagnoseTaskV2Req := &diagnose_task_v2.CreateDiagnoseTaskV2Req{
		Name:               req.TicketID,
		DiagnoseTemplateID: req.DiagnoseTemplateID,
		DiagnoseItemIDs:    req.DiagnoseItemIDs,
		CreateUserID:       req.CreateUserID,
		ProductCategoryIDs: req.ProductCategoryIDs,
		Dimension:          req.Dimension,
		TicketID:           lo.ToPtr(req.TicketID),
		DiagnoseType:       req.DiagnoseType,
		Origin:             lo.ToPtr(diagnoseTaskV2Svc.DiagnoseTaskOriginBot),
	}

	// 创建诊断任务
	diagnoseTaskV2ID, err := diagnoseTaskV2Svc.GetDiagnoseTaskV2Service().CreateDiagnoseTask(ctx, *createDiagnoseTaskV2Req)

	if err != nil {
		logger.CtxError(ctx, "CreateDiagnoseTaskV2 err:%v", err)
		return nil, err
	}

	//RunDiagnoseTask
	runDiagnoseTaskReq := &diagnose_task_run.RunDiagnoseTaskReq{
		DiagnoseTaskID:    diagnoseTaskV2ID,
		RunUserID:         user.Email,
		DiagnoseResources: req.DiagnoseResources,
		DiagnoseStartTime: req.DiagnoseStartTime,
		DiagnoseEndTime:   req.DiagnoseEndTime,
		Name:              lo.ToPtr(req.TicketID),
		AccountID:         req.AccountID,
	}

	// 执行诊断任务
	result, err := diagnoseTaskRunSvc.GetDiagnoseTaskRunService().RunDiagnoseTask(ctx, runDiagnoseTaskReq)

	if err != nil {
		logger.CtxError(ctx, "RunDiagnoseTaskV2 err:%v", err)
		return nil, err
	}

	data := &model.DiscourseMessage{
		SessionID: session.ID,
		Type:      lo.ToPtr(DIAGNOSETASK),
		TaskID:    lo.ToPtr(diagnoseTaskV2ID),
		TaskRunID: lo.ToPtr(result.DiagnoseTaskRunID),
		StartDate: time.Now(),
		Requery:   &req.ReQuery,
	}

	// 更新记录
	if err = dal.CreateDiscourseMessage(ctx, data); err != nil {
		logger.CtxError(ctx, "CreateDiagnoseTask err:%v", err)
		return nil, err
	}

	return &result.DiagnoseTaskRunID, nil
}

// 保底
func (s *Service) backStop(ctx context.Context, stream *sse.Stream, query interface{}, session *discourseHertz.SessionRedisObj, user *sso.UserInfo) {
	defer func() {
		err := UpdateAgentVar(ctx, &UpdateStruct{
			Session: *session.IntentionSession,
			Key:     TCC_KEY_INTENTION,
			UserID:  user.Email,
			Inputs:  map[string]interface{}{"is_qa": ""},
		})
		if err != nil {
			s.logger.CtxError(ctx, "Error from SSE: %v", err)
			return
		}
	}()

	// 更新变量
	err := UpdateAgentVar(ctx, &UpdateStruct{
		Session: *session.IntentionSession,
		Key:     TCC_KEY_INTENTION,
		UserID:  user.Email,
		Inputs:  map[string]interface{}{"is_qa": "问答"},
	})
	if err != nil {
		s.logger.CtxError(ctx, "Error from SSE: %v", err)
		sendErrorEvent(stream, "处理过程中发生错误", err)
		return
	}

	// 发起sse请求
	ChatV2(ctx, &ChatV2Struct{
		Session:   *session.IntentionSession,
		UserID:    user.Email,
		Key:       TCC_KEY_INTENTION,
		Query:     query,
		Stream:    stream,
		NeedBlock: false,
	})

	sendCompletionEvent(stream)
}

func (s *Service) report(ctx context.Context, DiagnoseTaskRunID int64, session *discourseHertz.SessionRedisObj, userInfo *sso.UserInfo, update bool) (*[]*discourseHertz.SSEMessage, error) {
	logger := s.logger.WithFunc("report")

	message, err := dal.QueryDiscourseMessageSummary(ctx, DiagnoseTaskRunID)

	if err != nil {
		logger.CtxError(ctx, "QueryDiscourseMessageSummary err:%v", err)
		return nil, err
	}

	// 已经总结过
	if message.Summary != nil {
		return lo.ToPtr([]*discourseHertz.SSEMessage{{
			Status:      "stop",
			CreatedTime: message.StartDate.Unix(),
		}}), nil
	} else if message.StartDate.Before(time.Now().Add(-5 * time.Minute)) {
		return lo.ToPtr([]*discourseHertz.SSEMessage{{
			Status:      "close",
			CreatedTime: message.StartDate.Unix(),
		}}), nil
	}

	status, err := dal.QueryDiagnoseTaskRunStatusByID(ctx, &DiagnoseTaskRunID)

	if err != nil {
		return nil, err
	}

	if status != "finish" {
		taskRunSummary, err := diagnoseTaskRunSvc.GetDiagnoseTaskRunService().QueryDiagnoseTaskRunSummary(ctx, &diagnose_task_run.QueryDiagnoseTaskRunSummaryReq{
			DiagnoseTaskRunID: DiagnoseTaskRunID,
		})
		if err != nil {
			return nil, err
		}
		return lo.ToPtr([]*discourseHertz.SSEMessage{
			{
				Status:      status,
				TaskRunID:   DiagnoseTaskRunID,
				Progress:    taskRunSummary.Progress,
				CreatedTime: message.StartDate.Unix(),
			},
		}), nil
	}

	// 分布式锁
	lock, err := rds_redis.GetLock(strconv.FormatInt(DiagnoseTaskRunID, 10), time.Minute)

	if err != nil {
		return nil, err
	}
	// 已经触发了总结
	if !lock {
		return lo.ToPtr([]*discourseHertz.SSEMessage{{
			Status:      "stop",
			CreatedTime: message.StartDate.Unix(),
		}}), nil
	}

	defer func() {
		rds_redis.DeleteLock(strconv.FormatInt(DiagnoseTaskRunID, 10))
	}()

	// 再次探测是否并发
	message, err = dal.QueryDiscourseMessageSummary(ctx, DiagnoseTaskRunID)
	if err != nil {
		return nil, err
	}

	// 已经总结过
	if message.Summary != nil {
		return lo.ToPtr([]*discourseHertz.SSEMessage{{
			Status:      "stop",
			CreatedTime: message.StartDate.Unix(),
		}}), nil
	}

	// 查询诊断报告概况信息
	taskDetail, err := s.queryDiagnoseTaskRunDetailBot(ctx,
		&diagnose_task_run.QueryDiagnoseTaskRunDetailReq{
			DiagnoseTaskRunID: DiagnoseTaskRunID,
			Action:            "",
		},
	)

	if err != nil {
		logger.CtxError(ctx, "QueryDiagnoseTaskRunDetail err:%v", err)
		return nil, err
	}

	// 构建总结查询 req
	product := taskDetail.Information.ProductCategory
	instances := taskDetail.Information.Instances
	// 防止报告污染
	taskDetail.Information.ProductCategory = nil
	taskDetail.Information.Instances = nil

	summaryReq := &discourseHertz.SummaryReq{
		Information: taskDetail.Information,
	}

	for key := range taskDetail.DiagnoseTaskRunByteFlow.DiagnoseItemStatusList {
		itemd, err := diagnoseTaskRunSvc.GetDiagnoseTaskRunService().QueryDiagnoseTaskRunItemDetail(
			ctx, &diagnose_task_run.QueryDiagnoseTaskRunItemDetailReq{
				DiagnoseTaskRunID: DiagnoseTaskRunID,
				DiagnoseItemID:    key,
				DiagnoseResultLevel: []string{
					"critical",
					"error",
					"warning",
					"info",
				},
				PageSize:   lo.ToPtr(int32(999)),
				PageNumber: lo.ToPtr(int32(1)),
			},
		)
		if err != nil {
			return nil, err
		}
		summaryReq.Items = append(summaryReq.Items, itemd)
	}

	summaryReqStr := doubleEncodeJson(summaryReq)

	// 报告总结
	summary, err := s.summary(ctx, &Suggest{
		query:     summaryReqStr,
		reQuery:   *message.Requery,
		session:   session,
		user:      userInfo,
		productID: (*product)[0].SubProduct,
	})

	if err != nil || (summary == nil || len(*summary) == 0) {
		return nil, errors.New("诊断报告失败")
	}

	// 关联总结报告
	_ = dal.UpdateDiscourseMessage(ctx, &model.DiscourseMessage{
		TaskRunID: &DiagnoseTaskRunID,
		Summary:   &(*summary)[0].ID, // 总结 ID
		Recommend: &(*summary)[1].ID, // 推荐 ID
	})

	suggestMsg := (*summary)[0]
	suggestMsg.Status = "finish"
	suggestMsg.TaskRunID = DiagnoseTaskRunID
	suggestJson := &discourseHertz.SuggestResp{}
	err = utils.JsonUnmarshalString(suggestMsg.Answer.(string), suggestJson)
	if err != nil {
		return nil, err
	}
	// 查询产品信息
	{
		taskDetail.Information.ProductCategory = product
		taskDetail.Information.Instances = instances
	}
	suggestJson.Information = taskDetail.Information

	suggestMsg.Answer = utils.JsonToString(suggestJson)

	// 更新变量
	if update {
		if err = UpdateAgentVar(ctx, &UpdateStruct{
			Session: *session.IntentionSession,
			Key:     TCC_KEY_INTENTION,
			UserID:  userInfo.Email,
			Inputs:  map[string]interface{}{"report": utils.JsonToString(suggestMsg.Answer)},
		}); err != nil {
			logger.CtxError(ctx, "UpdateAgentVar err:%v", err)
		}
	}

	return summary, nil
}

func (s *Service) aReport(ctx context.Context, reQuery *discourseHertz.RunRequest, agentDiagnoseDetail string, session *discourseHertz.SessionRedisObj, userInfo *sso.UserInfo, update bool) (*[]*discourseHertz.SSEMessage, error) {

	logger := s.logger.WithFunc("report")

	// 查询PID
	productInfo, err := getAgentProductInfo(ctx, reQuery.Function)
	if err != nil {
		logger.CtxError(ctx, "GetAgentProductInfo err:%v", err)
	}

	// 报告总结
	summary, err := s.summary(ctx, &Suggest{
		query:     agentDiagnoseDetail,
		reQuery:   reQuery.Query,
		session:   session,
		user:      userInfo,
		productID: productInfo.ProductID,
	})

	if err != nil || (summary == nil || len(*summary) == 0) {
		logger.CtxError(ctx, "agent diagnose summary error:%v", err)
		return nil, errors.New("诊断报告失败")
	}

	// 更新变量
	if update {
		if err = UpdateAgentVar(ctx, &UpdateStruct{
			Session: *session.IntentionSession,
			Key:     TCC_KEY_INTENTION,
			UserID:  userInfo.Email,
			Inputs:  map[string]interface{}{"report": utils.JsonToString((*summary)[0].Answer)},
		}); err != nil {
			logger.CtxError(ctx, "UpdateAgentVar err:%v", err)
		}
	}

	return summary, nil
}

// 根据诊断任务运行 ID 查询执行结果详情 bot侧复制版
func (s *Service) queryDiagnoseTaskRunDetailBot(ctx context.Context, req *diagnose_task_run.QueryDiagnoseTaskRunDetailReq) (*discourseHertz.QueryDiagnoseTaskRunDetailResultBot, error) {
	logger := s.logger.WithFunc("QueryDiagnoseTaskRunDetail")

	// 根据诊断任务运行 ID 查询任务运行记录
	taskRun, err := dal.QueryDiagnoseTaskRunByID(ctx, req.DiagnoseTaskRunID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			logger.CtxInfo(ctx, "Diagnose task run record not found for ID: %d", req.DiagnoseTaskRunID)
			return nil, err
		}
		logger.CtxError(ctx, "QueryDiagnoseTaskRunByID err:%v", err)
		return nil, err
	}

	// 构建返回的诊断任务运行详情对象
	diagnoseTaskRunDetail := &discourseHertz.DiagnoseTaskRunBot{
		Id:                   taskRun.ID,
		DiagnoseTaskID:       taskRun.DiagnoseTaskID,
		Name:                 taskRun.Name,
		Status:               taskRun.Status,
		RunUserID:            taskRun.CreateUserID,
		DiagnoseType:         taskRun.DiagnoseType,
		TicketID:             taskRun.TicketID,
		DiagnoseStartTime:    taskRun.DiagnoseStartTime,
		DiagnoseEndTime:      taskRun.DiagnoseEndTime,
		Origin:               taskRun.Origin,
		DiagnoseTemplateName: taskRun.DiagnoseTemplateName,
	}

	// 处理开始时间
	if taskRun.StartTime != nil {
		startTime, err := utils.TimeToString(*taskRun.StartTime, "")
		if err != nil {
			logger.CtxError(ctx, "TimeToString err:%v", err)
			return nil, err
		}
		diagnoseTaskRunDetail.StartTime = lo.ToPtr(startTime)
	}

	// 处理结束时间
	if taskRun.EndTime != nil {
		endTime, err := utils.TimeToString(*taskRun.EndTime, "")
		if err != nil {
			logger.CtxError(ctx, "TimeToString err:%v", err)
			return nil, err
		}
		diagnoseTaskRunDetail.EndTime = lo.ToPtr(endTime)
	}

	// 处理反馈信息
	if taskRun.FeedbackID != nil {
		diagnoseTaskRunDetail.Feedback = lo.ToPtr(true)
	} else {
		diagnoseTaskRunDetail.Feedback = lo.ToPtr(false)
	}

	// 计算诊断结果等级
	level, err := diagnoseTaskRunSvc.GetLevelByTaskRunID(ctx, taskRun.ID)
	if err != nil {
		logger.CtxError(ctx, "GetLevelByTaskRunID err:%v", err)
		return nil, err
	}
	diagnoseTaskRunDetail.DiagnoseResultLevel = level

	// 诊断项运行状态查询
	diagnoseItemStatusList, err := diagnoseTaskRunSvc.TransferStringToDiagnoseItemStatus(taskRun.DiagnoseItemStatus)
	if err != nil {
		logger.CtxError(ctx, "TransferStringToDiagnoseItemStatus err:%v", err)
		return nil, err
	}
	//计算诊断项结果等级
	for i, status := range diagnoseItemStatusList {
		itemLevel, err := diagnoseTaskRunSvc.GetLevelByTaskRunIDItemID(ctx, taskRun.ID, status.DiagnoseItem.Id)
		if err != nil {
			logger.CtxError(ctx, "GetLevelByTaskRunIDItemID err:%v", err)
			return nil, err
		}
		diagnoseItemStatusList[i].DiagnoseResultLevel = itemLevel
	}

	// 查询资源 ID
	resources, err := diagnoseTaskRunSvc.TransferStringToDiagnoseResource(taskRun.Resources)
	diagnoseTaskRunDetail.Instances = &resources[0].Instances
	productCategory, err := diagnoseTaskRunSvc.GetProductCategoryByID(ctx, resources[0].ProductCategoryID)
	if err == nil {
		diagnoseTaskRunDetail.ProductCategory = &[]*product_category.ProductCategory{productCategory}
	}

	////查询activityDiagnose
	//_, err = TransferStringToActivityDiagnose(taskRun.ActivityDiagnoses)
	//if err != nil {
	//	logger.CtxError(ctx, "TransferStringToActivityDiagnose err:%v", err)
	//	return nil, err
	//}
	return &discourseHertz.QueryDiagnoseTaskRunDetailResultBot{
		Information: diagnoseTaskRunDetail,
		DiagnoseTaskRunByteFlow: &diagnose_task_run.DiagnoseTaskRunByteFlow{
			StateMachineInfo:       taskRun.StateMachineInfo,
			DiagnoseItemStatusList: diagnoseItemStatusList,
		},
	}, nil
}
