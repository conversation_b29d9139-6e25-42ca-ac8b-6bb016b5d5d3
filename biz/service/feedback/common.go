package feedback

import (
	"code.byted.org/volcengine-support/cloud-sherlock/biz/dal"
	"code.byted.org/volcengine-support/cloud-sherlock/gen/hertz/feedback"
	"code.byted.org/volcengine-support/cloud-sherlock/utils"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/samber/lo"
	"gorm.io/gorm"
)

const (
	StatusFeedbackPending    = "pending"
	StatusFeedbackDone       = "done"
	StatusFeedbackProcessing = "processing"
	StatusFeedbackNoNeed     = "no_need"

	StageIntention = "intention"
	StageDiagnose  = "diagnose"
	StageRecommend = "recommend"
	StageNormal    = "normal"
	StageSummary   = "summary"
)

// TransferProblemItemsToString 转换ProblemItems为string
func TransferProblemItemsToString(problemItems []*feedback.ProblemItem) (*string, error) {
	if len(problemItems) == 0 {
		return nil, nil
	}
	problemItemsByte, err := json.Marshal(problemItems)
	if err != nil {
		return nil, err
	}
	return lo.ToPtr(string(problemItemsByte)), nil
}

// TransferStringToProblemItems 转换string为ProblemItems
func TransferStringToProblemItems(problemItems *string) ([]*feedback.ProblemItem, error) {
	if problemItems == nil {
		return nil, nil
	}
	problemItemsList := make([]*feedback.ProblemItem, 0)
	err := json.Unmarshal([]byte(*problemItems), &problemItemsList)
	if err != nil {
		return nil, err
	}
	return problemItemsList, nil
}

func GetProductCategoryNameBySubProduct(ctx context.Context, subProduct *string) (string, error) {
	if subProduct == nil {
		return "", nil
	}
	productCategory, err := dal.QueryProductCategoryBySubProduct(ctx, *subProduct)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return *subProduct, nil
		}
	}
	return fmt.Sprintf("%s/%s/%s", productCategory.ProductCn, productCategory.SubProductCn, productCategory.ResourceTypeCn), nil
}

func GetFeedbackLabelName(ctx context.Context, feedbackLabelIDs *string) ([]string, error) {
	if feedbackLabelIDs == nil {
		return nil, nil
	}
	feedbackLabelIDList, err := utils.TransferStringToArrayInt(feedbackLabelIDs)
	if err != nil {
		return nil, err
	}
	labelNames := make([]string, 0)
	feedbackLabels, err := dal.QueryFeedbackLabelsByIDs(ctx, feedbackLabelIDList)
	if err != nil {
		return nil, err
	}
	for _, feedbackLabel := range feedbackLabels {
		labelNames = append(labelNames, feedbackLabel.LabelName)
	}
	return labelNames, nil
}

func TransferStringToEndHandleResult(endHandleResults *string) ([]*feedback.EndHandleResult, error) {
	if endHandleResults == nil {
		return nil, nil
	}
	endHandleResultsList := make([]*feedback.EndHandleResult, 0)
	err := json.Unmarshal([]byte(*endHandleResults), &endHandleResultsList)
	if err != nil {
		return nil, err
	}
	return endHandleResultsList, nil
}

func TransferEndHandleResultToString(endHandleResults []*feedback.EndHandleResult) (*string, error) {
	if len(endHandleResults) == 0 {
		return nil, nil
	}
	endHandleResultsByte, err := json.Marshal(endHandleResults)
	if err != nil {
		return nil, err
	}
	return lo.ToPtr(string(endHandleResultsByte)), nil
}

func GetHandleUserIDsByLabelIDs(ctx context.Context, labelIDs []int64, subProduct string) ([]string, error) {
	feedbackLabels, err := dal.QueryFeedbackLabelsByIDs(ctx, labelIDs)
	if err != nil {
		return nil, err
	}
	handleUserIDs := make([]string, 0)
	for _, feedbackLabel := range feedbackLabels {
		userIDsMap, err := utils.TransferStringToMapStringArray(feedbackLabel.HandleUserIds)
		if err != nil {
			return nil, err
		}
		switch feedbackLabel.Stage {
		case StageDiagnose, StageSummary:
			if userIDs, ok := userIDsMap[subProduct]; ok {
				handleUserIDs = append(handleUserIDs, userIDs...)
			} else {
				handleUserIDs = append(handleUserIDs, userIDsMap["default"]...)
			}
		default:
			handleUserIDs = append(handleUserIDs, userIDsMap["default"]...)
		}
	}
	return lo.Uniq(handleUserIDs), nil
}
