package feedback

import (
	"code.byted.org/volcengine-support/cloud-sherlock/biz/service/discourse"
	"code.byted.org/volcengine-support/cloud-sherlock/gen/hertz/common"
	"code.byted.org/volcengine-support/cloud-sherlock/pkg/sso"
	"code.byted.org/volcengine-support/cloud-sherlock/pkg/tcc"
	"context"
	"errors"
	"sync"
	"time"

	"code.byted.org/volcengine-support/cloud-sherlock/biz/dal"
	"code.byted.org/volcengine-support/cloud-sherlock/biz/errorcode"
	"code.byted.org/volcengine-support/cloud-sherlock/gen/gorm/model"
	"code.byted.org/volcengine-support/cloud-sherlock/gen/hertz/feedback"
	"code.byted.org/volcengine-support/cloud-sherlock/utils"
	"github.com/samber/lo"
	"gorm.io/gorm"
)

var (
	service *Service
	once    sync.Once
)

const (
	defaultPageSize = int32(10)
	defaultPageNum  = int32(1)
)

type Service struct {
	logger *utils.ModuleLogger
}

func GetFeedbackService() *Service {
	once.Do(func() {
		service = &Service{
			logger: utils.NewModuleLogger("FeedbackService"),
		}
	})
	return service
}

func (s *Service) SubmitDiagnoseTaskFeedback(ctx context.Context, feedback feedback.SubmitDiagnoseTaskFeedbackReq) error {
	logger := s.logger.WithFunc("SubmitDiagnoseTaskFeedback")
	// 2、校验是否已经提交过
	feedbackRecord, err := dal.FeedbackFindByDiagnoseTaskRunID(ctx, feedback.DiagnoseTaskRunID)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		logger.CtxError(ctx, "FeedbackFindByDiagnoseTaskRunID err:%v", err)
		return err
	}
	if feedbackRecord != nil {
		logger.CtxWarn(ctx, "FeedbackRecord already exist")
		return errorcode.ErrDataAlreadyExist
	}
	// 4、保存反馈记录
	problemItems, err := TransferProblemItemsToString(feedback.ProblemItems)
	if err != nil {
		logger.CtxError(ctx, "TransferStringToProblemItems err:%v", err)
		return err
	}
	// 新增反馈时间字段，赋值为当前时间
	feedbackTime := time.Now()
	feedbackRecord = &model.Feedback{
		DiagnoseTaskID:    feedback.DiagnoseTaskID,
		DiagnoseTaskRunID: feedback.DiagnoseTaskRunID,
		Resolved:          feedback.Resolved,
		ProblemItems:      problemItems,
		FeedbackUserID:    feedback.FeedbackUserID,
		Description:       feedback.Description,
		// 添加反馈时间字段到结构体
		FeedbackTime: lo.ToPtr(feedbackTime),
	}
	//通过任务ID查找任务创建人
	task, err := dal.QueryDiagnoseTaskRunByID(ctx, feedback.DiagnoseTaskRunID)
	if err != nil {
		logger.CtxError(ctx, "QueryDiagnoseTaskRunByID err:%v", err)
		return err
	}
	feedbackRecord.DiagnoseRunUserID = task.CreateUserID
	err = dal.CreateFeedback(ctx, feedbackRecord)
	if err != nil {
		logger.CtxError(ctx, "FeedbackSave err:%v", err)
		return err
	}
	err = dal.DiagnoseTaskRunUpdateFeedbackIDByID(ctx, feedback.DiagnoseTaskRunID, lo.ToPtr(feedbackRecord.ID))
	if err != nil {
		logger.CtxError(ctx, "DiagnoseTaskUpdate err:%v", err)
		return err
	}
	return nil
}

func (s *Service) SubmitFeedback(ctx context.Context, req *feedback.SubmitFeedbackReq) (*feedback.SubmitFeedbackResult, error) {
	logger := utils.NewModuleLogger("FeedbackService").WithFunc("SubmitFeedback")
	// 2、校验是否已经提交过
	feedbackRecord, err := dal.ValidFeedbackV2FindBySessionIDMessageID(ctx, req.SessionID, req.MessageID)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		logger.CtxError(ctx, "FeedbackV2FindBySessionIDMessageID err:%v", err)
		return nil, err
	}
	var resultID int64
	var likeType int
	var userInfo *sso.UserInfo

	if req.IsLike {
		likeType = 1
	} else {
		likeType = -1
	}
	// 判断是否为取消的操作
	if feedbackRecord != nil && feedbackRecord.IsLike == req.IsLike {
		likeType = 0
	}
	// 调用discourse反馈接口
	err = discourse.FeedbackRequest(ctx, req.Stage, req.MessageID, *req.FeedbackUserID, likeType)
	if err != nil {
		logger.CtxError(ctx, "discourse.FeedbackRequest err:%v", err)
		return nil, err
	}
	// 将之前的反馈记录设置为无效
	if feedbackRecord != nil {
		resultID = feedbackRecord.ID
		err = dal.SetUnValidFeedbackV2(ctx, feedbackRecord.ID)
		if err != nil {
			logger.CtxError(ctx, "SetUnValidFeedbackV2 err:%v", err)
			return nil, err
		}
	}
	if likeType != 0 {

		if userInfo, err = sso.GetUserInfoByTokenFromRedis(req.Token); err != nil {
			logger.CtxError(ctx, "GetUserInfoByTokenFromRedis err:%v", err)
			return nil, err
		}

		feedbackV2 := &model.FeedbackV2{
			SessionID:          req.SessionID,
			MessageID:          req.MessageID,
			Stage:              req.Stage,
			IsLike:             req.IsLike,
			SubProduct:         req.SubProduct,
			PName:              req.PName,
			TicketID:           req.TicketID,
			DiagnoseTaskRunID:  req.DiagnoseTaskRunID,
			ReportLink:         req.ReportLink,
			Description:        req.Description,
			FeedbackTime:       time.Now(),
			FeedbackUserID:     req.FeedbackUserID,
			FeedbackDepartment: &userInfo.Department.Name,
			Status:             StatusFeedbackPending,
			Valid:              true,
		}
		if req.IsLike {
			feedbackV2.Status = StatusFeedbackNoNeed
		} else {
			feedbackLabelIds, err := utils.TransferArrayIntToString(req.FeedbackLabelIDs)
			if err != nil {
				logger.CtxError(ctx, "TransferArrayIntToString err:%v", err)
				return nil, err
			}

			feedbackV2.FeedbackLabelIds = &feedbackLabelIds
			handleUserIds, err := GetHandleUserIDsByLabelIDs(ctx, req.FeedbackLabelIDs, lo.FromPtr(req.SubProduct))
			if err != nil {
				logger.CtxError(ctx, "GetHandleUserIDsByLabelIDs err:%v", err)
				return nil, err
			}

			handleUserIdsStr, err := utils.TransferArrayStringToString(handleUserIds)
			if err != nil {
				logger.CtxError(ctx, "TransferArrayStringToString err:%v", err)
				return nil, err
			}
			feedbackV2.HandleUserIds = &handleUserIdsStr
		}

		//通过sessionID查找title
		session, err := dal.QueryDiscourseBySessionID(ctx, req.SessionID)
		if err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				logger.CtxError(ctx, "QueryDiscourseBySessionID err:%v", err)
				return nil, errorcode.ErrDataNotFound
			}
			logger.CtxError(ctx, "QueryDiscourseBySessionID err:%v", err)
			return nil, err
		}
		feedbackV2.Title = lo.ToPtr(session.Title)
		err = dal.CreateFeedbackV2(ctx, feedbackV2)
		if err != nil {
			logger.CtxError(ctx, "CreateFeedbackV2 err:%v", err)
			return nil, err
		}
		resultID = feedbackV2.ID
	}
	return &feedback.SubmitFeedbackResult{
		Id: lo.ToPtr(resultID),
	}, nil
}

func (s *Service) QueryFeedback(ctx context.Context, req *feedback.QueryFeedbackReq) (*feedback.QueryFeedbackResult, error) {
	logger := s.logger.WithFunc("QueryFeedback")
	filter := &model.FeedbackV2{}
	if req.FeedbackID != nil {
		filter.ID = *req.FeedbackID
	}
	if req.SessionID != nil {
		filter.SessionID = *req.SessionID
	}
	if req.FeedbackUserID != nil {
		filter.FeedbackUserID = req.FeedbackUserID
	}
	if req.TicketID != nil {
		filter.TicketID = req.TicketID
	}
	if req.Status != nil {
		filter.Status = *req.Status
	}
	if req.HandleUserID != nil {
		filter.HandleUserIds = req.HandleUserID
	}
	if req.SubProduct != nil {
		filter.SubProduct = req.SubProduct
	}
	if req.Title != nil {
		filter.Title = req.Title
	}
	if req.Stage != nil {
		filter.Stage = *req.Stage
	}
	if req.PageSize == nil {
		req.PageSize = lo.ToPtr(defaultPageSize)
	}
	if req.PageNumber == nil {
		req.PageNumber = lo.ToPtr(defaultPageNum)
	}
	// 2、查询反馈记录
	feedbackRecords, total, err := dal.FindFeedback(ctx, filter, req.IsLike, req.Valid, req.SortByFeedbackTime, req.SortByHandleStartTime,
		req.SortByHandleEndTime, int(*req.PageNumber), int(*req.PageSize))
	if err != nil {
		logger.CtxError(ctx, "QueryFeedback err:%v", err)
		return nil, err
	}
	// 3、转换为返回结果
	feedbackList := make([]*feedback.Feedback, 0)
	for _, feedbackRecord := range feedbackRecords {
		f := &feedback.Feedback{
			FeedbackID:         feedbackRecord.ID,
			Stage:              feedbackRecord.Stage,
			SessionID:          feedbackRecord.SessionID,
			FeedbackUserID:     feedbackRecord.FeedbackUserID,
			FeedbackDepartment: feedbackRecord.FeedbackDepartment,
			ReportLink:         feedbackRecord.ReportLink,
			TicketID:           feedbackRecord.TicketID,
			Status:             feedbackRecord.Status,
			StartHandleUserID:  feedbackRecord.StartHandleUserID,
			EndHandleUserID:    feedbackRecord.EndHandleUserID,
			Title:              feedbackRecord.Title,
			IsLike:             feedbackRecord.IsLike,
			Valid:              feedbackRecord.Valid,
			Description:        feedbackRecord.Description,
			MessageID:          feedbackRecord.MessageID,
		}
		f.FeedbackTime, err = utils.TimeToString(feedbackRecord.FeedbackTime, "")
		if err != nil {
			logger.CtxError(ctx, "TimeToString err:%v", err)
			return nil, err
		}
		if feedbackRecord.StartHandleTime != nil {
			startHandleTime, err := utils.TimeToString(*feedbackRecord.StartHandleTime, "")
			if err != nil {
				logger.CtxError(ctx, "TimeToString err:%v", err)
				return nil, err
			}
			f.StartHandleTime = &startHandleTime
		}
		if feedbackRecord.EndHandleTime != nil {
			endHandleTime, err := utils.TimeToString(*feedbackRecord.EndHandleTime, "")
			if err != nil {
				logger.CtxError(ctx, "TimeToString err:%v", err)
				return nil, err
			}
			f.EndHandleTime = &endHandleTime
		}
		if feedbackRecord.SubProduct != nil {
			productCategoryName, err := GetProductCategoryNameBySubProduct(ctx, feedbackRecord.SubProduct)
			if err != nil {
				logger.CtxError(ctx, "GetProductCategoryName err:%v", err)
				return nil, err
			}
			f.ProductCategory = &productCategoryName
		} else {
			f.ProductCategory = feedbackRecord.PName
		}

		feedbackLabelNames, err := GetFeedbackLabelName(ctx, feedbackRecord.FeedbackLabelIds)
		if err != nil {
			logger.CtxError(ctx, "GetFeedbackLabelName err:%v", err)
			return nil, err
		}
		f.FeedbackLabelNames = feedbackLabelNames
		f.HandleUserIDs, err = utils.TransferStringToArrayString(feedbackRecord.HandleUserIds)
		if err != nil {
			logger.CtxError(ctx, "TransferStringToArrayString err:%v", err)
			return nil, err
		}
		f.HandleResults, err = TransferStringToEndHandleResult(feedbackRecord.HandleResults)
		if err != nil {
			logger.CtxError(ctx, "TransferStringToEndHandleResult err:%v", err)
			return nil, err
		}
		feedbackList = append(feedbackList, f)
	}
	return &feedback.QueryFeedbackResult{
		FeedbackList: feedbackList,
		Pagination: &common.Pagination{
			PageNumber: *req.PageNumber,
			PageSize:   *req.PageSize,
			TotalCount: total,
		},
	}, nil
}

func (s *Service) QueryFeedbackLabel(ctx context.Context, req *feedback.QueryFeedbackLabelReq) (*feedback.QueryFeedbackLabelResult, error) {
	logger := s.logger.WithFunc("QueryFeedbackLabel")
	filter := &model.FeedbackLabel{}
	if req.Stage != nil {
		filter.Stage = *req.Stage
	}
	if req.Id != nil {
		filter.ID = *req.Id
	}
	if req.Key != nil {
		filter.LabelKey = *req.Key
	}
	if req.Name != nil {
		filter.LabelName = *req.Name
	}
	// 2、查询反馈标签
	feedbackLabels, err := dal.QueryFeedbackLabels(ctx, filter)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errorcode.ErrDataNotFound
		}
		logger.CtxError(ctx, "QueryFeedbackLabels err:%v", err)
		return nil, err
	}
	labels := make([]*feedback.FeedbackLabel, 0)
	for _, feedbackLabel := range feedbackLabels {
		l := &feedback.FeedbackLabel{
			Id:    feedbackLabel.ID,
			Stage: feedbackLabel.Stage,
			Key:   feedbackLabel.LabelKey,
			Name:  feedbackLabel.LabelName,
		}
		l.HandleUserIDs, err = utils.TransferStringToMapStringArray(feedbackLabel.HandleUserIds)
		if err != nil {
			logger.CtxError(ctx, "TransferStringToArrayString err:%v", err)
			return nil, err
		}
		labels = append(labels, l)
	}
	return &feedback.QueryFeedbackLabelResult{
		FeedbackLabelList: labels,
	}, nil
}

func (s *Service) StartHandle(ctx context.Context, req *feedback.StartHandleReq) error {
	logger := s.logger.WithFunc("StartHandle")
	// 1、校验反馈记录是否存在
	feedbackRecord, err := dal.FeedbackV2FindByID(ctx, req.FeedbackID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			logger.CtxError(ctx, "FeedbackV2FindByID err:%v", err)
			return errorcode.ErrDataNotFound
		}
		logger.CtxError(ctx, "FeedbackV2FindByID err:%v", err)
		return err
	}
	//判断处理人是否在处理人列表中
	handleUserIDs, err := utils.TransferStringToArrayString(feedbackRecord.HandleUserIds)
	if err != nil {
		logger.CtxError(ctx, "TransferStringToArrayString err:%v", err)
		return err
	}
	if !utils.ArrayContains(handleUserIDs, lo.FromPtr(req.HandleUserID)) {
		logger.CtxError(ctx, "HandleUserID not in handleUserIDs")
		return errorcode.ErrNoPermission.WithArgs(lo.FromPtr(req.HandleUserID))
	}
	if feedbackRecord.Status != StatusFeedbackPending {
		logger.CtxError(ctx, "FeedbackRecord status is not pending")
		return errorcode.ErrRequestParamInvalid.WithArgs(feedbackRecord.ID)
	}
	feedbackRecord.Status = StatusFeedbackProcessing
	feedbackRecord.StartHandleUserID = req.HandleUserID
	feedbackRecord.StartHandleTime = lo.ToPtr(time.Now())
	err = dal.FeedbackV2Update(ctx, feedbackRecord)
	if err != nil {
		logger.CtxError(ctx, "FeedbackV2Update err:%v", err)
		return err
	}
	return nil
}

func (s *Service) EndHandle(ctx context.Context, req *feedback.EndHandleReq) error {
	logger := s.logger.WithFunc("EndHandle")
	// 1、校验反馈记录是否存在
	feedbackRecord, err := dal.FeedbackV2FindByID(ctx, req.FeedbackID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			logger.CtxError(ctx, "FeedbackV2FindByID err:%v", err)
			return errorcode.ErrDataNotFound
		}
		logger.CtxError(ctx, "FeedbackV2FindByID err:%v", err)
		return err
	}
	//判断处理人是否在处理人列表中
	handleUserIDs, err := utils.TransferStringToArrayString(feedbackRecord.HandleUserIds)
	if err != nil {
		logger.CtxError(ctx, "TransferStringToArrayString err:%v", err)
		return err
	}
	if !utils.ArrayContains(handleUserIDs, lo.FromPtr(req.HandleUserID)) {
		logger.CtxError(ctx, "HandleUserID not in handleUserIDs")
		return errorcode.ErrNoPermission.WithArgs(lo.FromPtr(req.HandleUserID))
	}
	if feedbackRecord.Status != StatusFeedbackProcessing {
		logger.CtxError(ctx, "FeedbackRecord status is not processing")
		return errorcode.ErrRequestParamInvalid.WithArgs(feedbackRecord.ID)
	}
	feedbackRecord.Status = StatusFeedbackDone
	feedbackRecord.EndHandleUserID = req.HandleUserID
	feedbackRecord.EndHandleTime = lo.ToPtr(time.Now())
	feedbackRecord.HandleResults, err = TransferEndHandleResultToString(req.HandleResults)
	if err != nil {
		logger.CtxError(ctx, "TransferEndHandleResultToString err:%v", err)
		return err
	}
	err = dal.FeedbackV2Update(ctx, feedbackRecord)
	if err != nil {
		logger.CtxError(ctx, "FeedbackV2Update err:%v", err)
		return err
	}
	return nil
}

func (s *Service) AddFeedbackLabel(ctx context.Context, req *feedback.AddFeedbackLabelReq) error {
	logger := s.logger.WithFunc("AddFeedbackLabel")
	// 1、校验反馈标签是否存在
	feedbackLabel, err := dal.QueryFeedbackLabelByStageName(ctx, req.Stage, req.Name)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return err
	}
	//if feedbackLabel != nil {
	//	return errorcode.ErrDataAlreadyExist
	//}
	fLabel := &model.FeedbackLabel{
		Stage:     req.Stage,
		LabelKey:  req.Key,
		LabelName: req.Name,
	}
	if req.HandleUserIDs != nil {
		handleUserIDs, err := utils.TransferToMarshalString(req.HandleUserIDs)
		if err != nil {
			logger.CtxError(ctx, "TransferToMarshalString err:%v", err)
			return err
		}
		fLabel.HandleUserIds = &handleUserIDs
	}
	if feedbackLabel != nil {
		err = dal.UpdateFeedbackLabel(ctx, feedbackLabel.ID, fLabel)
	} else {
		err = dal.CreateFeedbackLabel(ctx, fLabel)
	}
	if err != nil {
		logger.CtxError(ctx, "CreateFeedbackLabel err:%v", err)
		return err
	}
	return nil
}

func (s *Service) DeleteFeedbackLabel(ctx context.Context, req *feedback.DeleteFeedbackLabelReq) error {
	logger := s.logger.WithFunc("DeleteFeedbackLabel")
	// 1、校验反馈标签是否存在
	_, err := dal.QueryFeedbackLabelsByIDs(ctx, []int64{req.Id})
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			logger.CtxError(ctx, "FeedbackLabel err:%v", err)
			return errorcode.ErrDataNotFound
		}
		logger.CtxError(ctx, "QueryFeedbackLabel err:%v", err)
		return err
	}
	err = dal.DeleteFeedbackLabelByID(ctx, req.Id)
	if err != nil {
		logger.CtxError(ctx, "DeleteFeedbackLabel err:%v", err)
		return err
	}
	return nil
}

func (s *Service) QueryResultLabels(ctx context.Context) (*feedback.QueryResultLabelsResult, error) {
	logger := s.logger.WithFunc("QueryResultLabels")
	labels, err := dal.QueryFeedbackLabels(ctx, &model.FeedbackLabel{
		Stage: dal.FeedbackLabelResultStage,
	})
	if err != nil {
		logger.CtxError(ctx, "QueryFeedbackLabels err:%v", err)
		return nil, err
	}
	labelNames := make([]string, 0)
	for _, label := range labels {
		labelNames = append(labelNames, label.LabelName)
	}
	return &feedback.QueryResultLabelsResult{
		ResultLabelList: labelNames,
	}, nil
}

func (s *Service) PagePermission(ctx context.Context, req *feedback.PagePermissionReq) error {
	pageUsers := tcc.GetServiceConfig().OperationPagePermission
	if !utils.ArrayContains(pageUsers, lo.FromPtr(req.UserID)) {
		return errorcode.ErrNoPagePermission.WithArgs(lo.FromPtr(req.UserID))
	}
	return nil
}
