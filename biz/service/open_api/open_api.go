package open_api

import (
	"code.byted.org/volcengine-support/cloud-sherlock/biz/dal"
	"code.byted.org/volcengine-support/cloud-sherlock/biz/errorcode"
	DiagnoseItemSvc "code.byted.org/volcengine-support/cloud-sherlock/biz/service/diagnose_item"
	diagnoseTaskRunSvc "code.byted.org/volcengine-support/cloud-sherlock/biz/service/diagnose_task_run"
	diagnoseTaskV2Svc "code.byted.org/volcengine-support/cloud-sherlock/biz/service/diagnose_task_v2"
	DiagnoseTemplateSvc "code.byted.org/volcengine-support/cloud-sherlock/biz/service/diagnose_template"
	DiscourseSvc "code.byted.org/volcengine-support/cloud-sherlock/biz/service/discourse"
	feedbackSvc "code.byted.org/volcengine-support/cloud-sherlock/biz/service/feedback"
	ProductCategorySvc "code.byted.org/volcengine-support/cloud-sherlock/biz/service/product_category"
	"code.byted.org/volcengine-support/cloud-sherlock/gen/gorm/model"
	"code.byted.org/volcengine-support/cloud-sherlock/gen/hertz/diagnose_item"
	"code.byted.org/volcengine-support/cloud-sherlock/gen/hertz/diagnose_result"
	"code.byted.org/volcengine-support/cloud-sherlock/gen/hertz/diagnose_task_run"
	"code.byted.org/volcengine-support/cloud-sherlock/gen/hertz/diagnose_task_v2"
	"code.byted.org/volcengine-support/cloud-sherlock/gen/hertz/diagnose_template"
	discourseHertz "code.byted.org/volcengine-support/cloud-sherlock/gen/hertz/discourse"
	"code.byted.org/volcengine-support/cloud-sherlock/gen/hertz/feedback"
	"code.byted.org/volcengine-support/cloud-sherlock/gen/hertz/openapi"
	"code.byted.org/volcengine-support/cloud-sherlock/gen/hertz/product_category"
	"code.byted.org/volcengine-support/cloud-sherlock/utils"
	"context"
	"fmt"
	"github.com/samber/lo"
	"strings"
	"sync"
)

const (
	defaultPageSize = int64(50)
	defaultPageNum  = int64(1)
)

var (
	service *Service
	once    sync.Once
)

type Service struct {
	logger *utils.ModuleLogger
}

func GetOpenApiService() *Service {
	once.Do(func() {
		service = &Service{
			logger: utils.NewModuleLogger("OpenApiService"),
		}
	})
	return service
}

func (s *Service) ListDiagnoseItem(ctx context.Context, req *openapi.ListDiagnoseItemRequest) (*openapi.ListDiagnoseItemResult, error) {
	logger := s.logger.WithFunc("OpenApi-ListDiagnoseItem")
	result, err := DiagnoseItemSvc.GetDiagnoseItemService().ListDiagnoseItem(ctx, &diagnose_item.ListDiagnoseItemRequest{
		PageNumber:              req.PageNumber,
		PageSize:                req.PageSize,
		DiagnoseItemIDs:         req.DiagnoseItemIDs,
		DiagnoseItemName:        req.DiagnoseItemName,
		Status:                  req.Status,
		ProductCategoryIDs:      req.ProductCategoryIDs,
		AccessResponsiblePerson: req.AccessResponsiblePerson,
	})
	if err != nil {
		logger.CtxError(ctx, "ListDiagnoseItem err:%v", err)
		return nil, err
	}
	return &openapi.ListDiagnoseItemResult{
		DiagnoseItems: result.DiagnoseItems,
		Pagination:    result.Pagination,
	}, nil
}

func (s *Service) ListDiagnoseTemplate(ctx context.Context, req *openapi.ListDiagnoseTemplateRequest) (*openapi.ListDiagnoseTemplateResult, error) {
	logger := s.logger.WithFunc("OpenApi-ListDiagnoseTemplate")
	if req.PageNumber == 0 {
		req.PageNumber = defaultPageNum
	}
	if req.PageSize == 0 {
		req.PageSize = defaultPageSize
	}
	result, err := DiagnoseTemplateSvc.GetDiagnoseTemplateService().ListDiagnoseTemplate(ctx, &diagnose_template.ListDiagnoseTemplateRequest{
		PageNumber:           lo.ToPtr(req.PageNumber),
		PageSize:             lo.ToPtr(req.PageSize),
		DiagnoseTemplateIDs:  req.DiagnoseTemplateIDs,
		DiagnoseTemplateName: req.DiagnoseTemplateName,
		Status:               req.Status,
		ResponsiblePerson:    req.ResponsiblePerson,
		ProductCategoryIDs:   req.ProductCategoryIDs,
	})
	if err != nil {
		logger.CtxError(ctx, "ListDiagnoseTemplate err:%v", err)
		return nil, err
	}
	return &openapi.ListDiagnoseTemplateResult{
		DiagnoseTemplates: result.DiagnoseTemplates,
		Pagination:        result.Pagination,
	}, nil
}

func (s *Service) ListProductCategory(ctx context.Context, req *openapi.ListProductCategoryRequest) (*openapi.ListProductCategoryResult, error) {
	logger := s.logger.WithFunc("OpenApi-ListProductCategory")
	result, err := ProductCategorySvc.GetProductCategoryService().QueryProductCategoryList(ctx, product_category.QueryProductCategoryListReq{
		Id:             req.Id,
		Product:        req.Product,
		SubProduct:     req.SubProduct,
		ResourceType:   req.ResourceType,
		ProductCn:      req.ProductCn,
		SubProductCn:   req.SubProductCn,
		ResourceTypeCn: req.ResourceTypeCn,
	})
	if err != nil {
		logger.CtxError(ctx, "ListProductCategory err:%v", err)
		return nil, err
	}
	return &openapi.ListProductCategoryResult{
		ProductCategoryList: result.ProductCategoryList,
	}, nil
}

func (s *Service) CreateDiagnoseTask(ctx context.Context, req *openapi.CreateDiagnoseTaskReq) (*openapi.CreateDiagnoseTaskResult, error) {
	logger := s.logger.WithFunc("OpenApi-CreateDiagnoseTask")
	createDiagnoseTaskV2Req := &diagnose_task_v2.CreateDiagnoseTaskV2Req{
		Name:               req.Name,
		DiagnoseTemplateID: req.DiagnoseTemplateID,
		DiagnoseItemIDs:    req.DiagnoseItemIDs,
		CreateUserID:       req.CreateUserID,
		ProductCategoryIDs: req.ProductCategoryIDs,
		Dimension:          req.Dimension,
		TicketID:           req.TicketID,
		DiagnoseType:       req.DiagnoseType,
		Origin:             req.Origin,
	}
	diagnoseTaskV2ID, err := diagnoseTaskV2Svc.GetDiagnoseTaskV2Service().CreateDiagnoseTask(ctx, *createDiagnoseTaskV2Req)
	if err != nil {
		logger.CtxError(ctx, "CreateDiagnoseTaskV2 err:%v", err)
		return nil, err
	}
	//RunDiagnoseTask
	runDiagnoseTaskReq := &diagnose_task_run.RunDiagnoseTaskReq{
		DiagnoseTaskID:    diagnoseTaskV2ID,
		RunUserID:         req.CreateUserID,
		DiagnoseResources: req.DiagnoseResources,
		DiagnoseStartTime: req.DiagnoseStartTime,
		DiagnoseEndTime:   req.DiagnoseEndTime,
		Name:              lo.ToPtr(req.Name),
		AccountID:         req.AccountID,
	}
	runResult, err := diagnoseTaskRunSvc.GetDiagnoseTaskRunService().RunDiagnoseTask(ctx, runDiagnoseTaskReq)
	if err != nil {
		logger.CtxError(ctx, "RunDiagnoseTask err:%v", err)
		return nil, err
	}
	return &openapi.CreateDiagnoseTaskResult{
		DiagnoseTaskID: runResult.DiagnoseTaskRunID,
	}, nil
}

func (s *Service) QueryDiagnoseTaskResult(ctx context.Context, req *openapi.QueryDiagnoseTaskResultRequest) (*openapi.QueryDiagnoseTaskResultResult, error) {
	logger := s.logger.WithFunc("OpenApi-QueryDiagnoseTaskResult")
	filter := &model.DiagnoseResultV2{
		DiagnoseTaskRunID: req.DiagnoseTaskRunID,
	}
	if req.DiagnoseItemID != nil {
		filter.DiagnoseItemID = *req.DiagnoseItemID
	}
	result, err := dal.DiagnoseResultsFindByConditionsSortByLevelNum(ctx, filter, req.Levels)
	if err != nil {
		logger.CtxError(ctx, "DiagnoseResultsFindByConditionsSortByLevelNum err:%v", err)
		return nil, err
	}
	dResultList := make([]*diagnose_result.DiagnoseResult, 0)
	for _, re := range result {
		dResultList = append(dResultList, &diagnose_result.DiagnoseResult{
			InstanceID:          re.InstanceID,
			InstanceName:        re.InstanceName,
			Region:              re.Region,
			Message:             re.DiagnoseMessage,
			DiagnoseResultLevel: re.DiagnoseResultLevel,
			Suggestion:          re.DiagnoseSuggestion,
			Operate:             re.DiagnoseOperate,
			DiagnoseItemID:      re.DiagnoseItemID,
		})
	}
	return &openapi.QueryDiagnoseTaskResultResult{
		DiagnoseResults: dResultList,
	}, nil
}

func (s *Service) UpdateDiagnoseItemName(ctx context.Context, req *openapi.UpdateDiagnoseItemNameRequest) (*openapi.UpdateDiagnoseItemNameResult, error) {
	s.logger.WithFunc("UpdateDiagnoseItemName")

	res, err := dal.FindDiagnoseItemByID(ctx, req.DiagnoseItemID)
	if err != nil {
		if err.Error() != "record not found" {
			s.logger.WithFunc("UpdateDiagnoseItem").Error("FindDiagnoseItemByID err:%v", err)
			return nil, errorcode.ErrDataNotFound.WithArgs(fmt.Sprintf("DiagnoseItem[%d]", req.DiagnoseItemID))
		} else {
			s.logger.WithFunc("UpdateDiagnoseItem").Error("FindDiagnoseItemByID err:%v", err)
			return nil, err
		}
	}
	if res.DeleteAt != nil {
		s.logger.WithFunc("UpdateDiagnoseItem").Error("DiagnoseItem[%d] has been deleted", req.DiagnoseItemID)
		return nil, errorcode.ErrDataNotFound.WithArgs(fmt.Sprintf("DiagnoseItem[%d]", req.DiagnoseItemID))
	}
	//查询该诊断项关联的诊断模版
	relations, err := dal.FindRelationByItemID(ctx, req.DiagnoseItemID)
	if err != nil {
		s.logger.WithFunc("UpdateDiagnoseItem").Error("FindRelationByItemID err:%v", err)
		return nil, err
	}
	templates := make([]*model.DiagnoseTemplate, 0)
	for _, relation := range relations {
		templateRes, err := dal.FindDiagnoseTemplateByID(ctx, relation.DiagnoseTemplateID)
		if err != nil {
			if err.Error() != "record not found" {
				s.logger.WithFunc("UpdateDiagnoseItemName").Error("FindDiagnoseTemplateByID err:%v", err)
				return nil, errorcode.ErrDataNotFound.WithArgs(fmt.Sprintf("DiagnoseTempalte[%d]", relation.DiagnoseTemplateID))
			} else {
				s.logger.WithFunc("UpdateDiagnoseItemName").Error("FindDiagnoseTemplateByID err:%v", err)
				return nil, err
			}
		}
		if res.DeleteAt != nil {
			s.logger.WithFunc("UpdateDiagnoseItemName").Error("DiagnoseTemplate[%d] has been deleted", relation.DiagnoseTemplateID)
			return nil, errorcode.ErrDataNotFound.WithArgs(fmt.Sprintf("DiagnoseTemplate[%d]", relation.DiagnoseTemplateID))
		}
		tempalte := &model.DiagnoseTemplate{
			DiagnoseTemplateID: relation.DiagnoseTemplateID,
			UpdaterID:          &req.UserInfo.UserID,
			UpdaterName:        &req.UserInfo.UserName,
		}
		asl := strings.Replace(*templateRes.StateMachineInfo, *res.DiagnoseItemName, req.DiagnoseItemName, -1)
		tempalte.StateMachineInfo = &asl
		templates = append(templates, tempalte)
	}

	item := &model.DiagnoseItem{
		DiagnoseItemID: req.DiagnoseItemID,
		UpdaterID:      &req.UserInfo.UserID,
		UpdaterName:    &req.UserInfo.UserName,
	}
	//更新诊断项的Asl语言
	if req.DiagnoseItemName != "" {
		item.DiagnoseItemName = &req.DiagnoseItemName
		item.StateMachineInfo = utils.GetDiagnoseItemAsl(req.DiagnoseItemName, *res.Activity)
	}
	err = dal.UpdateDiagnoseItemName(ctx, item, templates)
	if err != nil {
		return nil, err
	}
	IsSuccess := "success"
	return &openapi.UpdateDiagnoseItemNameResult{
		Result: &IsSuccess,
	}, nil
}

func (s *Service) TicketSummary(ctx context.Context, req *discourseHertz.TicketSummaryReq) (*discourseHertz.TicketResp, error) {
	logger := s.logger.WithFunc("OpenApi-TicketSummary")
	info, err := DiscourseSvc.GetDiscourseService().TicketSummary(ctx, req)
	if err != nil {
		logger.CtxError(ctx, "TicketSummary err:%v", err)
		return nil, err
	}
	return info, nil
}

func (s *Service) TicketReport(ctx context.Context, req *discourseHertz.TicketReportReq) *discourseHertz.TicketResp {
	return DiscourseSvc.GetDiscourseService().TicketReport(ctx, req)
}

func (s *Service) QueryFeedback(ctx context.Context, req *openapi.QueryFeedbackReq) (*openapi.QueryFeedbackResult, error) {
	logger := s.logger.WithFunc("OpenApi-QueryFeedback")
	result, err := feedbackSvc.GetFeedbackService().QueryFeedback(ctx, &feedback.QueryFeedbackReq{
		Status:         req.Status,
		SessionID:      req.SessionID,
		FeedbackUserID: req.FeedbackUserID,
		SubProduct:     req.SubProduct,
		TicketID:       req.TicketID,
		PageNumber:     req.PageNumber,
		PageSize:       req.PageSize,
		IsLike:         req.IsLike,
		Valid:          req.Valid,
	})
	if err != nil {
		logger.CtxError(ctx, "QueryFeedback err:%v", err)
		return nil, err
	}
	return &openapi.QueryFeedbackResult{
		FeedbackList: result.FeedbackList,
		Pagination:   result.Pagination,
	}, nil
}
