package paas

import (
	"context"
	"fmt"
	"strings"
	"time"

	"code.byted.org/volcengine-support/cloud-sherlock/biz/dal"
	"code.byted.org/volcengine-support/cloud-sherlock/faas_biz/bfevent/common"
	"code.byted.org/volcengine-support/cloud-sherlock/gen/gorm/model"
	"code.byted.org/volcengine-support/cloud-sherlock/gen/hertz/diagnose_result"
	"code.byted.org/volcengine-support/cloud-sherlock/pkg/cmdb"
	paasdiagnose "code.byted.org/volcengine-support/cloud-sherlock/pkg/paas"
	"code.byted.org/volcengine-support/cloud-sherlock/pkg/statemachines"
	"code.byted.org/volcengine-support/cloud-sherlock/utils"
	"github.com/samber/lo"
)

const (
	ScenarioKey = "Scenario"
)

type Handler struct{}

func NewHandler() *Handler {
	return &Handler{}
}

func (h *Handler) GetName() string {
	return common.PaasActivityName
}

func (h *Handler) TransferRespToResults(ctx context.Context, resp interface{}) ([]*model.DiagnoseResultV2, error) {
	logger := utils.NewModuleLogger("PaaSActivity-TransferRespToResults")
	diagnoseResults := make([]*model.DiagnoseResultV2, 0)
	switch paasResp := resp.(type) {
	case *paasdiagnose.PhaseResult:

		instanceID := ""
		instanceName := ""
		region := ""
		modelObject := &paasdiagnose.ModelObject{}
		switch paasResp.Target.ModelName {
		case "VkeNode":
			targetData := &cmdb.VkeNode{}
			err := utils.JsonUnmarshalString(paasResp.Target.Data, targetData)
			if err != nil {
				logger.CtxError(ctx, "VkeNode JSON解析失败: %s | 原始数据: %s", err, paasResp.Target.Data)
				return nil, err
			}
			instanceID = targetData.Spec.NodeId
			instanceName = targetData.Spec.Name
			region = targetData.Spec.Region
		case "VkeNodePool":
			targetData := &cmdb.VkeNodePool{}
			err := utils.JsonUnmarshalString(paasResp.Target.Data, targetData)
			if err != nil {
				logger.CtxError(ctx, "VkeNodePool JSON解析失败: %s | 原始数据: %s", err, paasResp.Target.Data)
				return nil, err
			}
			instanceID = targetData.Spec.NodePoolId
			instanceName = targetData.Spec.Name
			region = targetData.Spec.Region
		case "VkeInstance":
			targetData := &struct {
				AccountId         int64  `json:"AccountId"`
				Id                string `json:"Id"`
				KubernetesVersion string `json:"KubernetesVersion"`
				Name              string `json:"Name"`
				Region            string `json:"Region"`
			}{}
			fmt.Printf("paasResp.Target.Data: %s\n", paasResp.Target.Data)
			err := utils.JsonUnmarshalString(paasResp.Target.Data, targetData)
			if err != nil {
				logger.CtxError(ctx, "VkeInstance JSON解析失败: %s | 原始数据: %s", err, paasResp.Target.Data)
				return nil, err
			}
			instanceID = targetData.Id
			instanceName = targetData.Name
			region = targetData.Region
		case "VkePod":
			targetData := &cmdb.VkePod{}
			err := utils.JsonUnmarshalString(paasResp.Target.Data, targetData)
			if err != nil {
				logger.CtxError(ctx, "VkePod JSON解析失败: %s | 原始数据: %s", err, paasResp.Target.Data)
				return nil, err
			}
			instanceID = fmt.Sprintf("%s:%s:%s", targetData.Spec.ClusterId, targetData.Spec.Namespace, targetData.Spec.Pod.Name)
			instanceName = fmt.Sprintf("%s:%s:%s", targetData.Spec.ClusterId, targetData.Spec.Namespace, targetData.Spec.Pod.Name)
			region = targetData.Spec.Region
		default:
			return nil, fmt.Errorf("invalid resourceType name: %s", paasResp.Target.ModelName)

		}

		err := utils.JsonUnmarshal([]byte(paasResp.Target.Data), modelObject)
		if err != nil {
			return nil, err
		}
		logger.CtxInfo(ctx, "modelObject: %s", utils.JsonToString(paasResp.Results))
		for _, item := range paasResp.Results {
			var diagnoseResult model.DiagnoseResultV2
			diagnoseResult.InstanceID = instanceID
			diagnoseResult.InstanceName = lo.ToPtr(instanceName)
			diagnoseResult.Region = lo.ToPtr(region)
			diagnoseResult.DiagnoseMessage = lo.ToPtr(item.Rule.Description)
			diagnoseResult.DiagnoseResultLevel = transferLevel(item.Result)
			switch item.Result {
			case "Succeeded":
				diagnoseResult.DiagnoseSuggestion = lo.ToPtr("检测通过")
				diagnoseResult.DiagnoseOperate = lo.ToPtr("")

			case "Error":
				diagnoseResult.Status = lo.ToPtr(dal.DiagnoseResultStatusFailed)
				diagnoseResult.ErrorInfo = lo.ToPtr(item.Error)
				diagnoseResult.DiagnoseSuggestion = lo.ToPtr(item.Detail)
			case "Warning":
				diagnoseResult.Status = lo.ToPtr(string(dal.DiagnoseResultStatusSucceed))
				diagnoseResult.DiagnoseSuggestion = lo.ToPtr(item.Detail)
				// diagnoseResult.DiagnoseSuggestion = lo.ToPtr(item.Warning.Affection)
			case "Failed":
				diagnoseResult.Status = lo.ToPtr(string(dal.DiagnoseResultStatusSucceed))
				diagnoseResult.DiagnoseSuggestion = lo.ToPtr(item.Failed.Suggestion)
				diagnoseResult.DiagnoseOperate = lo.ToPtr(item.Failed.Reason)
			default:
				diagnoseResult.DiagnoseSuggestion = lo.ToPtr(item.Failed.Affection)
				diagnoseResult.DiagnoseOperate = lo.ToPtr(item.Failed.Suggestion)
			}
			diagnoseResults = append(diagnoseResults, &diagnoseResult)
		}
	default:
		logger.CtxError(ctx, "resp type error: %s", utils.JsonToString(resp))
		return nil, fmt.Errorf("invalid response type: %T", resp)
	}
	return diagnoseResults, nil
}

func (h *Handler) RunDiagnoseItem(ctx context.Context, itemID int64, resources []*diagnose_result.DiagnoseResource, diagnoseInput *statemachines.DiagnoseRequest) (interface{}, []*common.ErrorItem, error) {
	logger := utils.NewModuleLogger("PaaSActivity-RunDiagnoseItem")
	scenario, err := common.FindParamValueByDiagnoseItemID(ctx, itemID, ScenarioKey)
	if err != nil {
		return nil, nil, fmt.Errorf("FindParamValueByDiagnoseItemID error: %s", err)
	}
	errorItems := make([]*common.ErrorItem, 0)
	host, err := common.FindHostByDiagnoseItemID(ctx, itemID)
	if err != nil {
		return nil, nil, err
	}
	productCategoryID := int64(0)
	// 从cmdb 查询资源并获取每个资源的region信息
	resourceFilters := map[string][]cmdb.FieldFilter{}
	for _, resource := range resources {
		if productCategoryID == 0 {
			productCategoryID = resource.ProductCategoryID
		}
		productCategory, err := dal.QueryProductCategoryByID(ctx, resource.ProductCategoryID)
		if err != nil {
			logger.CtxWarn(ctx, "QueryProductCategoryByID error: %s", err)
			continue
		}

		ins := make([]cmdb.FieldFilter, 10)
		switch productCategory.ResourceType {

		case "VkeInstance":
			ins = lo.FilterMap(resource.Instances, func(item string, index int) (cmdb.FieldFilter, bool) {
				return []cmdb.FieldFilterItem{
					{
						Field: "metadata.name",
						Value: item,
					},
				}, true
			})
		case "VkePod":
			ins = lo.FilterMap(resource.Instances, func(item string, index int) (cmdb.FieldFilter, bool) {
				args := strings.Split(item, ":")
				if len(args) != 3 {
					logger.CtxWarn(ctx, "VkePod args error: %s", item)
					return nil, false
				}
				clusterID := args[0]
				namespace := args[1]
				podName := args[2]
				return []cmdb.FieldFilterItem{
					{
						Field: "spec.clusterId",
						Value: clusterID,
					},
					{
						Field: "spec.namespace",
						Value: namespace,
					},
					{
						Field: "metadata.name",
						Value: podName,
					},
				}, true
			})
		case "VkeNode":
			ins = lo.FilterMap(resource.Instances, func(machineID string, index int) (cmdb.FieldFilter, bool) {
				return []cmdb.FieldFilterItem{
					{
						Field: "spec.nodeId",
						Value: machineID,
					},
				}, true
			})
		case "VkeNodePool":
			ins = lo.FilterMap(resource.Instances, func(nodePoolID string, index int) (cmdb.FieldFilter, bool) {
				return []cmdb.FieldFilterItem{
					{
						Field: "spec.nodePoolId",
						Value: nodePoolID,
					},
				}, true
			})
		}
		logger.CtxInfo(ctx, "ins: %s\n", utils.JsonToString(ins))

		if _, ok := resourceFilters[productCategory.ResourceType]; !ok {
			resourceFilters[productCategory.ResourceType] = ins
		} else {
			resourceFilters[productCategory.ResourceType] = append(resourceFilters[productCategory.ResourceType], ins...)
		}
	}
	createTaskIDs := make(map[int64][]string)
	// 实例列表信息 interface{} 类型, 在执行诊断的时候根据ResourceType进行转换
	for resourceType, filters := range resourceFilters {
		allItems := []interface{}{}
		pageNumber := int32(1)
		pageSize := int32(100)
		for {
			listEntitiesRequest := &cmdb.ListEntitiesRequest{
				PageNumber: pageNumber,
				PageSize:   pageSize,
				Kind:       resourceType,
				Filter: cmdb.ListEntitiesFilter{
					FieldFilters: filters,
				},
			}
			listEntitiesResponse, err := cmdb.GetCMDBClient().ListEntities(ctx, listEntitiesRequest)
			if err != nil {
				logger.CtxWarn(ctx, "ListEntities error: %s", err)
				break
			}
			allItems = append(allItems, listEntitiesResponse.Items...)

			if int32(len(listEntitiesResponse.Items)) < pageSize {
				logger.CtxWarn(ctx, "ListEntities response is empty")
				break
			}
			pageNumber++
		}

		//创建任务,根据不同的资源类型进行request 请求体转换

		logger.CtxInfo(ctx, "CMDB查询参数: Kind=%s, Filters=%s", resourceType, utils.JsonToString(filters))

		for idx, item := range allItems {

			switch resourceType {
			case "VkeInstance":
				vkeClusterInstanceInfo := &cmdb.VKEClusterInstance{}
				diagnoseRequestBody, err := vkeClusterInstanceInfo.BuildDiagnoseRequest(ctx, scenario, diagnoseInput.TicketID, item)
				if err != nil {
					logger.CtxError(ctx, "[Item%d] 构建请求体失败: %s | 原始数据: %s", idx, err, utils.JsonToString(item))
					continue
				}

				logger.CtxInfo(ctx, "diagnoseRequestBody:", *diagnoseRequestBody)
				createTaskResult, err := CreateDiagnoseTask(ctx, host, diagnoseRequestBody)
				if err != nil {
					logger.CtxError(ctx, "CreateDiagnoseTask error: %s", err)
					errorItems = append(errorItems, &common.ErrorItem{
						ResourceID:     vkeClusterInstanceInfo.Spec.ClusterId,
						DiagnoseItemID: itemID,
						Error:          err,
					})
					logger.CtxError(ctx, "CreateDiagnoseTask  errTaskResourceIds: %s, err:%v", vkeClusterInstanceInfo.Spec.ClusterId, err)
					continue
				}
				createTaskIDs[createTaskResult.Result.Id] = []string{vkeClusterInstanceInfo.Spec.ClusterId}
				logger.CtxInfo(ctx, "CreateDiagnoseTask response: %d\n", createTaskResult.Result.Id)
			case "VkePod":
				vkePod := &cmdb.VkePod{}
				vkePodInfo, err := vkePod.BuildDiagnoseRequest(ctx, scenario, diagnoseInput.TicketID, item)
				if err != nil {
					logger.CtxError(ctx, "[Item%d] 构建请求体失败: %s | 原始数据: %s", idx, err, utils.JsonToString(item))
					continue
				}
				logger.CtxInfo(ctx, "diagnoseRequestBody:", *vkePodInfo)
				createTaskResult, err := CreateDiagnoseTask(ctx, host, vkePodInfo)
				if err != nil {
					logger.CtxError(ctx, "CreateDiagnoseTask error: %s", err)
					errorItems = append(errorItems, &common.ErrorItem{
						ResourceID:     fmt.Sprintf("%s/%s/%s", vkePod.Spec.ClusterId, vkePod.Spec.Namespace, vkePod.Metadata.Name),
						DiagnoseItemID: itemID,
						Error:          err,
					})
					logger.CtxError(ctx, "CreateDiagnoseTask  errTaskResourceIds: %s, err:%v", fmt.Sprintf("%s/%s/%s", vkePod.Spec.ClusterId, vkePod.Spec.Namespace, vkePod.Metadata.Name), err)
					continue
				}
				createTaskIDs[createTaskResult.Result.Id] = []string{fmt.Sprintf("%s/%s/%s", vkePod.Spec.ClusterId, vkePod.Spec.Namespace, vkePod.Metadata.Name)}
				logger.CtxInfo(ctx, "CreateDiagnoseTask response: %d\n", createTaskResult.Result.Id)
			case "VkeNode":
				vkeNode := &cmdb.VkeNode{}
				vkeNodeInfo, err := vkeNode.BuildDiagnoseRequest(ctx, scenario, diagnoseInput.TicketID, item)
				if err != nil {
					logger.CtxError(ctx, "[Item%d] 构建请求体失败: %s | 原始数据: %s", idx, err, utils.JsonToString(item))
					continue
				}
				logger.CtxInfo(ctx, "diagnoseRequestBody:", *vkeNodeInfo)
				createTaskResult, err := CreateDiagnoseTask(ctx, host, vkeNodeInfo)
				if err != nil {
					logger.CtxError(ctx, "CreateDiagnoseTask error: %s", err)
					errorItems = append(errorItems, &common.ErrorItem{
						ResourceID:     vkeNode.Spec.NodeId,
						DiagnoseItemID: itemID,
						Error:          err,
					})
				}
				createTaskIDs[createTaskResult.Result.Id] = []string{vkeNode.Spec.NodeId}
				logger.CtxInfo(ctx, "CreateDiagnoseTask response: %d\n", createTaskResult.Result.Id)

			case "VkeNodePool":
				vkeNodePool := &cmdb.VkeNodePool{}
				vkeNodePoolInfo, err := vkeNodePool.BuildDiagnoseRequest(ctx, scenario, diagnoseInput.TicketID, item)
				if err != nil {
					logger.CtxError(ctx, "[Item%d] 构建请求体失败: %s | 原始数据: %s", idx, err, utils.JsonToString(item))
					continue
				}
				logger.CtxInfo(ctx, "diagnoseRequestBody:", *vkeNodePoolInfo)
				createTaskResult, err := CreateDiagnoseTask(ctx, host, vkeNodePoolInfo)
				if err != nil {
					logger.CtxError(ctx, "CreateDiagnoseTask error: %s", err)
					errorItems = append(errorItems, &common.ErrorItem{
						ResourceID:     vkeNodePool.Spec.NodePoolId,
						DiagnoseItemID: itemID,
						Error:          err,
					})
				}
				createTaskIDs[createTaskResult.Result.Id] = []string{vkeNodePool.Spec.NodePoolId}
			}

		}

	}
	diagnoseResultArray := make([]*paasdiagnose.PhaseResult, 0)
	doneMap := make(map[int64]bool)
	done := false

	if len(createTaskIDs) > 0 {
		for i := 0; i < 18; i++ {
			if done {
				break
			}
			time.Sleep(5 * time.Second)
			taskIDs := lo.Keys(createTaskIDs)
			listDiagnoseTasksReq := &paasdiagnose.ListDiagnoseTasksRequest{
				Ids: taskIDs,
			}

			listDiagnoseTaskResponse, err := ListDiagnoseTasks(ctx, host, listDiagnoseTasksReq)
			if err != nil {
				logger.CtxError(ctx, "ListDiagnoseTasks error: %s", err)
				continue
			}
			logger.CtxInfo(ctx, "listDiagnoseTaskResponse", listDiagnoseTaskResponse)
			for _, taskResult := range listDiagnoseTaskResponse.Result.Data {
				if doneMap[taskResult.Id] {
					continue
				}
				if taskResult.TaskStatus == "Success" {
					for _, resultItem := range taskResult.Results {
						diagnoseResultArray = append(diagnoseResultArray, &resultItem)
						doneMap[taskResult.Id] = true
					}
				}
			}
			if len(doneMap) == len(createTaskIDs) {
				done = true
				break
			}
		}
	}
	//记录查询失败的诊断项
	if !done {
		for taskID, errInstanceIDs := range createTaskIDs {
			if doneMap[taskID] {
				continue
			}
			for _, clusterID := range errInstanceIDs {
				errorItems = append(errorItems, &common.ErrorItem{
					ResourceID:     clusterID,
					DiagnoseItemID: itemID,
					Error:          fmt.Errorf("ListDiagnoseTasks takeID:%d", taskID),
				})
			}
		}
	}
	return diagnoseResultArray, errorItems, nil
}
