// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameAppAkSk = "app_ak_sk"

// AppAkSk 应用AKSK表
type AppAkSk struct {
	ID           int64      `gorm:"column:id;primaryKey;autoIncrement:true;comment:自增ID" json:"ID"`                 // 自增ID
	AppID        string     `gorm:"column:app_id;not null;comment:应用ID" json:"AppID"`                               // 应用ID
	AppName      *string    `gorm:"column:app_name;comment:应用名称" json:"AppName"`                                    // 应用名称
	AppKey       string     `gorm:"column:app_key;not null;comment:应用Key" json:"AppKey"`                            // 应用Key
	CreateTime   time.Time  `gorm:"column:create_time;not null;comment:创建时间" json:"CreateTime"`                     // 创建时间
	UpdateTime   time.Time  `gorm:"column:update_time;not null;comment:更新时间" json:"UpdateTime"`                     // 更新时间
	CerateUserID string     `gorm:"column:cerate_user_id;not null;comment:创建人ID" json:"CerateUserID"`               // 创建人ID
	UpdateUserID string     `gorm:"column:update_user_id;not null;comment:更新人ID" json:"UpdateUserID"`               // 更新人ID
	OutDateTime  *time.Time `gorm:"column:out_date_time;comment:过期时间" json:"OutDateTime"`                           // 过期时间
	AppSecretEn  string     `gorm:"column:app_secret_en;not null;default:0;comment:加密的应用Secret" json:"AppSecretEn"` // 加密的应用Secret
}

// TableName AppAkSk's table name
func (*AppAkSk) TableName() string {
	return TableNameAppAkSk
}
