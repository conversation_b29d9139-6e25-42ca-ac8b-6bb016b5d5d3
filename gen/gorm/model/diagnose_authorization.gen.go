// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

const TableNameDiagnoseAuthorization = "diagnose_authorization"

// DiagnoseAuthorization 诊断授权历史表
type DiagnoseAuthorization struct {
	ID                   int64   `gorm:"column:id;primaryKey;autoIncrement:true;comment:主键" json:"ID"`                     // 主键
	AuthorizationID      *string `gorm:"column:authorization_id;comment:远程运维平台数据库中该条数据对应的授权id" json:"AuthorizationID"`     // 远程运维平台数据库中该条数据对应的授权id
	ApplicationPolicy    *string `gorm:"column:application_policy;comment:策略名称(申请策略)，名称之间用“，”隔开" json:"ApplicationPolicy"` // 策略名称(申请策略)，名称之间用“，”隔开
	CreatedTime          *int32  `gorm:"column:created_time;comment:授权提交时间（发起申请的时间，秒级时间戳）" json:"CreatedTime"`             // 授权提交时间（发起申请的时间，秒级时间戳）
	UpdateTime           *int32  `gorm:"column:update_time;comment:更新时间（可借助该字段同步官网的数据）" json:"UpdateTime"`                 // 更新时间（可借助该字段同步官网的数据）
	StartTime            *int32  `gorm:"column:start_time;comment:授权开始时间（通过后，秒级时间戳）" json:"StartTime"`                     // 授权开始时间（通过后，秒级时间戳）
	EndTime              *int32  `gorm:"column:end_time;comment:授权结束时间（秒级时间戳）" json:"EndTime"`                             // 授权结束时间（秒级时间戳）
	ExpiredTime          *int32  `gorm:"column:expired_time;comment:授权过期时间" json:"ExpiredTime"`                            // 授权过期时间
	Status               *int32  `gorm:"column:status;comment:状态：1. 待审批；2. 生效中；3. 拒绝；4. 结束；5. 过期" json:"Status"`           // 状态：1. 待审批；2. 生效中；3. 拒绝；4. 结束；5. 过期
	AccountID            *string `gorm:"column:account_id;comment:火山账号id" json:"AccountID"`                                // 火山账号id
	ApplyUser            *string `gorm:"column:apply_user;comment:申请人邮箱" json:"ApplyUser"`                                 // 申请人邮箱
	TicketID             *string `gorm:"column:ticket_id;comment:工单id" json:"TicketID"`                                    // 工单id
	DiagnoseTemplateName *string `gorm:"column:diagnose_template_name;comment:诊断场景名称" json:"DiagnoseTemplateName"`         // 诊断场景名称
	ProductCategoryIds   *string `gorm:"column:product_category_ids;comment:产品类型id" json:"ProductCategoryIDs"`             // 产品类型id
	InstanceIds          *string `gorm:"column:instance_ids;comment:本次授权的实例ID信息" json:"InstanceIDs"`                       // 本次授权的实例ID信息
}

// TableName DiagnoseAuthorization's table name
func (*DiagnoseAuthorization) TableName() string {
	return TableNameDiagnoseAuthorization
}
