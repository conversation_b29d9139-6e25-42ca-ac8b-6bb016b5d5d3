// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

const TableNameFeedbackLabel = "feedback_label"

// FeedbackLabel 反馈标签信息表
type FeedbackLabel struct {
	ID            int64   `gorm:"column:id;primaryKey;autoIncrement:true;comment:自增ID" json:"ID"` // 自增ID
	Stage         string  `gorm:"column:stage;not null;comment:阶段" json:"Stage"`                  // 阶段
	LabelKey      string  `gorm:"column:label_key;not null;comment:标签Key" json:"LabelKey"`        // 标签Key
	LabelName     string  `gorm:"column:label_name;not null;comment:标签名" json:"LabelName"`        // 标签名
	HandleUserIds *string `gorm:"column:handle_user_ids;comment:处理人" json:"HandleUserIDs"`        // 处理人
}

// TableName FeedbackLabel's table name
func (*FeedbackLabel) TableName() string {
	return TableNameFeedbackLabel
}
