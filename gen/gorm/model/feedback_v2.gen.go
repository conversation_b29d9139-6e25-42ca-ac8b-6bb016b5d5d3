// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameFeedbackV2 = "feedback_v2"

// FeedbackV2 用户反馈信息表
type FeedbackV2 struct {
	ID                 int64      `gorm:"column:id;primaryKey;autoIncrement:true;comment:自增ID" json:"ID"`         // 自增ID
	SessionID          string     `gorm:"column:session_id;not null;comment:会话ID" json:"SessionID"`               // 会话ID
	MessageID          string     `gorm:"column:message_id;not null;comment:消息ID" json:"MessageID"`               // 消息ID
	Stage              string     `gorm:"column:stage;not null;comment:阶段" json:"Stage"`                          // 阶段
	IsLike             bool       `gorm:"column:is_like;not null;comment:点赞" json:"IsLike"`                       // 点赞
	FeedbackUserID     *string    `gorm:"column:feedback_user_id;comment:反馈人邮箱" json:"FeedbackUserID"`            // 反馈人邮箱
	ProductCategoryID  *string    `gorm:"column:product_category_id;comment:产品类型ID（产管）" json:"ProductCategoryID"` // 产品类型ID（产管）
	TicketID           *string    `gorm:"column:ticket_id;comment:工单ID" json:"TicketID"`                          // 工单ID
	DiagnoseTaskRunID  *int64     `gorm:"column:diagnose_task_run_id;comment:诊断执行ID" json:"DiagnoseTaskRunID"`    // 诊断执行ID
	ReportLink         *string    `gorm:"column:report_link;comment:诊断报告链接" json:"ReportLink"`                    // 诊断报告链接
	FeedbackLabelIds   *string    `gorm:"column:feedback_label_ids;comment:反馈标签IDs" json:"FeedbackLabelIDs"`      // 反馈标签IDs
	Description        *string    `gorm:"column:description;comment:其他描述" json:"Description"`                     // 其他描述
	FeedbackTime       time.Time  `gorm:"column:feedback_time;not null;comment:反馈时间" json:"FeedbackTime"`         // 反馈时间
	HandleUserIds      *string    `gorm:"column:handle_user_ids;comment:处理人邮箱数组" json:"HandleUserIDs"`            // 处理人邮箱数组
	Status             string     `gorm:"column:status;not null;comment:状态" json:"Status"`                        // 状态
	StartHandleUserID  *string    `gorm:"column:start_handle_user_id;comment:开始处理人邮箱" json:"StartHandleUserID"`   // 开始处理人邮箱
	EndHandleUserID    *string    `gorm:"column:end_handle_user_id;comment:结束处理人邮箱" json:"EndHandleUserID"`       // 结束处理人邮箱
	StartHandleTime    *time.Time `gorm:"column:start_handle_time;comment:开始处理时间" json:"StartHandleTime"`         // 开始处理时间
	EndHandleTime      *time.Time `gorm:"column:end_handle_time;comment:结束处理时间" json:"EndHandleTime"`             // 结束处理时间
	Title              *string    `gorm:"column:title;comment:标题" json:"Title"`                                   // 标题
	HandleResults      *string    `gorm:"column:handle_results;comment:处理结果" json:"HandleResults"`                // 处理结果
	Valid              bool       `gorm:"column:valid;not null;default:1;comment:是否生效" json:"Valid"`              // 是否生效
	FeedbackDepartment *string    `gorm:"column:feedback_department;comment:反馈人部门" json:"FeedbackDepartment"`     // 反馈人部门
	SubProduct         *string    `gorm:"column:sub_product;comment:子产品类型" json:"SubProduct"`                     // 子产品类型
	PName              *string    `gorm:"column:p_name;comment:垂直线名称" json:"PName"`                               // 垂直线名称
}

// TableName FeedbackV2's table name
func (*FeedbackV2) TableName() string {
	return TableNameFeedbackV2
}
