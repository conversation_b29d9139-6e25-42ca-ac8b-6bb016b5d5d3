// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"code.byted.org/volcengine-support/cloud-sherlock/gen/gorm/model"
)

func newAppAkSk(db *gorm.DB, opts ...gen.DOOption) appAkSk {
	_appAkSk := appAkSk{}

	_appAkSk.appAkSkDo.UseDB(db, opts...)
	_appAkSk.appAkSkDo.UseModel(&model.AppAkSk{})

	tableName := _appAkSk.appAkSkDo.TableName()
	_appAkSk.ALL = field.NewAsterisk(tableName)
	_appAkSk.ID = field.NewInt64(tableName, "id")
	_appAkSk.AppID = field.NewString(tableName, "app_id")
	_appAkSk.AppName = field.NewString(tableName, "app_name")
	_appAkSk.AppKey = field.NewString(tableName, "app_key")
	_appAkSk.CreateTime = field.NewTime(tableName, "create_time")
	_appAkSk.UpdateTime = field.NewTime(tableName, "update_time")
	_appAkSk.CerateUserID = field.NewString(tableName, "cerate_user_id")
	_appAkSk.UpdateUserID = field.NewString(tableName, "update_user_id")
	_appAkSk.OutDateTime = field.NewTime(tableName, "out_date_time")
	_appAkSk.AppSecretEn = field.NewString(tableName, "app_secret_en")

	_appAkSk.fillFieldMap()

	return _appAkSk
}

// appAkSk 应用AKSK表
type appAkSk struct {
	appAkSkDo appAkSkDo

	ALL          field.Asterisk
	ID           field.Int64  // 自增ID
	AppID        field.String // 应用ID
	AppName      field.String // 应用名称
	AppKey       field.String // 应用Key
	CreateTime   field.Time   // 创建时间
	UpdateTime   field.Time   // 更新时间
	CerateUserID field.String // 创建人ID
	UpdateUserID field.String // 更新人ID
	OutDateTime  field.Time   // 过期时间
	AppSecretEn  field.String // 加密的应用Secret

	fieldMap map[string]field.Expr
}

func (a appAkSk) Table(newTableName string) *appAkSk {
	a.appAkSkDo.UseTable(newTableName)
	return a.updateTableName(newTableName)
}

func (a appAkSk) As(alias string) *appAkSk {
	a.appAkSkDo.DO = *(a.appAkSkDo.As(alias).(*gen.DO))
	return a.updateTableName(alias)
}

func (a *appAkSk) updateTableName(table string) *appAkSk {
	a.ALL = field.NewAsterisk(table)
	a.ID = field.NewInt64(table, "id")
	a.AppID = field.NewString(table, "app_id")
	a.AppName = field.NewString(table, "app_name")
	a.AppKey = field.NewString(table, "app_key")
	a.CreateTime = field.NewTime(table, "create_time")
	a.UpdateTime = field.NewTime(table, "update_time")
	a.CerateUserID = field.NewString(table, "cerate_user_id")
	a.UpdateUserID = field.NewString(table, "update_user_id")
	a.OutDateTime = field.NewTime(table, "out_date_time")
	a.AppSecretEn = field.NewString(table, "app_secret_en")

	a.fillFieldMap()

	return a
}

func (a *appAkSk) WithContext(ctx context.Context) *appAkSkDo { return a.appAkSkDo.WithContext(ctx) }

func (a appAkSk) TableName() string { return a.appAkSkDo.TableName() }

func (a appAkSk) Alias() string { return a.appAkSkDo.Alias() }

func (a appAkSk) Columns(cols ...field.Expr) gen.Columns { return a.appAkSkDo.Columns(cols...) }

func (a *appAkSk) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := a.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (a *appAkSk) fillFieldMap() {
	a.fieldMap = make(map[string]field.Expr, 10)
	a.fieldMap["id"] = a.ID
	a.fieldMap["app_id"] = a.AppID
	a.fieldMap["app_name"] = a.AppName
	a.fieldMap["app_key"] = a.AppKey
	a.fieldMap["create_time"] = a.CreateTime
	a.fieldMap["update_time"] = a.UpdateTime
	a.fieldMap["cerate_user_id"] = a.CerateUserID
	a.fieldMap["update_user_id"] = a.UpdateUserID
	a.fieldMap["out_date_time"] = a.OutDateTime
	a.fieldMap["app_secret_en"] = a.AppSecretEn
}

func (a appAkSk) clone(db *gorm.DB) appAkSk {
	a.appAkSkDo.ReplaceConnPool(db.Statement.ConnPool)
	return a
}

func (a appAkSk) replaceDB(db *gorm.DB) appAkSk {
	a.appAkSkDo.ReplaceDB(db)
	return a
}

type appAkSkDo struct{ gen.DO }

func (a appAkSkDo) Debug() *appAkSkDo {
	return a.withDO(a.DO.Debug())
}

func (a appAkSkDo) WithContext(ctx context.Context) *appAkSkDo {
	return a.withDO(a.DO.WithContext(ctx))
}

func (a appAkSkDo) ReadDB() *appAkSkDo {
	return a.Clauses(dbresolver.Read)
}

func (a appAkSkDo) WriteDB() *appAkSkDo {
	return a.Clauses(dbresolver.Write)
}

func (a appAkSkDo) Session(config *gorm.Session) *appAkSkDo {
	return a.withDO(a.DO.Session(config))
}

func (a appAkSkDo) Clauses(conds ...clause.Expression) *appAkSkDo {
	return a.withDO(a.DO.Clauses(conds...))
}

func (a appAkSkDo) Returning(value interface{}, columns ...string) *appAkSkDo {
	return a.withDO(a.DO.Returning(value, columns...))
}

func (a appAkSkDo) Not(conds ...gen.Condition) *appAkSkDo {
	return a.withDO(a.DO.Not(conds...))
}

func (a appAkSkDo) Or(conds ...gen.Condition) *appAkSkDo {
	return a.withDO(a.DO.Or(conds...))
}

func (a appAkSkDo) Select(conds ...field.Expr) *appAkSkDo {
	return a.withDO(a.DO.Select(conds...))
}

func (a appAkSkDo) Where(conds ...gen.Condition) *appAkSkDo {
	return a.withDO(a.DO.Where(conds...))
}

func (a appAkSkDo) Order(conds ...field.Expr) *appAkSkDo {
	return a.withDO(a.DO.Order(conds...))
}

func (a appAkSkDo) Distinct(cols ...field.Expr) *appAkSkDo {
	return a.withDO(a.DO.Distinct(cols...))
}

func (a appAkSkDo) Omit(cols ...field.Expr) *appAkSkDo {
	return a.withDO(a.DO.Omit(cols...))
}

func (a appAkSkDo) Join(table schema.Tabler, on ...field.Expr) *appAkSkDo {
	return a.withDO(a.DO.Join(table, on...))
}

func (a appAkSkDo) LeftJoin(table schema.Tabler, on ...field.Expr) *appAkSkDo {
	return a.withDO(a.DO.LeftJoin(table, on...))
}

func (a appAkSkDo) RightJoin(table schema.Tabler, on ...field.Expr) *appAkSkDo {
	return a.withDO(a.DO.RightJoin(table, on...))
}

func (a appAkSkDo) Group(cols ...field.Expr) *appAkSkDo {
	return a.withDO(a.DO.Group(cols...))
}

func (a appAkSkDo) Having(conds ...gen.Condition) *appAkSkDo {
	return a.withDO(a.DO.Having(conds...))
}

func (a appAkSkDo) Limit(limit int) *appAkSkDo {
	return a.withDO(a.DO.Limit(limit))
}

func (a appAkSkDo) Offset(offset int) *appAkSkDo {
	return a.withDO(a.DO.Offset(offset))
}

func (a appAkSkDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *appAkSkDo {
	return a.withDO(a.DO.Scopes(funcs...))
}

func (a appAkSkDo) Unscoped() *appAkSkDo {
	return a.withDO(a.DO.Unscoped())
}

func (a appAkSkDo) Create(values ...*model.AppAkSk) error {
	if len(values) == 0 {
		return nil
	}
	return a.DO.Create(values)
}

func (a appAkSkDo) CreateInBatches(values []*model.AppAkSk, batchSize int) error {
	return a.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (a appAkSkDo) Save(values ...*model.AppAkSk) error {
	if len(values) == 0 {
		return nil
	}
	return a.DO.Save(values)
}

func (a appAkSkDo) First() (*model.AppAkSk, error) {
	if result, err := a.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.AppAkSk), nil
	}
}

func (a appAkSkDo) Take() (*model.AppAkSk, error) {
	if result, err := a.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.AppAkSk), nil
	}
}

func (a appAkSkDo) Last() (*model.AppAkSk, error) {
	if result, err := a.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.AppAkSk), nil
	}
}

func (a appAkSkDo) Find() ([]*model.AppAkSk, error) {
	result, err := a.DO.Find()
	return result.([]*model.AppAkSk), err
}

func (a appAkSkDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.AppAkSk, err error) {
	buf := make([]*model.AppAkSk, 0, batchSize)
	err = a.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (a appAkSkDo) FindInBatches(result *[]*model.AppAkSk, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return a.DO.FindInBatches(result, batchSize, fc)
}

func (a appAkSkDo) Attrs(attrs ...field.AssignExpr) *appAkSkDo {
	return a.withDO(a.DO.Attrs(attrs...))
}

func (a appAkSkDo) Assign(attrs ...field.AssignExpr) *appAkSkDo {
	return a.withDO(a.DO.Assign(attrs...))
}

func (a appAkSkDo) Joins(fields ...field.RelationField) *appAkSkDo {
	for _, _f := range fields {
		a = *a.withDO(a.DO.Joins(_f))
	}
	return &a
}

func (a appAkSkDo) Preload(fields ...field.RelationField) *appAkSkDo {
	for _, _f := range fields {
		a = *a.withDO(a.DO.Preload(_f))
	}
	return &a
}

func (a appAkSkDo) FirstOrInit() (*model.AppAkSk, error) {
	if result, err := a.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.AppAkSk), nil
	}
}

func (a appAkSkDo) FirstOrCreate() (*model.AppAkSk, error) {
	if result, err := a.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.AppAkSk), nil
	}
}

func (a appAkSkDo) FindByPage(offset int, limit int) (result []*model.AppAkSk, count int64, err error) {
	result, err = a.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = a.Offset(-1).Limit(-1).Count()
	return
}

func (a appAkSkDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = a.Count()
	if err != nil {
		return
	}

	err = a.Offset(offset).Limit(limit).Scan(result)
	return
}

func (a appAkSkDo) Scan(result interface{}) (err error) {
	return a.DO.Scan(result)
}

func (a appAkSkDo) Delete(models ...*model.AppAkSk) (result gen.ResultInfo, err error) {
	return a.DO.Delete(models)
}

func (a *appAkSkDo) withDO(do gen.Dao) *appAkSkDo {
	a.DO = *do.(*gen.DO)
	return a
}
