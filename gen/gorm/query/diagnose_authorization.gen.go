// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"code.byted.org/volcengine-support/cloud-sherlock/gen/gorm/model"
)

func newDiagnoseAuthorization(db *gorm.DB, opts ...gen.DOOption) diagnoseAuthorization {
	_diagnoseAuthorization := diagnoseAuthorization{}

	_diagnoseAuthorization.diagnoseAuthorizationDo.UseDB(db, opts...)
	_diagnoseAuthorization.diagnoseAuthorizationDo.UseModel(&model.DiagnoseAuthorization{})

	tableName := _diagnoseAuthorization.diagnoseAuthorizationDo.TableName()
	_diagnoseAuthorization.ALL = field.NewAsterisk(tableName)
	_diagnoseAuthorization.ID = field.NewInt64(tableName, "id")
	_diagnoseAuthorization.AuthorizationID = field.NewString(tableName, "authorization_id")
	_diagnoseAuthorization.ApplicationPolicy = field.NewString(tableName, "application_policy")
	_diagnoseAuthorization.CreatedTime = field.NewInt32(tableName, "created_time")
	_diagnoseAuthorization.UpdateTime = field.NewInt32(tableName, "update_time")
	_diagnoseAuthorization.StartTime = field.NewInt32(tableName, "start_time")
	_diagnoseAuthorization.EndTime = field.NewInt32(tableName, "end_time")
	_diagnoseAuthorization.ExpiredTime = field.NewInt32(tableName, "expired_time")
	_diagnoseAuthorization.Status = field.NewInt32(tableName, "status")
	_diagnoseAuthorization.AccountID = field.NewString(tableName, "account_id")
	_diagnoseAuthorization.ApplyUser = field.NewString(tableName, "apply_user")
	_diagnoseAuthorization.TicketID = field.NewString(tableName, "ticket_id")
	_diagnoseAuthorization.DiagnoseTemplateName = field.NewString(tableName, "diagnose_template_name")
	_diagnoseAuthorization.ProductCategoryIds = field.NewString(tableName, "product_category_ids")
	_diagnoseAuthorization.InstanceIds = field.NewString(tableName, "instance_ids")

	_diagnoseAuthorization.fillFieldMap()

	return _diagnoseAuthorization
}

// diagnoseAuthorization 诊断授权历史表
type diagnoseAuthorization struct {
	diagnoseAuthorizationDo diagnoseAuthorizationDo

	ALL                  field.Asterisk
	ID                   field.Int64  // 主键
	AuthorizationID      field.String // 远程运维平台数据库中该条数据对应的授权id
	ApplicationPolicy    field.String // 策略名称(申请策略)，名称之间用“，”隔开
	CreatedTime          field.Int32  // 授权提交时间（发起申请的时间，秒级时间戳）
	UpdateTime           field.Int32  // 更新时间（可借助该字段同步官网的数据）
	StartTime            field.Int32  // 授权开始时间（通过后，秒级时间戳）
	EndTime              field.Int32  // 授权结束时间（秒级时间戳）
	ExpiredTime          field.Int32  // 授权过期时间
	Status               field.Int32  // 状态：1. 待审批；2. 生效中；3. 拒绝；4. 结束；5. 过期
	AccountID            field.String // 火山账号id
	ApplyUser            field.String // 申请人邮箱
	TicketID             field.String // 工单id
	DiagnoseTemplateName field.String // 诊断场景名称
	ProductCategoryIds   field.String // 产品类型id
	InstanceIds          field.String // 本次授权的实例ID信息

	fieldMap map[string]field.Expr
}

func (d diagnoseAuthorization) Table(newTableName string) *diagnoseAuthorization {
	d.diagnoseAuthorizationDo.UseTable(newTableName)
	return d.updateTableName(newTableName)
}

func (d diagnoseAuthorization) As(alias string) *diagnoseAuthorization {
	d.diagnoseAuthorizationDo.DO = *(d.diagnoseAuthorizationDo.As(alias).(*gen.DO))
	return d.updateTableName(alias)
}

func (d *diagnoseAuthorization) updateTableName(table string) *diagnoseAuthorization {
	d.ALL = field.NewAsterisk(table)
	d.ID = field.NewInt64(table, "id")
	d.AuthorizationID = field.NewString(table, "authorization_id")
	d.ApplicationPolicy = field.NewString(table, "application_policy")
	d.CreatedTime = field.NewInt32(table, "created_time")
	d.UpdateTime = field.NewInt32(table, "update_time")
	d.StartTime = field.NewInt32(table, "start_time")
	d.EndTime = field.NewInt32(table, "end_time")
	d.ExpiredTime = field.NewInt32(table, "expired_time")
	d.Status = field.NewInt32(table, "status")
	d.AccountID = field.NewString(table, "account_id")
	d.ApplyUser = field.NewString(table, "apply_user")
	d.TicketID = field.NewString(table, "ticket_id")
	d.DiagnoseTemplateName = field.NewString(table, "diagnose_template_name")
	d.ProductCategoryIds = field.NewString(table, "product_category_ids")
	d.InstanceIds = field.NewString(table, "instance_ids")

	d.fillFieldMap()

	return d
}

func (d *diagnoseAuthorization) WithContext(ctx context.Context) *diagnoseAuthorizationDo {
	return d.diagnoseAuthorizationDo.WithContext(ctx)
}

func (d diagnoseAuthorization) TableName() string { return d.diagnoseAuthorizationDo.TableName() }

func (d diagnoseAuthorization) Alias() string { return d.diagnoseAuthorizationDo.Alias() }

func (d diagnoseAuthorization) Columns(cols ...field.Expr) gen.Columns {
	return d.diagnoseAuthorizationDo.Columns(cols...)
}

func (d *diagnoseAuthorization) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := d.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (d *diagnoseAuthorization) fillFieldMap() {
	d.fieldMap = make(map[string]field.Expr, 15)
	d.fieldMap["id"] = d.ID
	d.fieldMap["authorization_id"] = d.AuthorizationID
	d.fieldMap["application_policy"] = d.ApplicationPolicy
	d.fieldMap["created_time"] = d.CreatedTime
	d.fieldMap["update_time"] = d.UpdateTime
	d.fieldMap["start_time"] = d.StartTime
	d.fieldMap["end_time"] = d.EndTime
	d.fieldMap["expired_time"] = d.ExpiredTime
	d.fieldMap["status"] = d.Status
	d.fieldMap["account_id"] = d.AccountID
	d.fieldMap["apply_user"] = d.ApplyUser
	d.fieldMap["ticket_id"] = d.TicketID
	d.fieldMap["diagnose_template_name"] = d.DiagnoseTemplateName
	d.fieldMap["product_category_ids"] = d.ProductCategoryIds
	d.fieldMap["instance_ids"] = d.InstanceIds
}

func (d diagnoseAuthorization) clone(db *gorm.DB) diagnoseAuthorization {
	d.diagnoseAuthorizationDo.ReplaceConnPool(db.Statement.ConnPool)
	return d
}

func (d diagnoseAuthorization) replaceDB(db *gorm.DB) diagnoseAuthorization {
	d.diagnoseAuthorizationDo.ReplaceDB(db)
	return d
}

type diagnoseAuthorizationDo struct{ gen.DO }

func (d diagnoseAuthorizationDo) Debug() *diagnoseAuthorizationDo {
	return d.withDO(d.DO.Debug())
}

func (d diagnoseAuthorizationDo) WithContext(ctx context.Context) *diagnoseAuthorizationDo {
	return d.withDO(d.DO.WithContext(ctx))
}

func (d diagnoseAuthorizationDo) ReadDB() *diagnoseAuthorizationDo {
	return d.Clauses(dbresolver.Read)
}

func (d diagnoseAuthorizationDo) WriteDB() *diagnoseAuthorizationDo {
	return d.Clauses(dbresolver.Write)
}

func (d diagnoseAuthorizationDo) Session(config *gorm.Session) *diagnoseAuthorizationDo {
	return d.withDO(d.DO.Session(config))
}

func (d diagnoseAuthorizationDo) Clauses(conds ...clause.Expression) *diagnoseAuthorizationDo {
	return d.withDO(d.DO.Clauses(conds...))
}

func (d diagnoseAuthorizationDo) Returning(value interface{}, columns ...string) *diagnoseAuthorizationDo {
	return d.withDO(d.DO.Returning(value, columns...))
}

func (d diagnoseAuthorizationDo) Not(conds ...gen.Condition) *diagnoseAuthorizationDo {
	return d.withDO(d.DO.Not(conds...))
}

func (d diagnoseAuthorizationDo) Or(conds ...gen.Condition) *diagnoseAuthorizationDo {
	return d.withDO(d.DO.Or(conds...))
}

func (d diagnoseAuthorizationDo) Select(conds ...field.Expr) *diagnoseAuthorizationDo {
	return d.withDO(d.DO.Select(conds...))
}

func (d diagnoseAuthorizationDo) Where(conds ...gen.Condition) *diagnoseAuthorizationDo {
	return d.withDO(d.DO.Where(conds...))
}

func (d diagnoseAuthorizationDo) Order(conds ...field.Expr) *diagnoseAuthorizationDo {
	return d.withDO(d.DO.Order(conds...))
}

func (d diagnoseAuthorizationDo) Distinct(cols ...field.Expr) *diagnoseAuthorizationDo {
	return d.withDO(d.DO.Distinct(cols...))
}

func (d diagnoseAuthorizationDo) Omit(cols ...field.Expr) *diagnoseAuthorizationDo {
	return d.withDO(d.DO.Omit(cols...))
}

func (d diagnoseAuthorizationDo) Join(table schema.Tabler, on ...field.Expr) *diagnoseAuthorizationDo {
	return d.withDO(d.DO.Join(table, on...))
}

func (d diagnoseAuthorizationDo) LeftJoin(table schema.Tabler, on ...field.Expr) *diagnoseAuthorizationDo {
	return d.withDO(d.DO.LeftJoin(table, on...))
}

func (d diagnoseAuthorizationDo) RightJoin(table schema.Tabler, on ...field.Expr) *diagnoseAuthorizationDo {
	return d.withDO(d.DO.RightJoin(table, on...))
}

func (d diagnoseAuthorizationDo) Group(cols ...field.Expr) *diagnoseAuthorizationDo {
	return d.withDO(d.DO.Group(cols...))
}

func (d diagnoseAuthorizationDo) Having(conds ...gen.Condition) *diagnoseAuthorizationDo {
	return d.withDO(d.DO.Having(conds...))
}

func (d diagnoseAuthorizationDo) Limit(limit int) *diagnoseAuthorizationDo {
	return d.withDO(d.DO.Limit(limit))
}

func (d diagnoseAuthorizationDo) Offset(offset int) *diagnoseAuthorizationDo {
	return d.withDO(d.DO.Offset(offset))
}

func (d diagnoseAuthorizationDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *diagnoseAuthorizationDo {
	return d.withDO(d.DO.Scopes(funcs...))
}

func (d diagnoseAuthorizationDo) Unscoped() *diagnoseAuthorizationDo {
	return d.withDO(d.DO.Unscoped())
}

func (d diagnoseAuthorizationDo) Create(values ...*model.DiagnoseAuthorization) error {
	if len(values) == 0 {
		return nil
	}
	return d.DO.Create(values)
}

func (d diagnoseAuthorizationDo) CreateInBatches(values []*model.DiagnoseAuthorization, batchSize int) error {
	return d.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (d diagnoseAuthorizationDo) Save(values ...*model.DiagnoseAuthorization) error {
	if len(values) == 0 {
		return nil
	}
	return d.DO.Save(values)
}

func (d diagnoseAuthorizationDo) First() (*model.DiagnoseAuthorization, error) {
	if result, err := d.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.DiagnoseAuthorization), nil
	}
}

func (d diagnoseAuthorizationDo) Take() (*model.DiagnoseAuthorization, error) {
	if result, err := d.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.DiagnoseAuthorization), nil
	}
}

func (d diagnoseAuthorizationDo) Last() (*model.DiagnoseAuthorization, error) {
	if result, err := d.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.DiagnoseAuthorization), nil
	}
}

func (d diagnoseAuthorizationDo) Find() ([]*model.DiagnoseAuthorization, error) {
	result, err := d.DO.Find()
	return result.([]*model.DiagnoseAuthorization), err
}

func (d diagnoseAuthorizationDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.DiagnoseAuthorization, err error) {
	buf := make([]*model.DiagnoseAuthorization, 0, batchSize)
	err = d.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (d diagnoseAuthorizationDo) FindInBatches(result *[]*model.DiagnoseAuthorization, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return d.DO.FindInBatches(result, batchSize, fc)
}

func (d diagnoseAuthorizationDo) Attrs(attrs ...field.AssignExpr) *diagnoseAuthorizationDo {
	return d.withDO(d.DO.Attrs(attrs...))
}

func (d diagnoseAuthorizationDo) Assign(attrs ...field.AssignExpr) *diagnoseAuthorizationDo {
	return d.withDO(d.DO.Assign(attrs...))
}

func (d diagnoseAuthorizationDo) Joins(fields ...field.RelationField) *diagnoseAuthorizationDo {
	for _, _f := range fields {
		d = *d.withDO(d.DO.Joins(_f))
	}
	return &d
}

func (d diagnoseAuthorizationDo) Preload(fields ...field.RelationField) *diagnoseAuthorizationDo {
	for _, _f := range fields {
		d = *d.withDO(d.DO.Preload(_f))
	}
	return &d
}

func (d diagnoseAuthorizationDo) FirstOrInit() (*model.DiagnoseAuthorization, error) {
	if result, err := d.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.DiagnoseAuthorization), nil
	}
}

func (d diagnoseAuthorizationDo) FirstOrCreate() (*model.DiagnoseAuthorization, error) {
	if result, err := d.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.DiagnoseAuthorization), nil
	}
}

func (d diagnoseAuthorizationDo) FindByPage(offset int, limit int) (result []*model.DiagnoseAuthorization, count int64, err error) {
	result, err = d.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = d.Offset(-1).Limit(-1).Count()
	return
}

func (d diagnoseAuthorizationDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = d.Count()
	if err != nil {
		return
	}

	err = d.Offset(offset).Limit(limit).Scan(result)
	return
}

func (d diagnoseAuthorizationDo) Scan(result interface{}) (err error) {
	return d.DO.Scan(result)
}

func (d diagnoseAuthorizationDo) Delete(models ...*model.DiagnoseAuthorization) (result gen.ResultInfo, err error) {
	return d.DO.Delete(models)
}

func (d *diagnoseAuthorizationDo) withDO(do gen.Dao) *diagnoseAuthorizationDo {
	d.DO = *do.(*gen.DO)
	return d
}
