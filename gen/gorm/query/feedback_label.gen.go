// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"code.byted.org/volcengine-support/cloud-sherlock/gen/gorm/model"
)

func newFeedbackLabel(db *gorm.DB, opts ...gen.DOOption) feedbackLabel {
	_feedbackLabel := feedbackLabel{}

	_feedbackLabel.feedbackLabelDo.UseDB(db, opts...)
	_feedbackLabel.feedbackLabelDo.UseModel(&model.FeedbackLabel{})

	tableName := _feedbackLabel.feedbackLabelDo.TableName()
	_feedbackLabel.ALL = field.NewAsterisk(tableName)
	_feedbackLabel.ID = field.NewInt64(tableName, "id")
	_feedbackLabel.Stage = field.NewString(tableName, "stage")
	_feedbackLabel.LabelKey = field.NewString(tableName, "label_key")
	_feedbackLabel.LabelName = field.NewString(tableName, "label_name")
	_feedbackLabel.HandleUserIds = field.NewString(tableName, "handle_user_ids")

	_feedbackLabel.fillFieldMap()

	return _feedbackLabel
}

// feedbackLabel 反馈标签信息表
type feedbackLabel struct {
	feedbackLabelDo feedbackLabelDo

	ALL           field.Asterisk
	ID            field.Int64  // 自增ID
	Stage         field.String // 阶段
	LabelKey      field.String // 标签Key
	LabelName     field.String // 标签名
	HandleUserIds field.String // 处理人

	fieldMap map[string]field.Expr
}

func (f feedbackLabel) Table(newTableName string) *feedbackLabel {
	f.feedbackLabelDo.UseTable(newTableName)
	return f.updateTableName(newTableName)
}

func (f feedbackLabel) As(alias string) *feedbackLabel {
	f.feedbackLabelDo.DO = *(f.feedbackLabelDo.As(alias).(*gen.DO))
	return f.updateTableName(alias)
}

func (f *feedbackLabel) updateTableName(table string) *feedbackLabel {
	f.ALL = field.NewAsterisk(table)
	f.ID = field.NewInt64(table, "id")
	f.Stage = field.NewString(table, "stage")
	f.LabelKey = field.NewString(table, "label_key")
	f.LabelName = field.NewString(table, "label_name")
	f.HandleUserIds = field.NewString(table, "handle_user_ids")

	f.fillFieldMap()

	return f
}

func (f *feedbackLabel) WithContext(ctx context.Context) *feedbackLabelDo {
	return f.feedbackLabelDo.WithContext(ctx)
}

func (f feedbackLabel) TableName() string { return f.feedbackLabelDo.TableName() }

func (f feedbackLabel) Alias() string { return f.feedbackLabelDo.Alias() }

func (f feedbackLabel) Columns(cols ...field.Expr) gen.Columns {
	return f.feedbackLabelDo.Columns(cols...)
}

func (f *feedbackLabel) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := f.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (f *feedbackLabel) fillFieldMap() {
	f.fieldMap = make(map[string]field.Expr, 5)
	f.fieldMap["id"] = f.ID
	f.fieldMap["stage"] = f.Stage
	f.fieldMap["label_key"] = f.LabelKey
	f.fieldMap["label_name"] = f.LabelName
	f.fieldMap["handle_user_ids"] = f.HandleUserIds
}

func (f feedbackLabel) clone(db *gorm.DB) feedbackLabel {
	f.feedbackLabelDo.ReplaceConnPool(db.Statement.ConnPool)
	return f
}

func (f feedbackLabel) replaceDB(db *gorm.DB) feedbackLabel {
	f.feedbackLabelDo.ReplaceDB(db)
	return f
}

type feedbackLabelDo struct{ gen.DO }

func (f feedbackLabelDo) Debug() *feedbackLabelDo {
	return f.withDO(f.DO.Debug())
}

func (f feedbackLabelDo) WithContext(ctx context.Context) *feedbackLabelDo {
	return f.withDO(f.DO.WithContext(ctx))
}

func (f feedbackLabelDo) ReadDB() *feedbackLabelDo {
	return f.Clauses(dbresolver.Read)
}

func (f feedbackLabelDo) WriteDB() *feedbackLabelDo {
	return f.Clauses(dbresolver.Write)
}

func (f feedbackLabelDo) Session(config *gorm.Session) *feedbackLabelDo {
	return f.withDO(f.DO.Session(config))
}

func (f feedbackLabelDo) Clauses(conds ...clause.Expression) *feedbackLabelDo {
	return f.withDO(f.DO.Clauses(conds...))
}

func (f feedbackLabelDo) Returning(value interface{}, columns ...string) *feedbackLabelDo {
	return f.withDO(f.DO.Returning(value, columns...))
}

func (f feedbackLabelDo) Not(conds ...gen.Condition) *feedbackLabelDo {
	return f.withDO(f.DO.Not(conds...))
}

func (f feedbackLabelDo) Or(conds ...gen.Condition) *feedbackLabelDo {
	return f.withDO(f.DO.Or(conds...))
}

func (f feedbackLabelDo) Select(conds ...field.Expr) *feedbackLabelDo {
	return f.withDO(f.DO.Select(conds...))
}

func (f feedbackLabelDo) Where(conds ...gen.Condition) *feedbackLabelDo {
	return f.withDO(f.DO.Where(conds...))
}

func (f feedbackLabelDo) Order(conds ...field.Expr) *feedbackLabelDo {
	return f.withDO(f.DO.Order(conds...))
}

func (f feedbackLabelDo) Distinct(cols ...field.Expr) *feedbackLabelDo {
	return f.withDO(f.DO.Distinct(cols...))
}

func (f feedbackLabelDo) Omit(cols ...field.Expr) *feedbackLabelDo {
	return f.withDO(f.DO.Omit(cols...))
}

func (f feedbackLabelDo) Join(table schema.Tabler, on ...field.Expr) *feedbackLabelDo {
	return f.withDO(f.DO.Join(table, on...))
}

func (f feedbackLabelDo) LeftJoin(table schema.Tabler, on ...field.Expr) *feedbackLabelDo {
	return f.withDO(f.DO.LeftJoin(table, on...))
}

func (f feedbackLabelDo) RightJoin(table schema.Tabler, on ...field.Expr) *feedbackLabelDo {
	return f.withDO(f.DO.RightJoin(table, on...))
}

func (f feedbackLabelDo) Group(cols ...field.Expr) *feedbackLabelDo {
	return f.withDO(f.DO.Group(cols...))
}

func (f feedbackLabelDo) Having(conds ...gen.Condition) *feedbackLabelDo {
	return f.withDO(f.DO.Having(conds...))
}

func (f feedbackLabelDo) Limit(limit int) *feedbackLabelDo {
	return f.withDO(f.DO.Limit(limit))
}

func (f feedbackLabelDo) Offset(offset int) *feedbackLabelDo {
	return f.withDO(f.DO.Offset(offset))
}

func (f feedbackLabelDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *feedbackLabelDo {
	return f.withDO(f.DO.Scopes(funcs...))
}

func (f feedbackLabelDo) Unscoped() *feedbackLabelDo {
	return f.withDO(f.DO.Unscoped())
}

func (f feedbackLabelDo) Create(values ...*model.FeedbackLabel) error {
	if len(values) == 0 {
		return nil
	}
	return f.DO.Create(values)
}

func (f feedbackLabelDo) CreateInBatches(values []*model.FeedbackLabel, batchSize int) error {
	return f.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (f feedbackLabelDo) Save(values ...*model.FeedbackLabel) error {
	if len(values) == 0 {
		return nil
	}
	return f.DO.Save(values)
}

func (f feedbackLabelDo) First() (*model.FeedbackLabel, error) {
	if result, err := f.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.FeedbackLabel), nil
	}
}

func (f feedbackLabelDo) Take() (*model.FeedbackLabel, error) {
	if result, err := f.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.FeedbackLabel), nil
	}
}

func (f feedbackLabelDo) Last() (*model.FeedbackLabel, error) {
	if result, err := f.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.FeedbackLabel), nil
	}
}

func (f feedbackLabelDo) Find() ([]*model.FeedbackLabel, error) {
	result, err := f.DO.Find()
	return result.([]*model.FeedbackLabel), err
}

func (f feedbackLabelDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.FeedbackLabel, err error) {
	buf := make([]*model.FeedbackLabel, 0, batchSize)
	err = f.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (f feedbackLabelDo) FindInBatches(result *[]*model.FeedbackLabel, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return f.DO.FindInBatches(result, batchSize, fc)
}

func (f feedbackLabelDo) Attrs(attrs ...field.AssignExpr) *feedbackLabelDo {
	return f.withDO(f.DO.Attrs(attrs...))
}

func (f feedbackLabelDo) Assign(attrs ...field.AssignExpr) *feedbackLabelDo {
	return f.withDO(f.DO.Assign(attrs...))
}

func (f feedbackLabelDo) Joins(fields ...field.RelationField) *feedbackLabelDo {
	for _, _f := range fields {
		f = *f.withDO(f.DO.Joins(_f))
	}
	return &f
}

func (f feedbackLabelDo) Preload(fields ...field.RelationField) *feedbackLabelDo {
	for _, _f := range fields {
		f = *f.withDO(f.DO.Preload(_f))
	}
	return &f
}

func (f feedbackLabelDo) FirstOrInit() (*model.FeedbackLabel, error) {
	if result, err := f.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.FeedbackLabel), nil
	}
}

func (f feedbackLabelDo) FirstOrCreate() (*model.FeedbackLabel, error) {
	if result, err := f.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.FeedbackLabel), nil
	}
}

func (f feedbackLabelDo) FindByPage(offset int, limit int) (result []*model.FeedbackLabel, count int64, err error) {
	result, err = f.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = f.Offset(-1).Limit(-1).Count()
	return
}

func (f feedbackLabelDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = f.Count()
	if err != nil {
		return
	}

	err = f.Offset(offset).Limit(limit).Scan(result)
	return
}

func (f feedbackLabelDo) Scan(result interface{}) (err error) {
	return f.DO.Scan(result)
}

func (f feedbackLabelDo) Delete(models ...*model.FeedbackLabel) (result gen.ResultInfo, err error) {
	return f.DO.Delete(models)
}

func (f *feedbackLabelDo) withDO(do gen.Dao) *feedbackLabelDo {
	f.DO = *do.(*gen.DO)
	return f
}
