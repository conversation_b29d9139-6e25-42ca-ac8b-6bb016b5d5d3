// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"code.byted.org/volcengine-support/cloud-sherlock/gen/gorm/model"
)

func newFeedbackV2(db *gorm.DB, opts ...gen.DOOption) feedbackV2 {
	_feedbackV2 := feedbackV2{}

	_feedbackV2.feedbackV2Do.UseDB(db, opts...)
	_feedbackV2.feedbackV2Do.UseModel(&model.FeedbackV2{})

	tableName := _feedbackV2.feedbackV2Do.TableName()
	_feedbackV2.ALL = field.NewAsterisk(tableName)
	_feedbackV2.ID = field.NewInt64(tableName, "id")
	_feedbackV2.SessionID = field.NewString(tableName, "session_id")
	_feedbackV2.MessageID = field.NewString(tableName, "message_id")
	_feedbackV2.Stage = field.NewString(tableName, "stage")
	_feedbackV2.IsLike = field.NewBool(tableName, "is_like")
	_feedbackV2.FeedbackUserID = field.NewString(tableName, "feedback_user_id")
	_feedbackV2.ProductCategoryID = field.NewString(tableName, "product_category_id")
	_feedbackV2.TicketID = field.NewString(tableName, "ticket_id")
	_feedbackV2.DiagnoseTaskRunID = field.NewInt64(tableName, "diagnose_task_run_id")
	_feedbackV2.ReportLink = field.NewString(tableName, "report_link")
	_feedbackV2.FeedbackLabelIds = field.NewString(tableName, "feedback_label_ids")
	_feedbackV2.Description = field.NewString(tableName, "description")
	_feedbackV2.FeedbackTime = field.NewTime(tableName, "feedback_time")
	_feedbackV2.HandleUserIds = field.NewString(tableName, "handle_user_ids")
	_feedbackV2.Status = field.NewString(tableName, "status")
	_feedbackV2.StartHandleUserID = field.NewString(tableName, "start_handle_user_id")
	_feedbackV2.EndHandleUserID = field.NewString(tableName, "end_handle_user_id")
	_feedbackV2.StartHandleTime = field.NewTime(tableName, "start_handle_time")
	_feedbackV2.EndHandleTime = field.NewTime(tableName, "end_handle_time")
	_feedbackV2.Title = field.NewString(tableName, "title")
	_feedbackV2.HandleResults = field.NewString(tableName, "handle_results")
	_feedbackV2.Valid = field.NewBool(tableName, "valid")
	_feedbackV2.FeedbackDepartment = field.NewString(tableName, "feedback_department")
	_feedbackV2.SubProduct = field.NewString(tableName, "sub_product")

	_feedbackV2.fillFieldMap()

	return _feedbackV2
}

// feedbackV2 用户反馈信息表
type feedbackV2 struct {
	feedbackV2Do feedbackV2Do

	ALL                field.Asterisk
	ID                 field.Int64  // 自增ID
	SessionID          field.String // 会话ID
	MessageID          field.String // 消息ID
	Stage              field.String // 阶段
	IsLike             field.Bool   // 点赞
	FeedbackUserID     field.String // 反馈人邮箱
	ProductCategoryID  field.String // 产品类型ID（产管）
	TicketID           field.String // 工单ID
	DiagnoseTaskRunID  field.Int64  // 诊断执行ID
	ReportLink         field.String // 诊断报告链接
	FeedbackLabelIds   field.String // 反馈标签IDs
	Description        field.String // 其他描述
	FeedbackTime       field.Time   // 反馈时间
	HandleUserIds      field.String // 处理人邮箱数组
	Status             field.String // 状态
	StartHandleUserID  field.String // 开始处理人邮箱
	EndHandleUserID    field.String // 结束处理人邮箱
	StartHandleTime    field.Time   // 开始处理时间
	EndHandleTime      field.Time   // 结束处理时间
	Title              field.String // 标题
	HandleResults      field.String // 处理结果
	Valid              field.Bool   // 是否生效
	FeedbackDepartment field.String // 反馈人部门
	SubProduct         field.String // 子产品类型

	fieldMap map[string]field.Expr
}

func (f feedbackV2) Table(newTableName string) *feedbackV2 {
	f.feedbackV2Do.UseTable(newTableName)
	return f.updateTableName(newTableName)
}

func (f feedbackV2) As(alias string) *feedbackV2 {
	f.feedbackV2Do.DO = *(f.feedbackV2Do.As(alias).(*gen.DO))
	return f.updateTableName(alias)
}

func (f *feedbackV2) updateTableName(table string) *feedbackV2 {
	f.ALL = field.NewAsterisk(table)
	f.ID = field.NewInt64(table, "id")
	f.SessionID = field.NewString(table, "session_id")
	f.MessageID = field.NewString(table, "message_id")
	f.Stage = field.NewString(table, "stage")
	f.IsLike = field.NewBool(table, "is_like")
	f.FeedbackUserID = field.NewString(table, "feedback_user_id")
	f.ProductCategoryID = field.NewString(table, "product_category_id")
	f.TicketID = field.NewString(table, "ticket_id")
	f.DiagnoseTaskRunID = field.NewInt64(table, "diagnose_task_run_id")
	f.ReportLink = field.NewString(table, "report_link")
	f.FeedbackLabelIds = field.NewString(table, "feedback_label_ids")
	f.Description = field.NewString(table, "description")
	f.FeedbackTime = field.NewTime(table, "feedback_time")
	f.HandleUserIds = field.NewString(table, "handle_user_ids")
	f.Status = field.NewString(table, "status")
	f.StartHandleUserID = field.NewString(table, "start_handle_user_id")
	f.EndHandleUserID = field.NewString(table, "end_handle_user_id")
	f.StartHandleTime = field.NewTime(table, "start_handle_time")
	f.EndHandleTime = field.NewTime(table, "end_handle_time")
	f.Title = field.NewString(table, "title")
	f.HandleResults = field.NewString(table, "handle_results")
	f.Valid = field.NewBool(table, "valid")
	f.FeedbackDepartment = field.NewString(table, "feedback_department")
	f.SubProduct = field.NewString(table, "sub_product")

	f.fillFieldMap()

	return f
}

func (f *feedbackV2) WithContext(ctx context.Context) *feedbackV2Do {
	return f.feedbackV2Do.WithContext(ctx)
}

func (f feedbackV2) TableName() string { return f.feedbackV2Do.TableName() }

func (f feedbackV2) Alias() string { return f.feedbackV2Do.Alias() }

func (f feedbackV2) Columns(cols ...field.Expr) gen.Columns { return f.feedbackV2Do.Columns(cols...) }

func (f *feedbackV2) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := f.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (f *feedbackV2) fillFieldMap() {
	f.fieldMap = make(map[string]field.Expr, 24)
	f.fieldMap["id"] = f.ID
	f.fieldMap["session_id"] = f.SessionID
	f.fieldMap["message_id"] = f.MessageID
	f.fieldMap["stage"] = f.Stage
	f.fieldMap["is_like"] = f.IsLike
	f.fieldMap["feedback_user_id"] = f.FeedbackUserID
	f.fieldMap["product_category_id"] = f.ProductCategoryID
	f.fieldMap["ticket_id"] = f.TicketID
	f.fieldMap["diagnose_task_run_id"] = f.DiagnoseTaskRunID
	f.fieldMap["report_link"] = f.ReportLink
	f.fieldMap["feedback_label_ids"] = f.FeedbackLabelIds
	f.fieldMap["description"] = f.Description
	f.fieldMap["feedback_time"] = f.FeedbackTime
	f.fieldMap["handle_user_ids"] = f.HandleUserIds
	f.fieldMap["status"] = f.Status
	f.fieldMap["start_handle_user_id"] = f.StartHandleUserID
	f.fieldMap["end_handle_user_id"] = f.EndHandleUserID
	f.fieldMap["start_handle_time"] = f.StartHandleTime
	f.fieldMap["end_handle_time"] = f.EndHandleTime
	f.fieldMap["title"] = f.Title
	f.fieldMap["handle_results"] = f.HandleResults
	f.fieldMap["valid"] = f.Valid
	f.fieldMap["feedback_department"] = f.FeedbackDepartment
	f.fieldMap["sub_product"] = f.SubProduct
}

func (f feedbackV2) clone(db *gorm.DB) feedbackV2 {
	f.feedbackV2Do.ReplaceConnPool(db.Statement.ConnPool)
	return f
}

func (f feedbackV2) replaceDB(db *gorm.DB) feedbackV2 {
	f.feedbackV2Do.ReplaceDB(db)
	return f
}

type feedbackV2Do struct{ gen.DO }

func (f feedbackV2Do) Debug() *feedbackV2Do {
	return f.withDO(f.DO.Debug())
}

func (f feedbackV2Do) WithContext(ctx context.Context) *feedbackV2Do {
	return f.withDO(f.DO.WithContext(ctx))
}

func (f feedbackV2Do) ReadDB() *feedbackV2Do {
	return f.Clauses(dbresolver.Read)
}

func (f feedbackV2Do) WriteDB() *feedbackV2Do {
	return f.Clauses(dbresolver.Write)
}

func (f feedbackV2Do) Session(config *gorm.Session) *feedbackV2Do {
	return f.withDO(f.DO.Session(config))
}

func (f feedbackV2Do) Clauses(conds ...clause.Expression) *feedbackV2Do {
	return f.withDO(f.DO.Clauses(conds...))
}

func (f feedbackV2Do) Returning(value interface{}, columns ...string) *feedbackV2Do {
	return f.withDO(f.DO.Returning(value, columns...))
}

func (f feedbackV2Do) Not(conds ...gen.Condition) *feedbackV2Do {
	return f.withDO(f.DO.Not(conds...))
}

func (f feedbackV2Do) Or(conds ...gen.Condition) *feedbackV2Do {
	return f.withDO(f.DO.Or(conds...))
}

func (f feedbackV2Do) Select(conds ...field.Expr) *feedbackV2Do {
	return f.withDO(f.DO.Select(conds...))
}

func (f feedbackV2Do) Where(conds ...gen.Condition) *feedbackV2Do {
	return f.withDO(f.DO.Where(conds...))
}

func (f feedbackV2Do) Order(conds ...field.Expr) *feedbackV2Do {
	return f.withDO(f.DO.Order(conds...))
}

func (f feedbackV2Do) Distinct(cols ...field.Expr) *feedbackV2Do {
	return f.withDO(f.DO.Distinct(cols...))
}

func (f feedbackV2Do) Omit(cols ...field.Expr) *feedbackV2Do {
	return f.withDO(f.DO.Omit(cols...))
}

func (f feedbackV2Do) Join(table schema.Tabler, on ...field.Expr) *feedbackV2Do {
	return f.withDO(f.DO.Join(table, on...))
}

func (f feedbackV2Do) LeftJoin(table schema.Tabler, on ...field.Expr) *feedbackV2Do {
	return f.withDO(f.DO.LeftJoin(table, on...))
}

func (f feedbackV2Do) RightJoin(table schema.Tabler, on ...field.Expr) *feedbackV2Do {
	return f.withDO(f.DO.RightJoin(table, on...))
}

func (f feedbackV2Do) Group(cols ...field.Expr) *feedbackV2Do {
	return f.withDO(f.DO.Group(cols...))
}

func (f feedbackV2Do) Having(conds ...gen.Condition) *feedbackV2Do {
	return f.withDO(f.DO.Having(conds...))
}

func (f feedbackV2Do) Limit(limit int) *feedbackV2Do {
	return f.withDO(f.DO.Limit(limit))
}

func (f feedbackV2Do) Offset(offset int) *feedbackV2Do {
	return f.withDO(f.DO.Offset(offset))
}

func (f feedbackV2Do) Scopes(funcs ...func(gen.Dao) gen.Dao) *feedbackV2Do {
	return f.withDO(f.DO.Scopes(funcs...))
}

func (f feedbackV2Do) Unscoped() *feedbackV2Do {
	return f.withDO(f.DO.Unscoped())
}

func (f feedbackV2Do) Create(values ...*model.FeedbackV2) error {
	if len(values) == 0 {
		return nil
	}
	return f.DO.Create(values)
}

func (f feedbackV2Do) CreateInBatches(values []*model.FeedbackV2, batchSize int) error {
	return f.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (f feedbackV2Do) Save(values ...*model.FeedbackV2) error {
	if len(values) == 0 {
		return nil
	}
	return f.DO.Save(values)
}

func (f feedbackV2Do) First() (*model.FeedbackV2, error) {
	if result, err := f.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.FeedbackV2), nil
	}
}

func (f feedbackV2Do) Take() (*model.FeedbackV2, error) {
	if result, err := f.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.FeedbackV2), nil
	}
}

func (f feedbackV2Do) Last() (*model.FeedbackV2, error) {
	if result, err := f.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.FeedbackV2), nil
	}
}

func (f feedbackV2Do) Find() ([]*model.FeedbackV2, error) {
	result, err := f.DO.Find()
	return result.([]*model.FeedbackV2), err
}

func (f feedbackV2Do) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.FeedbackV2, err error) {
	buf := make([]*model.FeedbackV2, 0, batchSize)
	err = f.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (f feedbackV2Do) FindInBatches(result *[]*model.FeedbackV2, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return f.DO.FindInBatches(result, batchSize, fc)
}

func (f feedbackV2Do) Attrs(attrs ...field.AssignExpr) *feedbackV2Do {
	return f.withDO(f.DO.Attrs(attrs...))
}

func (f feedbackV2Do) Assign(attrs ...field.AssignExpr) *feedbackV2Do {
	return f.withDO(f.DO.Assign(attrs...))
}

func (f feedbackV2Do) Joins(fields ...field.RelationField) *feedbackV2Do {
	for _, _f := range fields {
		f = *f.withDO(f.DO.Joins(_f))
	}
	return &f
}

func (f feedbackV2Do) Preload(fields ...field.RelationField) *feedbackV2Do {
	for _, _f := range fields {
		f = *f.withDO(f.DO.Preload(_f))
	}
	return &f
}

func (f feedbackV2Do) FirstOrInit() (*model.FeedbackV2, error) {
	if result, err := f.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.FeedbackV2), nil
	}
}

func (f feedbackV2Do) FirstOrCreate() (*model.FeedbackV2, error) {
	if result, err := f.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.FeedbackV2), nil
	}
}

func (f feedbackV2Do) FindByPage(offset int, limit int) (result []*model.FeedbackV2, count int64, err error) {
	result, err = f.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = f.Offset(-1).Limit(-1).Count()
	return
}

func (f feedbackV2Do) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = f.Count()
	if err != nil {
		return
	}

	err = f.Offset(offset).Limit(limit).Scan(result)
	return
}

func (f feedbackV2Do) Scan(result interface{}) (err error) {
	return f.DO.Scan(result)
}

func (f feedbackV2Do) Delete(models ...*model.FeedbackV2) (result gen.ResultInfo, err error) {
	return f.DO.Delete(models)
}

func (f *feedbackV2Do) withDO(do gen.Dao) *feedbackV2Do {
	f.DO = *do.(*gen.DO)
	return f
}
