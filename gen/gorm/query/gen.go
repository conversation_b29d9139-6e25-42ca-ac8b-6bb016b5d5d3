// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"
	"database/sql"

	"gorm.io/gorm"

	"gorm.io/gen"

	"gorm.io/plugin/dbresolver"
)

func Use(db *gorm.DB, opts ...gen.DOOption) *Query {
	return &Query{
		db:                    db,
		AppAkSk:               newAppAkSk(db, opts...),
		Customer:              newCustomer(db, opts...),
		DiagnoseAuthorization: newDiagnoseAuthorization(db, opts...),
		DiagnoseItem:          newDiagnoseItem(db, opts...),
		DiagnoseResult:        newDiagnoseResult(db, opts...),
		DiagnoseResultV2:      newDiagnoseResultV2(db, opts...),
		DiagnoseTask:          newDiagnoseTask(db, opts...),
		DiagnoseTaskRun:       newDiagnoseTaskRun(db, opts...),
		DiagnoseTaskV2:        newDiagnoseTaskV2(db, opts...),
		DiagnoseTemplate:      newDiagnoseTemplate(db, opts...),
		DiscourseFeeback:      newDiscourseFeeback(db, opts...),
		DiscourseMessage:      newDiscourseMessage(db, opts...),
		DiscourseSession:      newDiscourseSession(db, opts...),
		Feedback:              newFeedback(db, opts...),
		FeedbackLabel:         newFeedbackLabel(db, opts...),
		FeedbackV2:            newFeedbackV2(db, opts...),
		ItemTemplate:          newItemTemplate(db, opts...),
		ProductCategory:       newProductCategory(db, opts...),
	}
}

type Query struct {
	db *gorm.DB

	AppAkSk               appAkSk
	Customer              customer
	DiagnoseAuthorization diagnoseAuthorization
	DiagnoseItem          diagnoseItem
	DiagnoseResult        diagnoseResult
	DiagnoseResultV2      diagnoseResultV2
	DiagnoseTask          diagnoseTask
	DiagnoseTaskRun       diagnoseTaskRun
	DiagnoseTaskV2        diagnoseTaskV2
	DiagnoseTemplate      diagnoseTemplate
	DiscourseFeeback      discourseFeeback
	DiscourseMessage      discourseMessage
	DiscourseSession      discourseSession
	Feedback              feedback
	FeedbackLabel         feedbackLabel
	FeedbackV2            feedbackV2
	ItemTemplate          itemTemplate
	ProductCategory       productCategory
}

func (q *Query) Available() bool { return q.db != nil }

func (q *Query) clone(db *gorm.DB) *Query {
	return &Query{
		db:                    db,
		AppAkSk:               q.AppAkSk.clone(db),
		Customer:              q.Customer.clone(db),
		DiagnoseAuthorization: q.DiagnoseAuthorization.clone(db),
		DiagnoseItem:          q.DiagnoseItem.clone(db),
		DiagnoseResult:        q.DiagnoseResult.clone(db),
		DiagnoseResultV2:      q.DiagnoseResultV2.clone(db),
		DiagnoseTask:          q.DiagnoseTask.clone(db),
		DiagnoseTaskRun:       q.DiagnoseTaskRun.clone(db),
		DiagnoseTaskV2:        q.DiagnoseTaskV2.clone(db),
		DiagnoseTemplate:      q.DiagnoseTemplate.clone(db),
		DiscourseFeeback:      q.DiscourseFeeback.clone(db),
		DiscourseMessage:      q.DiscourseMessage.clone(db),
		DiscourseSession:      q.DiscourseSession.clone(db),
		Feedback:              q.Feedback.clone(db),
		FeedbackLabel:         q.FeedbackLabel.clone(db),
		FeedbackV2:            q.FeedbackV2.clone(db),
		ItemTemplate:          q.ItemTemplate.clone(db),
		ProductCategory:       q.ProductCategory.clone(db),
	}
}

func (q *Query) ReadDB() *Query {
	return q.ReplaceDB(q.db.Clauses(dbresolver.Read))
}

func (q *Query) WriteDB() *Query {
	return q.ReplaceDB(q.db.Clauses(dbresolver.Write))
}

func (q *Query) ReplaceDB(db *gorm.DB) *Query {
	return &Query{
		db:                    db,
		AppAkSk:               q.AppAkSk.replaceDB(db),
		Customer:              q.Customer.replaceDB(db),
		DiagnoseAuthorization: q.DiagnoseAuthorization.replaceDB(db),
		DiagnoseItem:          q.DiagnoseItem.replaceDB(db),
		DiagnoseResult:        q.DiagnoseResult.replaceDB(db),
		DiagnoseResultV2:      q.DiagnoseResultV2.replaceDB(db),
		DiagnoseTask:          q.DiagnoseTask.replaceDB(db),
		DiagnoseTaskRun:       q.DiagnoseTaskRun.replaceDB(db),
		DiagnoseTaskV2:        q.DiagnoseTaskV2.replaceDB(db),
		DiagnoseTemplate:      q.DiagnoseTemplate.replaceDB(db),
		DiscourseFeeback:      q.DiscourseFeeback.replaceDB(db),
		DiscourseMessage:      q.DiscourseMessage.replaceDB(db),
		DiscourseSession:      q.DiscourseSession.replaceDB(db),
		Feedback:              q.Feedback.replaceDB(db),
		FeedbackLabel:         q.FeedbackLabel.replaceDB(db),
		FeedbackV2:            q.FeedbackV2.replaceDB(db),
		ItemTemplate:          q.ItemTemplate.replaceDB(db),
		ProductCategory:       q.ProductCategory.replaceDB(db),
	}
}

type queryCtx struct {
	AppAkSk               *appAkSkDo
	Customer              *customerDo
	DiagnoseAuthorization *diagnoseAuthorizationDo
	DiagnoseItem          *diagnoseItemDo
	DiagnoseResult        *diagnoseResultDo
	DiagnoseResultV2      *diagnoseResultV2Do
	DiagnoseTask          *diagnoseTaskDo
	DiagnoseTaskRun       *diagnoseTaskRunDo
	DiagnoseTaskV2        *diagnoseTaskV2Do
	DiagnoseTemplate      *diagnoseTemplateDo
	DiscourseFeeback      *discourseFeebackDo
	DiscourseMessage      *discourseMessageDo
	DiscourseSession      *discourseSessionDo
	Feedback              *feedbackDo
	FeedbackLabel         *feedbackLabelDo
	FeedbackV2            *feedbackV2Do
	ItemTemplate          *itemTemplateDo
	ProductCategory       *productCategoryDo
}

func (q *Query) WithContext(ctx context.Context) *queryCtx {
	return &queryCtx{
		AppAkSk:               q.AppAkSk.WithContext(ctx),
		Customer:              q.Customer.WithContext(ctx),
		DiagnoseAuthorization: q.DiagnoseAuthorization.WithContext(ctx),
		DiagnoseItem:          q.DiagnoseItem.WithContext(ctx),
		DiagnoseResult:        q.DiagnoseResult.WithContext(ctx),
		DiagnoseResultV2:      q.DiagnoseResultV2.WithContext(ctx),
		DiagnoseTask:          q.DiagnoseTask.WithContext(ctx),
		DiagnoseTaskRun:       q.DiagnoseTaskRun.WithContext(ctx),
		DiagnoseTaskV2:        q.DiagnoseTaskV2.WithContext(ctx),
		DiagnoseTemplate:      q.DiagnoseTemplate.WithContext(ctx),
		DiscourseFeeback:      q.DiscourseFeeback.WithContext(ctx),
		DiscourseMessage:      q.DiscourseMessage.WithContext(ctx),
		DiscourseSession:      q.DiscourseSession.WithContext(ctx),
		Feedback:              q.Feedback.WithContext(ctx),
		FeedbackLabel:         q.FeedbackLabel.WithContext(ctx),
		FeedbackV2:            q.FeedbackV2.WithContext(ctx),
		ItemTemplate:          q.ItemTemplate.WithContext(ctx),
		ProductCategory:       q.ProductCategory.WithContext(ctx),
	}
}

func (q *Query) Transaction(fc func(tx *Query) error, opts ...*sql.TxOptions) error {
	return q.db.Transaction(func(tx *gorm.DB) error { return fc(q.clone(tx)) }, opts...)
}

func (q *Query) Begin(opts ...*sql.TxOptions) *QueryTx {
	tx := q.db.Begin(opts...)
	return &QueryTx{Query: q.clone(tx), Error: tx.Error}
}

type QueryTx struct {
	*Query
	Error error
}

func (q *QueryTx) Commit() error {
	return q.db.Commit().Error
}

func (q *QueryTx) Rollback() error {
	return q.db.Rollback().Error
}

func (q *QueryTx) SavePoint(name string) error {
	return q.db.SavePoint(name).Error
}

func (q *QueryTx) RollbackTo(name string) error {
	return q.db.RollbackTo(name).Error
}
