// This file is automatically generated. Do not modify.

package diagnose_authorization

import (
	"fmt"

	"code.byted.org/volcengine-support/cloud-sherlock/gen/hertz/base"
	"code.byted.org/volcengine-support/cloud-sherlock/gen/hertz/common"
)

var _ = fmt.Sprintf

type ApplyDiagnoseAuthorizationRequest struct {
	StartTime            int32              `form:"StartTime,required" json:"StartTime,required" query:"StartTime,required"`
	ProductCategoryIDs   []int32            `form:"ProductCategoryIDs,required" json:"ProductCategoryIDs,required" query:"ProductCategoryIDs,required"`
	TicketID             string             `form:"TicketID,required" json:"TicketID,required" query:"TicketID,required"`
	DiagnoseTemplateName string             `form:"DiagnoseTemplateName,required" json:"DiagnoseTemplateName,required" query:"DiagnoseTemplateName,required"`
	InstanceIDs          map[int32][]string `form:"InstanceIDs,required" json:"InstanceIDs,required" query:"InstanceIDs,required"`
	ApplicationDuration  int32              `form:"ApplicationDuration,required" json:"ApplicationDuration,required" query:"ApplicationDuration,required"`
	ApplicationReason    string             `form:"ApplicationReason,required" json:"ApplicationReason,required" query:"ApplicationReason,required"`
	Action               string             `json:"Action,required" query:"Action,required"`
	Version              string             `json:"Version,required" query:"Version,required"`
}

type ApplyDiagnoseAuthorizationResponse struct {
	ResponseMetadata *base.ResponseMetadata            `form:"ResponseMetadata,required" json:"ResponseMetadata,required" query:"ResponseMetadata,required"`
	Result           *ApplyDiagnoseAuthorizationResult `form:"Result,required" json:"Result,required" query:"Result,required"`
}

type ApplyDiagnoseAuthorizationResult struct {
	Id *int64 `form:"ID" json:"ID,omitempty" query:"ID"`
}

type DiagnoseAuthorization struct {
	Id                   *int64   `form:"ID" json:"ID,omitempty" query:"ID"`
	ApplicationPolicy    []string `form:"ApplicationPolicy" json:"ApplicationPolicy,omitempty" query:"ApplicationPolicy"`
	CreatedTime          *string  `form:"CreatedTime" json:"CreatedTime,omitempty" query:"CreatedTime"`
	UpdateTime           *string  `form:"UpdateTime" json:"UpdateTime,omitempty" query:"UpdateTime"`
	StartTime            *string  `form:"StartTime" json:"StartTime,omitempty" query:"StartTime"`
	EndTime              *string  `form:"EndTime" json:"EndTime,omitempty" query:"EndTime"`
	ExpiredTime          *int32   `form:"ExpiredTime" json:"ExpiredTime,omitempty" query:"ExpiredTime"`
	Status               *int32   `form:"Status" json:"Status,omitempty" query:"Status"`
	AccountID            *string  `form:"AccountID" json:"AccountID,omitempty" query:"AccountID"`
	ApplyUser            *string  `form:"ApplyUser" json:"ApplyUser,omitempty" query:"ApplyUser"`
	TicketID             *string  `form:"TicketID" json:"TicketID,omitempty" query:"TicketID"`
	DiagnoseTemplateName *string  `form:"DiagnoseTemplateName" json:"DiagnoseTemplateName,omitempty" query:"DiagnoseTemplateName"`
	ProductCategoryID    []int32  `form:"ProductCategoryID" json:"ProductCategoryID,omitempty" query:"ProductCategoryID"`
	AuthorizationID      *string  `form:"AuthorizationID" json:"AuthorizationID,omitempty" query:"AuthorizationID"`
}

type ListDiagnoseAuthorizationRequest struct {
	PageNumber            int32    `form:"PageNumber,required" json:"PageNumber,required" query:"PageNumber,required"`
	PageSize              int32    `form:"PageSize,required" json:"PageSize,required" query:"PageSize,required"`
	ApplyUser             *string  `form:"ApplyUser" json:"ApplyUser,omitempty" query:"ApplyUser"`
	Status                []int32  `form:"Status" json:"Status,omitempty" query:"Status"`
	AccountID             *string  `form:"AccountID" json:"AccountID,omitempty" query:"AccountID"`
	DiagnoseTemplateNames []string `form:"DiagnoseTemplateNames" json:"DiagnoseTemplateNames,omitempty" query:"DiagnoseTemplateNames"`
	TicketID              *string  `form:"TicketID" json:"TicketID,omitempty" query:"TicketID"`
	Action                string   `json:"Action,required" query:"Action,required"`
	Version               string   `json:"Version,required" query:"Version,required"`
}

type ListDiagnoseAuthorizationResponse struct {
	ResponseMetadata *base.ResponseMetadata           `form:"ResponseMetadata,required" json:"ResponseMetadata,required" query:"ResponseMetadata,required"`
	Result           *ListDiagnoseAuthorizationResult `form:"Result,required" json:"Result,required" query:"Result,required"`
}

type ListDiagnoseAuthorizationResult struct {
	Pagination             *common.Pagination       `form:"Pagination,required" json:"Pagination,required" query:"Pagination,required"`
	DiagnoseAuthorizations []*DiagnoseAuthorization `form:"DiagnoseAuthorizations,required" json:"DiagnoseAuthorizations,required" query:"DiagnoseAuthorizations,required"`
}

type TerminateDiagnoseAuthorizationRequest struct {
	AuthorizationID string `form:"AuthorizationID,required" json:"AuthorizationID,required" query:"AuthorizationID,required"`
}

type TerminateDiagnoseAuthorizationResult struct {
	Result bool `form:"Result,required" json:"Result,required" query:"Result,required"`
}
