package discourse

import (
	"fmt"
	"time"

	"code.byted.org/volcengine-support/cloud-sherlock/gen/hertz/diagnose_result"
	"code.byted.org/volcengine-support/cloud-sherlock/gen/hertz/diagnose_task_run"
	"code.byted.org/volcengine-support/cloud-sherlock/gen/hertz/product_category"
	"code.byted.org/volcengine-support/cloud-sherlock/gen/hertz/ticket_diagnose_task"
)

var _ = fmt.Sprintf

type CreateReq struct {
	Token string `form:"Token,required" json:"Token,required" query:"Token,required"`
	Query string `form:"Query,required" json:"Query,required" query:"Query,required"`
	Info  DoInfo `form:"Info" json:"Info" query:"Info"`
}

type DoInfo struct {
	TicketID       string `json:"TicketID"`
	CurrentHandler string `json:"CurrentHandler"`
	SubProductName string `json:"SubProductName"`
	SubProductID   string `json:"SubProductID"`
	AccountID      string `json:"AccountID"`
	Source         string `json:"Source"`
}

type CreateResp struct {
	SessionID string `from:"SessionID" json:"SessionID,required" query:"SessionID"`
}

type HistoryRequest struct {
	Token      string `form:"Token,required" json:"Token,required" query:"Token,required"`
	PageNumber int    `from:"PageNumber" json:"PageNumber,required" query:"PageNumber"`
	PageSize   int    `from:"PageSize" json:"PageSize,required" query:"PageSize"`
}

type HistoryResp struct {
	TotalCount int64                      `from:"TotalCount" json:"TotalCount,required" query:"TotalCount"`
	PageNumber int                        `from:"PageNumber" json:"PageNumber,required" query:"PageNumber"`
	PageSize   int                        `from:"PageSize" json:"PageSize,required" query:"PageSize"`
	Items      []*QueryDiscourseSessionDo `from:"Items" json:"Items,required" query:"Items"`
}

type QueryDiscourseSessionDo struct {
	SessionID string     `from:"SessionID" json:"SessionID,required" query:"SessionID"`
	Title     string     `from:"Title" json:"Title,required" query:"Title"`
	Date      *time.Time `from:"Date" json:"Date,required" query:"Date"`
}

type QueryDiagnoseTaskRunDetailReq struct {
	DiagnoseTaskRunID int64 `form:"DiagnoseTaskRunID,required" json:"DiagnoseTaskRunID,required" query:"DiagnoseTaskRunID,required"`
}

type QueryDiagnoseTaskRunDetailResp struct {
	Data      *[]*SSEMessage `json:"data"`
	Status    string         `json:"status"`
	Progress  int32          `json:"progress"`
	TaskRunID int64          `json:"task_run_id"`
}

type DetailRequest struct {
	SessionID string `form:"SessionID,required" json:"SessionID,required" query:"SessionID,required"`
	Token     string `form:"Token,required" json:"Token,required" query:"Token,required"`
	UserID    string `form:"UserID" json:"UserID" query:"UserID"`
	PageSize  int64  `form:"PageSize" json:"PageSize" query:"PageSize"`
	PageNum   int64  `form:"PageNum" json:"PageNum" query:"PageNum"`
}

type HandleDiscourseRequest struct {
	Query     string `form:"Query" json:"Query" query:"Query"`
	ReQuery   interface{}
	Token     string `form:"Token,required" json:"Token,required" query:"Token,required"`
	SessionID string `form:"SessionID,required" json:"SessionID,required" query:"SessionID,required"`
}

type SSEDetail struct {
	Messages []DetailMessage `form:"Messages,required" json:"Messages,required" query:"Messages,required"`
}

type CreateAgentDiagnoseTaskReq struct {
	ReQuery   interface{} `form:"ReQuery,required" json:"ReQuery,required" query:"ReQuery,required"`
	Token     string      `form:"Token,required" json:"Token,required" query:"Token,required"`
	SessionID string      `form:"SessionID,required" json:"SessionID,required" query:"SessionID,required"`
}

type SessionRedisObj struct {
	ID               int64   `json:"ID"`
	SessionID        string  `json:"SessionID"`
	IntentionSession *string `json:"IntentionSession"`
	DiagnoseSession  *string `json:"DiagnoseSession"`
	SummarySession   *string `json:"SummarySession"`
}

type RunRequest struct {
	Update            bool     `form:"Update" json:"Update,omitempty" query:"Update"`
	Product           string   `form:"Product" json:"Product,omitempty" query:"Product"`
	ID                int64    `form:"ID" json:"ID,omitempty" query:"ID"`
	Function          string   `form:"Function" json:"Function,omitempty" query:"Function"`
	AccountID         string   `form:"AccountID" json:"AccountID,omitempty" query:"AccountID"`
	TicketID          string   `form:"TicketID" json:"TicketID,omitempty" query:"TicketID"`
	Arguments         string   `form:"Arguments" json:"Arguments,omitempty" query:"Arguments"`
	Query             string   `form:"Query" json:"Query,omitempty" query:"Query"`
	Instances         []string `form:"Instances" json:"Instances,omitempty" query:"Instances"`
	Token             string   `form:"Token" json:"Token" query:"Token"`
	SessionID         string   `form:"SessionID" json:"SessionID" query:"SessionID"`
	DiagnoseStartTime int64    `form:"DiagnoseStartTime,omitempty" json:"DiagnoseStartTime" query:"DiagnoseStartTime"`
	DiagnoseEndTime   int64    `form:"DiagnoseEndTime,omitempty" json:"DiagnoseEndTime" query:"DiagnoseEndTime"`
}

type FeedBackRequest struct {
	SessionID string `form:"SessionID,required" json:"SessionID,required" query:"SessionID,required"`
	MessageID string `form:"MessageID,required" json:"MessageID" query:"MessageID,required"`
	Upvote    *bool  `form:"Upvote" json:"Upvote" query:"Upvote"`
}

type CreateDiagnoseTaskReq struct {
	ticket_diagnose_task.CreateDiagnoseTaskReq
	Token     string `form:"Token,required" json:"Token,required" query:"Token,required"`
	SessionID string `form:"SessionID,required" json:"SessionID,required" query:"SessionID,required"`
	ReQuery   string `form:"ReQuery,required" json:"ReQuery" query:"ReQuery,required"`
}

type DiagnoseTaskRunBot struct {
	Id                   int64                                `form:"ID,required" json:"ID,required" query:"ID,required"`
	DiagnoseTaskID       int64                                `form:"DiagnoseTaskID,required" json:"DiagnoseTaskID,required" query:"DiagnoseTaskID,required"`
	Name                 *string                              `form:"Name" json:"Name,omitempty" query:"Name"`
	DiagnoseType         *int32                               `form:"DiagnoseType" json:"DiagnoseType,omitempty" query:"DiagnoseType"`
	Status               *string                              `form:"Status" json:"Status,omitempty" query:"Status"`
	StartTime            *string                              `form:"StartTime" json:"StartTime,omitempty" query:"StartTime"`
	EndTime              *string                              `form:"EndTime" json:"EndTime,omitempty" query:"EndTime"`
	RunUserID            *string                              `form:"RunUserID" json:"RunUserID,omitempty" query:"RunUserID"`
	TicketID             *string                              `form:"TicketID" json:"TicketID,omitempty" query:"TicketID"`
	DiagnoseResultLevel  *diagnose_result.DiagnoseResultLevel `form:"DiagnoseResultLevel" json:"DiagnoseResultLevel,omitempty" query:"DiagnoseResultLevel"`
	Feedback             *bool                                `form:"Feedback" json:"Feedback,omitempty" query:"Feedback"`
	DiagnoseStartTime    *int64                               `form:"DiagnoseStartTime" json:"DiagnoseStartTime,omitempty" query:"DiagnoseStartTime"`
	DiagnoseEndTime      *int64                               `form:"DiagnoseEndTime" json:"DiagnoseEndTime,omitempty" query:"DiagnoseEndTime"`
	DiagnoseTemplateName *string                              `form:"DiagnoseTemplateName" json:"DiagnoseTemplateName,omitempty" query:"DiagnoseTemplateName"`
	Origin               int32                                `form:"Origin,required" json:"Origin,required" query:"Origin,required"`
	ProductCategory      *[]*product_category.ProductCategory `form:"ProductCategory" json:"ProductCategory,omitempty" query:"ProductCategory"`
	Instances            *[]string                            `form:"Instances" json:"Instances,omitempty" query:"Instances"`
}

type QueryDiagnoseTaskRunDetailResultBot struct {
	Information             *DiagnoseTaskRunBot                        `form:"Information,required" json:"Information,required" query:"Information,required"`
	DiagnoseTaskRunByteFlow *diagnose_task_run.DiagnoseTaskRunByteFlow `form:"DiagnoseTaskRunByteFlow,required" json:"DiagnoseTaskRunByteFlow,required" query:"DiagnoseTaskRunByteFlow,required"`
}

type TicketSummaryReq struct {
	TicketID       string    `form:"TicketID,required" json:"TicketID,required" query:"TicketID,required"`
	SubProductID   string    `form:"SubProductID,required" json:"SubProductID,required" query:"SubProductID,required"`
	SubProductName string    `form:"SubProductName" json:"SubProductName" query:"SubProductName"`
	Data           *[]DoData `form:"Data,required" json:"Data,required" query:"Data,required"`
}

type DoData struct {
	Content     string `json:"Content"`
	ChatID      string `json:"ChatID"`
	Channel     string `json:"Channel"`
	CreateTime  int64  `json:"CreateTime"`
	MessageID   string `json:"MessageID"`
	ContentType string `json:"ContentType"`
	Sender      struct {
		SenderType string `json:"SenderType"`
		Email      string `json:"Email"`
	} `json:"Sender"`
}

type TicketReportReq struct {
	ID        string `form:"ID,required" json:"ID,required" query:"ID,required"`
	SessionID string `form:"SessionID,required" json:"SessionID,required" query:"SessionID,required"`
}

type TicketResp struct {
	Status    string       `json:"Status"`
	Choices   *[]DoChoices `json:"Choices"`
	Created   int64        `json:"Created"`
	ID        string       `json:"ID"`
	SessionID string       `json:"SessionID"`
}

type DoChoices struct {
	FinishReason string       `json:"FinishReason"`
	Index        int          `json:"Index"`
	Message      DoSubMessage `json:"Message"`
}

type DoSubMessage struct {
	Content          string      `json:"Content"`
	Role             string      `json:"Role"`
	ReasoningContent interface{} `json:"ReasoningContent"`
}

type TicketRedisObj struct {
	Status      string      `json:"Status"`
	TaskRunID   int64       `json:"TaskRunID"`
	AnswerInfo  *AnswerInfo `json:"AnswerInfo"`
	ReQuery     string      `json:"ReQuery"`
	ProductID   string      `json:"ProductID"`
	ProductName string      `json:"ProductName"`
}

type StageRequest struct {
	MessageID string `form:"MessageID,required" json:"MessageID,required" query:"MessageID,required"`
	SessionID string `form:"SessionID,required" json:"SessionID,required" query:"SessionID,required"`
	Token     string `form:"Token,required" json:"Token,required" query:"Token,required"`
}

type StageResponse struct {
	Stage       string `form:"Stage,required" json:"Stage,required" query:"Stage,required"`
	ProductID   string `form:"ProductID,required" json:"ProductID,required" query:"ProductID,required"`
	ProductName string `form:"ProductName,required" json:"ProductName,required" query:"ProductName,required"`
}

type StageResp struct {
	MessageId    string `json:"message_id"`
	UserId       string `json:"user_id"`
	Query        string `json:"query"`
	Like         int    `json:"like"`
	PID          string `json:"p_id"`
	PName        string `json:"p_name"`
	MessageStage string `json:"message_stage"`
}
