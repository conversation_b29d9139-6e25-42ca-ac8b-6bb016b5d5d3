package discourse

import (
	"code.byted.org/volcengine-support/cloud-sherlock/gen/hertz/diagnose_task_run"
)

type SummaryReq struct {
	Information *DiagnoseTaskRunBot                                  `form:"Information,required" json:"Information,required" query:"Information,required"`
	Items       []*diagnose_task_run.DiagnoseTaskRunItemDetailResult `form:"Items,required" json:"Items,required" query:"Items,required"`
}

type SuggestResp struct {
	Reason      string              `form:"Reason,required" json:"reason,required" query:"Reason,required"`
	Suggest     string              `form:"Suggest,required" json:"suggest,required" query:"Suggest,required"`
	Information *DiagnoseTaskRunBot `form:"Information,required" json:"information,required,omitempty" query:"Information,required"`
}

type DetailMessage struct {
	ConversationID string      `form:"ConversationID,required" json:"ConversationID,required" query:"ConversationID,required"`
	Type           string      `form:"Type,required" json:"Type,required" query:"Type,required"`
	QueryID        string      `form:"Content,required" json:"Content,required" query:"Content,required"`
	Query          string      `form:"Query,required" json:"Query,required" query:"Query,required"`
	AnswerInfo     *AnswerInfo `form:"AnswerInfo,required" json:"AnswerInfo,required" query:"AnswerInfo,required"`
}

type AnswerInfo struct {
	Answer      interface{} `form:"Answer,required" json:"Answer,required" query:"Answer,required"`
	MessageID   string      `form:"MessageID,required" json:"MessageID,required" query:"MessageID,required"`
	CreatedTime int64       `form:"CreatedTime,required" json:"CreatedTime,required" query:"CreatedTime,required"`
	TotalTokens int64       `form:"TotalTokens,required" json:"TotalTokens,required" query:"TotalTokens,required"`
	ID          string      `form:"ID,required" json:"ID,required" query:"ID,required"`
	TaskRunID   int64       `form:"TaskRunID,required" json:"TaskRunID,required" query:"TaskRunID,required"`
	Like        int         `form:"Like,required" json:"Like,required" query:"Like,required"`
	Status      string      `json:"Status"`
	Progress    int32       `json:"Progress"`
}

type SSEMessage struct {
	Event          string      `json:"event"`
	TaskRunID      int64       `json:"task_run_id"`
	ID             string      `json:"id"`
	ConversationID string      `json:"conversation_id"`
	Answer         interface{} `json:"answer,omitempty"`
	CreatedTime    int64       `json:"created_at"`
	Status         string      `json:"status"`
	Progress       int32       `json:"progress"`
}

// FromMsgResp 表单抽取返回
type FromMsgResp struct {
	Product   string      `json:"product"`
	Answer    string      `json:"answer"`
	Function  string      `json:"function"`
	Arguments interface{} `json:"arguments"`
	AccountId string      `json:"account_id"`
	TickId    string      `json:"ticket_id"`
	StopTag   bool        `json:"stop_tag"`
	Query     string      `json:"query"`
	Id        int64       `json:"id"`
	Instances *DoInstances
}

type DoInstances struct {
	Instance  string `json:"InstanceID" mapstructure:"InstanceID"`
	ClusterID string `json:"ClusterID" mapstructure:"ClusterID"`
	Namespace string `json:"Namespace" mapstructure:"Namespace"`
	PodName   string `json:"PodName" mapstructure:"PodName"`
}

type CreateSessionResp struct {
	Conversation *Conversation `json:"Conversation"`
	BaseResp     interface{}   `json:"BaseResp"`
}

type Conversation struct {
	AppConversationID string `json:"AppConversationID"`
	ConversationName  string `json:"ConversationName"`
	CreateTime        string `json:"CreateTime"`
	LastChatTime      string `json:"LastChatTime"`
	EmptyConversation bool   `json:"EmptyConversation"`
}

type MCPConfig struct {
}

type MCPJson struct {
	ProductCn      string      `json:"ProductCn"`
	SubProduct     string      `json:"SubProduct"`
	ResourceTypeCn string      `json:"ResourceTypeCn"`
	Name           string      `json:"name"`
	Description    string      `json:"description"`
	Parameters     interface{} `json:"parameters"`
	ID             int64       `json:"id"`
	ProductID      string      `json:"product_id"`
}

type ThoughtMsg struct {
	Event          string      `json:"event"`
	ID             string      `json:"id"`
	ChainID        string      `json:"chain_id"`
	TaskID         string      `json:"task_id"`
	MessageID      string      `json:"message_id"`
	Position       int         `json:"position"`
	Thought        string      `json:"thought"`
	SkillType      string      `json:"skill_type"`
	Tool           string      `json:"tool"`
	ToolInput      string      `json:"tool_input"`
	ThinkMessageID interface{} `json:"think_message_id"`
	CreatedAt      int64       `json:"created_at"`
	ConversationID string      `json:"conversation_id"`
}
