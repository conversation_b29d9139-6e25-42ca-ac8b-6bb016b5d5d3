// This file is automatically generated. Do not modify.

package feedback

import (
	"fmt"

	"code.byted.org/volcengine-support/cloud-sherlock/gen/hertz/base"
	"code.byted.org/volcengine-support/cloud-sherlock/gen/hertz/common"
)

var _ = fmt.Sprintf

type AddFeedbackLabelReq struct {
	Stage         string              `form:"Stage,required" json:"Stage,required" query:"Stage,required"`
	Key           string              `form:"Key,required" json:"Key,required" query:"Key,required"`
	Name          string              `form:"Name,required" json:"Name,required" query:"Name,required"`
	HandleUserIDs map[string][]string `form:"HandleUserIDs,required" json:"HandleUserIDs,required" query:"HandleUserIDs,required"`
	Action        string              `json:"Action,required" query:"Action,required"`
	Version       *string             `json:"Version,omitempty" query:"Version"`
}

type AddFeedbackLabelResp struct {
	ResponseMetadata *base.ResponseMetadata `form:"ResponseMetadata" json:"ResponseMetadata,omitempty" query:"ResponseMetadata"`
}

type DeleteFeedbackLabelReq struct {
	Id     int64  `form:"ID,required" json:"ID,required" query:"ID,required"`
	Action string `json:"Action,required" query:"Action,required"`
}

type DeleteFeedbackLabelResp struct {
	ResponseMetadata *base.ResponseMetadata `form:"ResponseMetadata" json:"ResponseMetadata,omitempty" query:"ResponseMetadata"`
}

type EndHandleReq struct {
	FeedbackID    int64              `form:"FeedbackID,required" json:"FeedbackID,required" query:"FeedbackID,required"`
	HandleResults []*EndHandleResult `form:"HandleResults" json:"HandleResults,omitempty" query:"HandleResults"`
	HandleUserID  *string            `form:"HandleUserID" json:"HandleUserID,omitempty" query:"HandleUserID"`
	Action        string             `json:"Action,required" query:"Action,required"`
}

type EndHandleResp struct {
	ResponseMetadata *base.ResponseMetadata `form:"ResponseMetadata" json:"ResponseMetadata,omitempty" query:"ResponseMetadata"`
}

type EndHandleResult struct {
	ResultLabel *string `form:"ResultLabel" json:"ResultLabel,omitempty" query:"ResultLabel"`
	Description *string `form:"Description" json:"Description,omitempty" query:"Description"`
}

type Feedback struct {
	FeedbackID         int64              `form:"FeedbackID,required" json:"FeedbackID,required" query:"FeedbackID,required"`
	Stage              string             `form:"Stage,required" json:"Stage,required" query:"Stage,required"`
	Title              *string            `form:"Title" json:"Title,omitempty" query:"Title"`
	SessionID          string             `form:"SessionID,required" json:"SessionID,required" query:"SessionID,required"`
	FeedbackUserID     *string            `form:"FeedbackUserID" json:"FeedbackUserID,omitempty" query:"FeedbackUserID"`
	FeedbackDepartment *string            `form:"FeedbackDepartment" json:"FeedbackDepartment,omitempty" query:"FeedbackDepartment"`
	ReportLink         *string            `form:"ReportLink" json:"ReportLink,omitempty" query:"ReportLink"`
	ProductCategory    *string            `form:"ProductCategory" json:"ProductCategory,omitempty" query:"ProductCategory"`
	FeedbackLabelNames []string           `form:"FeedbackLabelNames" json:"FeedbackLabelNames,omitempty" query:"FeedbackLabelNames"`
	TicketID           *string            `form:"TicketID" json:"TicketID,omitempty" query:"TicketID"`
	HandleUserIDs      []string           `form:"HandleUserIDs" json:"HandleUserIDs,omitempty" query:"HandleUserIDs"`
	Status             string             `form:"Status,required" json:"Status,required" query:"Status,required"`
	FeedbackTime       string             `form:"FeedbackTime,required" json:"FeedbackTime,required" query:"FeedbackTime,required"`
	StartHandleTime    *string            `form:"StartHandleTime" json:"StartHandleTime,omitempty" query:"StartHandleTime"`
	EndHandleTime      *string            `form:"EndHandleTime" json:"EndHandleTime,omitempty" query:"EndHandleTime"`
	StartHandleUserID  *string            `form:"StartHandleUserID" json:"StartHandleUserID,omitempty" query:"StartHandleUserID"`
	EndHandleUserID    *string            `form:"EndHandleUserID" json:"EndHandleUserID,omitempty" query:"EndHandleUserID"`
	HandleResults      []*EndHandleResult `form:"HandleResults" json:"HandleResults,omitempty" query:"HandleResults"`
	IsLike             bool               `form:"IsLike,required" json:"IsLike,required" query:"IsLike,required"`
	Valid              bool               `form:"Valid,required" json:"Valid,required" query:"Valid,required"`
	Description        *string            `form:"Description" json:"Description,omitempty" query:"Description"`
	MessageID          string             `form:"MessageID,required" json:"MessageID,required" query:"MessageID,required"`
}

type FeedbackLabel struct {
	Id            int64               `form:"ID,required" json:"ID,required" query:"ID,required"`
	Stage         string              `form:"Stage,required" json:"Stage,required" query:"Stage,required"`
	Key           string              `form:"Key,required" json:"Key,required" query:"Key,required"`
	Name          string              `form:"Name,required" json:"Name,required" query:"Name,required"`
	HandleUserIDs map[string][]string `form:"HandleUserIDs" json:"HandleUserIDs,omitempty" query:"HandleUserIDs"`
}

type PagePermissionReq struct {
	UserID  *string `form:"UserID" json:"UserID,omitempty" query:"UserID"`
	Action  string  `json:"Action,required" query:"Action,required"`
	Version *string `json:"Version,omitempty" query:"Version"`
}

type PagePermissionResp struct {
	ResponseMetadata *base.ResponseMetadata `form:"ResponseMetadata" json:"ResponseMetadata,omitempty" query:"ResponseMetadata"`
}

type ProblemItem struct {
	ProblemCode string `form:"ProblemCode,required" json:"ProblemCode,required" query:"ProblemCode,required"`
	ProblemName string `form:"ProblemName,required" json:"ProblemName,required" query:"ProblemName,required"`
}

type QueryFeedbackLabelReq struct {
	Stage   *string `form:"Stage" json:"Stage,omitempty" query:"Stage"`
	Id      *int64  `form:"ID" json:"ID,omitempty" query:"ID"`
	Key     *string `form:"Key" json:"Key,omitempty" query:"Key"`
	Name    *string `form:"Name" json:"Name,omitempty" query:"Name"`
	Action  string  `json:"Action,required" query:"Action,required"`
	Version *string `json:"Version,omitempty" query:"Version"`
}

type QueryFeedbackLabelResp struct {
	Result           *QueryFeedbackLabelResult `form:"Result" json:"Result,omitempty" query:"Result"`
	ResponseMetadata *base.ResponseMetadata    `form:"ResponseMetadata" json:"ResponseMetadata,omitempty" query:"ResponseMetadata"`
}

type QueryFeedbackLabelResult struct {
	FeedbackLabelList []*FeedbackLabel `form:"FeedbackLabelList" json:"FeedbackLabelList,omitempty" query:"FeedbackLabelList"`
}

type QueryFeedbackReq struct {
	Status                *string `form:"Status" json:"Status,omitempty" query:"Status"`
	SessionID             *string `form:"SessionID" json:"SessionID,omitempty" query:"SessionID"`
	HandleUserID          *string `form:"HandleUserID" json:"HandleUserID,omitempty" query:"HandleUserID"`
	Title                 *string `form:"Title" json:"Title,omitempty" query:"Title"`
	FeedbackUserID        *string `form:"FeedbackUserID" json:"FeedbackUserID,omitempty" query:"FeedbackUserID"`
	SubProduct            *string `form:"SubProduct" json:"SubProduct,omitempty" query:"SubProduct"`
	TicketID              *string `form:"TicketID" json:"TicketID,omitempty" query:"TicketID"`
	SortByFeedbackTime    *string `form:"SortByFeedbackTime" json:"SortByFeedbackTime,omitempty" query:"SortByFeedbackTime"`
	SortByHandleStartTime *string `form:"SortByHandleStartTime" json:"SortByHandleStartTime,omitempty" query:"SortByHandleStartTime"`
	SortByHandleEndTime   *string `form:"SortByHandleEndTime" json:"SortByHandleEndTime,omitempty" query:"SortByHandleEndTime"`
	PageNumber            *int32  `form:"PageNumber" json:"PageNumber,omitempty" query:"PageNumber"`
	PageSize              *int32  `form:"PageSize" json:"PageSize,omitempty" query:"PageSize"`
	IsLike                *bool   `form:"IsLike" json:"IsLike,omitempty" query:"IsLike"`
	Valid                 *bool   `form:"Valid" json:"Valid,omitempty" query:"Valid"`
	FeedbackID            *int64  `form:"FeedbackID" json:"FeedbackID,omitempty" query:"FeedbackID"`
	Stage                 *string `form:"Stage" json:"Stage,omitempty" query:"Stage"`
	Action                string  `json:"Action,required" query:"Action,required"`
	Version               *string `json:"Version,omitempty" query:"Version"`
}

type QueryFeedbackResp struct {
	Result           *QueryFeedbackResult   `form:"Result" json:"Result,omitempty" query:"Result"`
	ResponseMetadata *base.ResponseMetadata `form:"ResponseMetadata" json:"ResponseMetadata,omitempty" query:"ResponseMetadata"`
}

type QueryFeedbackResult struct {
	FeedbackList []*Feedback        `form:"FeedbackList" json:"FeedbackList,omitempty" query:"FeedbackList"`
	Pagination   *common.Pagination `form:"Pagination" json:"Pagination,omitempty" query:"Pagination"`
}

type QueryResultLabelsReq struct {
	Action  string  `json:"Action,required" query:"Action,required"`
	Version *string `json:"Version,omitempty" query:"Version"`
}

type QueryResultLabelsResp struct {
	Result           *QueryResultLabelsResult `form:"Result" json:"Result,omitempty" query:"Result"`
	ResponseMetadata *base.ResponseMetadata   `form:"ResponseMetadata" json:"ResponseMetadata,omitempty" query:"ResponseMetadata"`
}

type QueryResultLabelsResult struct {
	ResultLabelList []string `form:"ResultLabelList" json:"ResultLabelList,omitempty" query:"ResultLabelList"`
}

type StartHandleReq struct {
	FeedbackID   int64   `form:"FeedbackID,required" json:"FeedbackID,required" query:"FeedbackID,required"`
	HandleUserID *string `form:"HandleUserID" json:"HandleUserID,omitempty" query:"HandleUserID"`
	Action       string  `json:"Action,required" query:"Action,required"`
}

type StartHandleResp struct {
	ResponseMetadata *base.ResponseMetadata `form:"ResponseMetadata" json:"ResponseMetadata,omitempty" query:"ResponseMetadata"`
}

type SubmitDiagnoseTaskFeedbackReq struct {
	DiagnoseTaskID    *int64         `form:"DiagnoseTaskID" json:"DiagnoseTaskID,omitempty" query:"DiagnoseTaskID"`
	DiagnoseTaskRunID int64          `form:"DiagnoseTaskRunID,required" json:"DiagnoseTaskRunID,required" query:"DiagnoseTaskRunID,required"`
	Resolved          bool           `form:"Resolved,required" json:"Resolved,required" query:"Resolved,required"`
	ProblemItems      []*ProblemItem `form:"ProblemItems" json:"ProblemItems,omitempty" query:"ProblemItems"`
	Description       *string        `form:"Description" json:"Description,omitempty" query:"Description"`
	FeedbackUserID    *string        `form:"FeedbackUserID" json:"FeedbackUserID,omitempty" query:"FeedbackUserID"`
	Action            string         `json:"Action,required" query:"Action,required"`
	Version           *string        `json:"Version,omitempty" query:"Version"`
}

type SubmitDiagnoseTaskFeedbackResp struct {
	ResponseMetadata *base.ResponseMetadata `form:"ResponseMetadata" json:"ResponseMetadata,omitempty" query:"ResponseMetadata"`
}

type SubmitFeedbackReq struct {
	SessionID         string  `form:"SessionID,required" json:"SessionID,required" query:"SessionID,required"`
	MessageID         string  `form:"MessageID,required" json:"MessageID,required" query:"MessageID,required"`
	Stage             string  `form:"Stage,required" json:"Stage,required" query:"Stage,required"`
	IsLike            bool    `form:"IsLike,required" json:"IsLike,required" query:"IsLike,required"`
	SubProduct        *string `form:"SubProduct" json:"SubProduct,omitempty" query:"SubProduct"`
	TicketID          *string `form:"TicketID" json:"TicketID,omitempty" query:"TicketID"`
	DiagnoseTaskRunID *int64  `form:"DiagnoseTaskRunID" json:"DiagnoseTaskRunID,omitempty" query:"DiagnoseTaskRunID"`
	ReportLink        *string `form:"ReportLink" json:"ReportLink,omitempty" query:"ReportLink"`
	FeedbackLabelIDs  []int64 `form:"FeedbackLabelIDs" json:"FeedbackLabelIDs,omitempty" query:"FeedbackLabelIDs"`
	Description       *string `form:"Description" json:"Description,omitempty" query:"Description"`
	FeedbackUserID    *string `form:"FeedbackUserID" json:"FeedbackUserID,omitempty" query:"FeedbackUserID"`
	Token             string  `form:"Token,required" json:"Token,required" query:"Token,required"`
	PName             *string `form:"PName" json:"PName,omitempty" query:"PName"`
	Action            string  `json:"Action,required" query:"Action,required"`
	Version           *string `json:"Version,omitempty" query:"Version"`
}

type SubmitFeedbackResp struct {
	Result           *SubmitFeedbackResult  `form:"Result" json:"Result,omitempty" query:"Result"`
	ResponseMetadata *base.ResponseMetadata `form:"ResponseMetadata" json:"ResponseMetadata,omitempty" query:"ResponseMetadata"`
}

type SubmitFeedbackResult struct {
	Id *int64 `form:"ID" json:"ID,omitempty" query:"ID"`
}
