module code.byted.org/volcengine-support/cloud-sherlock

go 1.24.0

toolchain go1.24.2

require (
	code.byted.org/bytedtrace/bytedtrace-common/go v0.0.13
	code.byted.org/bytefaas/faas-go v1.6.22
	code.byted.org/bytefaas/sdk-go v0.0.0-20240317143940-b88bab7d38f7
	code.byted.org/cicd/byteflow v1.6.29
	code.byted.org/eps-platform/biz_gopkg v0.0.0-20250414070815-d91c2920881c
	code.byted.org/eps-platform/volcengine-innersdk-golang v1.0.50-0.20250728090710-64795e05f311
	code.byted.org/eventbus/proto v1.3.51
	code.byted.org/gopkg/env v1.6.16
	code.byted.org/gopkg/lang/v2 v2.1.4
	code.byted.org/gopkg/logs v1.2.23
	code.byted.org/gopkg/logs/v2 v2.1.54
	code.byted.org/gopkg/rand v0.0.0-20200622102840-8cd9b682e5b4
	code.byted.org/gopkg/retry v0.0.0-20230828071853-31868f91208d
	code.byted.org/gopkg/tccclient v1.6.0
	code.byted.org/gorm/bytedgen v0.3.25
	code.byted.org/gorm/bytedgorm v0.9.19
	code.byted.org/iaasng/volcengine-go-ops-sdk v0.0.0-20241120030917-7ca4632ddc80
	code.byted.org/kv/goredis v5.3.12+incompatible
	code.byted.org/kv/redis-v6 v1.0.29
	code.byted.org/middleware/hertz v1.13.8
	code.byted.org/middleware/hertz_ext/v2 v2.1.10
	code.byted.org/overpass/eps_platform_permission_core v0.0.0-20241107161012-38d681dadaf1
	code.byted.org/security/uba_sdk/databus_go v0.0.0-20241126081028-0cd7c791805b
	code.byted.org/videoarch/cloud_gopkg v1.1.226
	code.byted.org/videoarch/golib v0.11.45
	code.byted.org/volcengine-support/gopkg v0.0.2
	code.byted.org/volcengine/volc-signer-golang v1.0.0
	code.byted.org/webcast/libs_anycache v1.6.7
	github.com/bytedance/go-tagexpr/v2 v2.9.2
	github.com/bytedance/gopkg v0.1.2
	github.com/bytedance/mockey v1.2.14
	github.com/bytedance/sonic v1.13.2
	github.com/cloudwego/hertz v0.9.7
	github.com/golang/mock v1.6.0
	github.com/google/uuid v1.6.0
	github.com/hertz-contrib/cors v0.1.0
	github.com/hertz-contrib/sse v0.1.0
	github.com/mitchellh/mapstructure v1.5.0
	github.com/orcaman/concurrent-map v1.0.0
	github.com/orcaman/concurrent-map/v2 v2.0.1
	github.com/pkg/errors v0.9.1
	github.com/samber/lo v1.50.0
	github.com/shopspring/decimal v1.4.0
	github.com/smartystreets/goconvey v1.8.1
	github.com/stretchr/testify v1.10.0
	github.com/thoas/go-funk v0.9.3
	github.com/volcengine/volcengine-go-sdk v1.0.97
	golang.org/x/oauth2 v0.7.0
	golang.org/x/sync v0.12.0
	gopkg.in/yaml.v3 v3.0.1
	gorm.io/gen v0.3.25
	gorm.io/gorm v1.25.12
	gorm.io/plugin/dbresolver v1.5.3
	gorm.io/plugin/soft_delete v1.2.1
	k8s.io/api v0.33.0
)

require (
	code.byted.org/aiops/apm_vendor_byted v0.0.27 // indirect
	code.byted.org/aiops/metrics_codec v0.0.24 // indirect
	code.byted.org/aiops/monitoring-common-go v0.0.5 // indirect
	code.byted.org/bytedtrace-contrib/kitex-go v1.1.52 // indirect
	code.byted.org/bytedtrace/bytedtrace-client-go v1.2.3-pre // indirect
	code.byted.org/bytedtrace/bytedtrace-compatible-lightweight-go v1.0.1 // indirect
	code.byted.org/bytedtrace/bytedtrace-conf-provider-client-go v0.0.26 // indirect
	code.byted.org/bytedtrace/bytedtrace-gls-switch v1.3.0 // indirect
	code.byted.org/bytedtrace/interface-go v1.0.20 // indirect
	code.byted.org/bytedtrace/serializer-go v1.0.0 // indirect
	code.byted.org/bytefaas/golang-x-net v1.0.1 // indirect
	code.byted.org/byteflow/base v1.1.16 // indirect
	code.byted.org/data/databus_client v1.3.7 // indirect
	code.byted.org/gopkg/apm_vendor_interface v0.0.3 // indirect
	code.byted.org/gopkg/asyncache v0.0.0-20210129072708-1df5611dba17 // indirect
	code.byted.org/gopkg/asynccache v0.0.0-20210422090342-26f94f7676b8 // indirect
	code.byted.org/gopkg/bytedmysql v1.1.15 // indirect
	code.byted.org/gopkg/consul v1.2.6 // indirect
	code.byted.org/gopkg/ctxvalues v0.6.0 // indirect
	code.byted.org/gopkg/debug v0.10.1 // indirect
	code.byted.org/gopkg/etcd_util v2.3.3+incompatible // indirect
	code.byted.org/gopkg/etcdproxy v0.1.1 // indirect
	code.byted.org/gopkg/gorm v2.0.1+incompatible // indirect
	code.byted.org/gopkg/lang v0.21.8 // indirect
	code.byted.org/gopkg/localcache v0.9.4 // indirect
	code.byted.org/gopkg/localcache/base v0.8.0 // indirect
	code.byted.org/gopkg/localcache/contributes/freecache v0.7.3 // indirect
	code.byted.org/gopkg/localcache/contributes/gcache v0.8.1 // indirect
	code.byted.org/gopkg/localcache/contributes/vfastcache v0.2.0 // indirect
	code.byted.org/gopkg/logid v0.0.0-20241008043456-230d03adb830 // indirect
	code.byted.org/gopkg/metainfo v0.1.4 // indirect
	code.byted.org/gopkg/metrics v1.4.25 // indirect
	code.byted.org/gopkg/metrics/v3 v3.1.35 // indirect
	code.byted.org/gopkg/metrics/v4 v4.1.4 // indirect
	code.byted.org/gopkg/metrics_core v0.0.39 // indirect
	code.byted.org/gopkg/net2 v1.5.0 // indirect
	code.byted.org/gopkg/pkg v0.0.0-20210817064112-6fe00340bb36 // indirect
	code.byted.org/gopkg/stats v1.2.12 // indirect
	code.byted.org/gopkg/thrift v1.14.0 // indirect
	code.byted.org/hystrix/hystrix-go v0.0.0-20190214095017-a2a890c81cd5 // indirect
	code.byted.org/iespkg/bytedkits-go/goext v0.4.0 // indirect
	code.byted.org/iespkg/retry-go v0.1.2 // indirect
	code.byted.org/inf/authcenter v1.5.2 // indirect
	code.byted.org/inf/infsecc v1.0.3 // indirect
	code.byted.org/kite/kitex v1.19.3 // indirect
	code.byted.org/kite/kitex-overpass-suite v0.0.29 // indirect
	code.byted.org/kite/kitex/pkg/protocol/bthrift v0.0.0-20250417065639-ab2c597a3224 // indirect
	code.byted.org/kite/rpal v0.2.3 // indirect
	code.byted.org/kv/backoff v0.0.0-20191031070508-5d868504e646 // indirect
	code.byted.org/kv/circuitbreaker v0.0.0-20200212034351-d3f51a5b9165 // indirect
	code.byted.org/lang/trace v0.0.3 // indirect
	code.byted.org/lidar/agent v0.2.28 // indirect
	code.byted.org/lidar/profiler v0.4.5 // indirect
	code.byted.org/lidar/profiler/hertz v0.4.7 // indirect
	code.byted.org/lidar/profiler/kitex v0.4.7 // indirect
	code.byted.org/log_market/gosdk v0.0.0-20230524072203-e069d8367314 // indirect
	code.byted.org/log_market/loghelper v0.1.10 // indirect
	code.byted.org/log_market/tracelog v0.1.4 // indirect
	code.byted.org/log_market/ttlogagent_gosdk v0.0.6 // indirect
	code.byted.org/log_market/ttlogagent_gosdk/v4 v4.0.51 // indirect
	code.byted.org/middleware/fic_client v0.2.8 // indirect
	code.byted.org/middleware/gocaller v0.0.6 // indirect
	code.byted.org/overpass/common v0.0.0-20240815141408-18f972b75038 // indirect
	code.byted.org/rocketmq/rocketmq-go-proxy v1.5.9 // indirect
	code.byted.org/rocketmq/rocketmq-go-proxy-mqmesh-interceptor v1.0.18 // indirect
	code.byted.org/security/go-spiffe-v2 v1.0.6 // indirect
	code.byted.org/security/memfd v0.0.1 // indirect
	code.byted.org/security/sensitive_finder_engine v0.3.18 // indirect
	code.byted.org/security/tq_libs v1.0.4 // indirect
	code.byted.org/security/zti-jwt-helper-golang v1.0.16 // indirect
	code.byted.org/service_mesh/http_egress_client v0.0.0-20210511135451-0f6dd8f1e86b // indirect
	code.byted.org/service_mesh/shmipc v0.2.20 // indirect
	code.byted.org/trace/trace-client-go v1.3.7 // indirect
	code.byted.org/ttarch/spd_kitex_section v1.0.1 // indirect
	code.byted.org/videoarch/vfastcache v1.0.10 // indirect
	code.byted.org/webcast/libs_anycache/plugin/cache/base v0.1.1-0.20221212082232-7c36e6844ac9 // indirect
	code.byted.org/webcast/libs_anycache/plugin/cache/objectcache v0.0.1 // indirect
	code.byted.org/webcast/libs_anycache/plugin/codec/base v0.1.0 // indirect
	code.byted.org/webcast/libs_sync v0.1.2 // indirect
	github.com/Knetic/govaluate v3.0.1-0.20171022003610-9aa49832a739+incompatible // indirect
	github.com/alicebob/gopher-json v0.0.0-20230218143504-906a9b012302 // indirect
	github.com/apache/thrift v0.16.0 // indirect
	github.com/beorn7/perks v1.0.1 // indirect
	github.com/bits-and-blooms/bitset v1.13.0 // indirect
	github.com/bits-and-blooms/bloom/v3 v3.6.0 // indirect
	github.com/bluele/gcache v0.0.2 // indirect
	github.com/bytedance/sonic/loader v0.2.4 // indirect
	github.com/caarlos0/env/v6 v6.10.1 // indirect
	github.com/cenkalti/backoff/v4 v4.1.1 // indirect
	github.com/cespare/xxhash v1.1.0 // indirect
	github.com/cespare/xxhash/v2 v2.2.0 // indirect
	github.com/choleraehyq/pid v0.0.20 // indirect
	github.com/cloudevents/sdk-go v1.2.0 // indirect
	github.com/cloudevents/sdk-go/v2 v2.6.0 // indirect
	github.com/cloudwego/base64x v0.1.5 // indirect
	github.com/cloudwego/configmanager v0.2.3 // indirect
	github.com/cloudwego/dynamicgo v0.6.3 // indirect
	github.com/cloudwego/fastpb v0.0.5 // indirect
	github.com/cloudwego/frugal v0.2.5 // indirect
	github.com/cloudwego/gopkg v0.1.4 // indirect
	github.com/cloudwego/iasm v0.2.0 // indirect
	github.com/cloudwego/kitex v0.13.1 // indirect
	github.com/cloudwego/kitex/pkg/protocol/bthrift v0.0.0-20250424071640-01e60a05724d // indirect
	github.com/cloudwego/localsession v0.1.2 // indirect
	github.com/cloudwego/netpoll v0.7.0 // indirect
	github.com/cloudwego/runtimex v0.1.1 // indirect
	github.com/cloudwego/thriftgo v0.4.1 // indirect
	github.com/coocood/freecache v1.2.0 // indirect
	github.com/davecgh/go-spew v1.1.2-0.20180830191138-d8f796af33cc // indirect
	github.com/dgrijalva/jwt-go v3.2.1-0.20180921172315-3af4c746e1c2+incompatible // indirect
	github.com/eapache/go-xerial-snappy v0.0.0-20180814174437-776d5712da21 // indirect
	github.com/facebookgo/clock v0.0.0-20150410010913-600d898af40a // indirect
	github.com/fatih/structtag v1.2.0 // indirect
	github.com/fsnotify/fsnotify v1.5.4 // indirect
	github.com/fxamacker/cbor/v2 v2.7.0 // indirect
	github.com/gabriel-vasile/mimetype v1.4.3 // indirect
	github.com/gin-contrib/sse v0.1.0 // indirect
	github.com/gin-gonic/gin v1.10.0 // indirect
	github.com/go-jose/go-jose/v3 v3.0.0 // indirect
	github.com/go-kit/log v0.2.1 // indirect
	github.com/go-logfmt/logfmt v0.6.0 // indirect
	github.com/go-logr/logr v1.4.2 // indirect
	github.com/go-ole/go-ole v1.2.6 // indirect
	github.com/go-playground/locales v0.14.1 // indirect
	github.com/go-playground/universal-translator v0.18.1 // indirect
	github.com/go-playground/validator/v10 v10.20.0 // indirect
	github.com/go-resty/resty/v2 v2.7.0 // indirect
	github.com/go-sql-driver/mysql v1.7.1 // indirect
	github.com/goccy/go-json v0.10.3 // indirect
	github.com/gogo/protobuf v1.3.2 // indirect
	github.com/golang-jwt/jwt/v4 v4.5.0 // indirect
	github.com/golang/groupcache v0.0.0-20210331224755-41bb18bfe9da // indirect
	github.com/golang/protobuf v1.5.4 // indirect
	github.com/golang/snappy v0.0.4 // indirect
	github.com/google/pprof v0.0.0-20240827171923-fa2c70bbbfe5 // indirect
	github.com/gopherjs/gopherjs v1.17.2 // indirect
	github.com/gorilla/mux v1.8.0 // indirect
	github.com/hashicorp/hcl v1.0.0 // indirect
	github.com/hbollon/go-edlib v1.6.0 // indirect
	github.com/henrylee2cn/ameda v1.4.10 // indirect
	github.com/henrylee2cn/goutil v0.0.0-20210127050712-89660552f6f8 // indirect
	github.com/hertz-contrib/http2 v0.1.1 // indirect
	github.com/hertz-contrib/localsession v0.1.0 // indirect
	github.com/huandu/skiplist v1.2.0 // indirect
	github.com/iancoleman/strcase v0.2.0 // indirect
	github.com/jhump/protoreflect v1.8.2 // indirect
	github.com/jinzhu/inflection v1.0.0 // indirect
	github.com/jinzhu/now v1.1.5 // indirect
	github.com/jmespath/go-jmespath v0.4.0 // indirect
	github.com/json-iterator/go v1.1.12 // indirect
	github.com/jtolds/gls v4.20.0+incompatible // indirect
	github.com/klauspost/compress v1.17.9 // indirect
	github.com/klauspost/cpuid/v2 v2.2.9 // indirect
	github.com/leodido/go-urn v1.4.0 // indirect
	github.com/magiconair/properties v1.8.6 // indirect
	github.com/mattn/go-isatty v0.0.20 // indirect
	github.com/modern-go/concurrent v0.0.0-20180306012644-bacd9c7ef1dd // indirect
	github.com/modern-go/reflect2 v1.0.2 // indirect
	github.com/mohae/deepcopy v0.0.0-20170929034955-c48cc78d4826 // indirect
	github.com/mwitkow/go-proto-validators v0.3.2 // indirect
	github.com/nyaruka/phonenumbers v1.0.56 // indirect
	github.com/oliveagle/jsonpath v0.0.0-20180606110733-2e52cf6e6852 // indirect
	github.com/opentracing/opentracing-go v1.2.1-0.20210205174328-3088eee7e4d2 // indirect
	github.com/pelletier/go-toml v1.9.4 // indirect
	github.com/pelletier/go-toml/v2 v2.2.2 // indirect
	github.com/philhofer/fwd v1.1.1 // indirect
	github.com/pierrec/lz4 v2.6.1+incompatible // indirect
	github.com/pierrec/lz4/v4 v4.1.21 // indirect
	github.com/pmezard/go-difflib v1.0.0 // indirect
	github.com/power-devops/perfstat v0.0.0-20210106213030-5aafc221ea8c // indirect
	github.com/satori/go.uuid v1.2.1-0.20181028125025-b2ce2384e17b // indirect
	github.com/shirou/gopsutil/v3 v3.22.1 // indirect
	github.com/sirupsen/logrus v1.9.3 // indirect
	github.com/smarty/assertions v1.15.0 // indirect
	github.com/spf13/afero v1.9.2 // indirect
	github.com/spf13/cast v1.6.0 // indirect
	github.com/spf13/jwalterweatherman v1.1.0 // indirect
	github.com/spf13/pflag v1.0.5 // indirect
	github.com/spf13/viper v1.10.1 // indirect
	github.com/streadway/amqp v1.0.0 // indirect
	github.com/subosito/gotenv v1.2.0 // indirect
	github.com/tidwall/gjson v1.17.3 // indirect
	github.com/tidwall/match v1.1.1 // indirect
	github.com/tidwall/pretty v1.2.1 // indirect
	github.com/tinylib/msgp v1.1.6 // indirect
	github.com/twitchyliquid64/golang-asm v0.15.1 // indirect
	github.com/ugorji/go/codec v1.2.12 // indirect
	github.com/vmihailenco/msgpack/v4 v4.3.12 // indirect
	github.com/vmihailenco/msgpack/v5 v5.4.1 // indirect
	github.com/vmihailenco/tagparser v0.1.2 // indirect
	github.com/volcengine/volc-sdk-golang v1.0.23 // indirect
	github.com/x448/float16 v0.8.4 // indirect
	github.com/yuin/gopher-lua v1.1.1 // indirect
	github.com/yusufpapurcu/wmi v1.2.2 // indirect
	github.com/zeebo/errs v1.3.0 // indirect
	go.opencensus.io v0.24.0 // indirect
	go.uber.org/atomic v1.9.0 // indirect
	go.uber.org/multierr v1.7.0 // indirect
	go.uber.org/zap v1.18.1 // indirect
	go4.org/unsafe/assume-no-moving-gc v0.0.0-20230525183740-e7c30c78aeb2 // indirect
	golang.org/x/arch v0.14.0 // indirect
	golang.org/x/crypto v0.36.0 // indirect
	golang.org/x/mod v0.20.0 // indirect
	golang.org/x/net v0.38.0 // indirect
	golang.org/x/sys v0.31.0 // indirect
	golang.org/x/text v0.23.0 // indirect
	golang.org/x/time v0.9.0 // indirect
	golang.org/x/tools v0.24.0 // indirect
	google.golang.org/appengine v1.6.7 // indirect
	google.golang.org/genproto v0.0.0-20230706204954-ccb25ca9f130 // indirect
	google.golang.org/genproto/googleapis/rpc v0.0.0-20230720185612-659f7aaaa771 // indirect
	google.golang.org/grpc v1.56.2 // indirect
	google.golang.org/protobuf v1.36.5 // indirect
	gopkg.in/inf.v0 v0.9.1 // indirect
	gopkg.in/ini.v1 v1.66.4 // indirect
	gopkg.in/yaml.v2 v2.4.0 // indirect
	gorm.io/datatypes v1.1.1-0.20230130040222-c43177d3cf8c // indirect
	gorm.io/driver/mysql v1.5.7 // indirect
	gorm.io/hints v1.1.2 // indirect
	k8s.io/apimachinery v0.33.0 // indirect
	k8s.io/klog/v2 v2.130.1 // indirect
	k8s.io/utils v0.0.0-20241104100929-3ea5e8cea738 // indirect
	sigs.k8s.io/json v0.0.0-20241010143419-9aa6b5e7a4b3 // indirect
	sigs.k8s.io/randfill v1.0.0 // indirect
	sigs.k8s.io/structured-merge-diff/v4 v4.6.0 // indirect
	sigs.k8s.io/yaml v1.4.0 // indirect
)

replace github.com/apache/thrift => github.com/apache/thrift v0.13.0

replace github.com/apache/thrift/lib/go/thrift => github.com/apache/thrift v0.13.0
