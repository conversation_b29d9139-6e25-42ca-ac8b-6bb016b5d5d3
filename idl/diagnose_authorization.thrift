include "base.thrift"
include "common.thrift"


struct ApplyDiagnoseAuthorizationRequest {
    2: required i32 StartTime;
    4: required list<i32> ProductCategoryIDs;
    5: required string TicketID;
    6: required string DiagnoseTemplateName;
    7: required map<i32,list<string>> InstanceIDs;
    8: required i32 ApplicationDuration;
    10: required string ApplicationReason;
    254: required string Action             (api.query = "Action",api.const = "ApplyDiagnoseAuthorization")
    255: required string Version            (api.query = "Version",api.const = "")
}

struct ApplyDiagnoseAuthorizationResponse {
    1: required base.ResponseMetadata ResponseMetadata;
    2: required ApplyDiagnoseAuthorizationResult Result;
}

struct ApplyDiagnoseAuthorizationResult {
     1: optional i64 ID ;
}

struct ListDiagnoseAuthorizationRequest {
    1: required i32 PageNumber
    2: required i32 PageSize
    3: optional string ApplyUser
    4: optional list<i32> Status
    5: optional string AccountID
    6: optional list<string> DiagnoseTemplateNames
    7: optional string TicketID
    254: required string Action             (api.query = "Action",api.const = "ListDiagnoseAuthorization")
    255: required string Version            (api.query = "Version",api.const = "")
}

struct ListDiagnoseAuthorizationResponse {
    1: required base.ResponseMetadata ResponseMetadata;
    2: required ListDiagnoseAuthorizationResult Result;
}

struct ListDiagnoseAuthorizationResult {
     1: required common.Pagination Pagination
     2: required list<DiagnoseAuthorization> DiagnoseAuthorizations
}

struct DiagnoseAuthorization {
    1: optional i64 ID,                      // ID
    2: optional list<string> ApplicationPolicy, // 策略名称(申请策略)
    3: optional string CreatedTime,           // 授权提交时间（发起申请的时间）
    4: optional string UpdateTime,            // 更新时间
    5: optional string StartTime,             // 授权开始时间（通过后）
    6: optional string EndTime,               // 授权结束时间
    7: optional i32 ExpiredTime,           // 授权过期时间
    8: optional i32 Status,   // 状态
    9: optional string AccountID,             // 火山账号id
    10: optional string ApplyUser,            // 申请人邮箱
    11: optional string TicketID,             // 工单id
    12: optional string DiagnoseTemplateName, // 诊断场景名称
    13: optional list<i32> ProductCategoryID  // 产品类型id
    14: optional string AuthorizationID  //授权ID
}

struct TerminateDiagnoseAuthorizationRequest {
    1: required string AuthorizationID;
}

struct TerminateDiagnoseAuthorizationResult {
    1: required bool Result;
}

