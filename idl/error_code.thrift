enum ErrorCode {

    CommonError      = -18010001 (api.message = "服务端内部错误")               // @solve: 看内容，看情况问开发
    UnAuthentication = -18010002 (api.message = "用户未鉴权")
    NoAuthentication = -18010003 (api.message = "用户无权限")
    RequestParamInvalid = -18010004 (api.message = "请求参数错误")
    DataNotFound = -18010005 (api.message = "数据不存在")
    DatabaseError = -18010006 (api.message = "数据库报错")
    UserHasNoViewableData = -18010007 (api.message = "用户无可见数据")
    UpStreamSystemError = -18010008 (api.message = "上游系统异常")
    ReLoadingFrontEndError = -18010009 (api.message = "重新加载前端页面")
    TccConfigNotFound = -18010010 (api.message = "TCC配置不存在")
    DataAlreadyExist = -180100011 (api.message = "数据已存在")
    ErrRegisterDiagnoseTemplateSuccessRelateItemFailed  = -180100012(api.message = "注册诊断模版成功，但绑定诊断项失败")
    AuthStateInvalid = -180100013 (api.message = "sso鉴权状态码无效")
    AuthTokenInvalid = -180100014 (api.message = "sso鉴权token无效")
    AuthTokenMissing = -180100015 (api.message = "sso Token 缺失")
    StoreRedisError = -180100016 (api.message = " token 保存到 redis 发生错误")
    ErrorCodeAkSkMissing = -180100017 (api.message = "ak sk 缺失")
    ErrorCodeAkSkInvalid = -180100018 (api.message = "ak sk 无效")
    ErrorCodeAkSkExpired = -180100019 (api.message = "ak sk 已过期")
    ErrorCodeAkSkNotMatch = -180100020 (api.message = "ak sk 不匹配")
    ErrorMonitorTimeDuration = -180100021 (api.message = "监控时间跨度超过该产品类型最大时间")
    ErrorMonitorTimeBeforeNow = -180100022 (api.message = "监控时间超过当前时间指定小时数之前")
    ErrorMonitorEndTimeBeforeStartTime = -180100023 (api.message = "监控结束时间早于开始时间")
    ErrUserNotCollaborator = -180100024 (api.message = "用户不是工单协作人")
    ErrUserNotAuthorized = -180100025 (api.message = "用户授权未通过")
    ErrUserAuthPending = -180100026 (api.message = "用户授权待审批")
    ErrUserAuthReject = -180100027 (api.message = "用户授权已拒绝")
    ErrUserAuthEnd = -180100028 (api.message = "用户授权已结束")
    ErrUserAuthExpire = -180100029 (api.message = "用户授权已过期")
    ErrTicketIDIsEmpty = -180100030 (api.message = "工单ID为空")
    ErrInstanceNotAuthorized = -180100031 (api.message = "实例未授权")
    ErrNoPermission = -180100032 (api.message = "无操作权限")
    ErrNoPagePermission = -180100033 (api.message = "无页面权限")

    UndefinedError = -18010999 (api.message = "未定义错误")

    // 业务报错
    BizError = -18020001 (api.message = "业务错误")
}