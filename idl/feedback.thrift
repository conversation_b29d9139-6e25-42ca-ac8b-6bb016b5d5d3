include "base.thrift"
include "common.thrift"


struct SubmitDiagnoseTaskFeedbackReq {
    1: optional i64 DiagnoseTaskID   // 任务ID
    2: required i64 DiagnoseTaskRunID // 任务运行ID
    3: required bool Resolved           // 是否解决
    4: optional list<ProblemItem> ProblemItems // 问题项
    5: optional string Description         // 问题描述
    6: optional string FeedbackUserID      // 评价人ID
    254: required string Action         (api.query = "Action",api.const = "SubmitDiagnoseTaskFeedback")
    255: optional string Version        (api.query = "Version",api.const = "2024-12-01")
}

struct ProblemItem{
    1: required string ProblemCode
    2: required string ProblemName
}
struct SubmitDiagnoseTaskFeedbackResp {
	255: optional base.ResponseMetadata ResponseMetadata
}

struct SubmitFeedbackReq {
    1: required string SessionID
    2: required string MessageID
    3: required string Stage
    4: required bool IsLike
    5: optional string SubProduct
    6: optional string TicketID
    7: optional i64 DiagnoseTaskRunID
    8: optional string ReportLink
    9: optional list<i64> FeedbackLabelIDs
    10: optional string Description
    11: optional string FeedbackUserID
    12: required string Token
    254: required string Action         (api.query = "Action",api.const = "SubmitFeedback")
    255: optional string Version        (api.query = "Version",api.const = "2024-12-01")
}

struct SubmitFeedbackResp {
    1: optional SubmitFeedbackResult Result,
	255: optional base.ResponseMetadata ResponseMetadata
}

struct SubmitFeedbackResult {
    1: optional i64 ID,
}

struct QueryFeedbackReq {
    1: optional string Status
    2: optional string SessionID
    3: optional string HandleUserID
    4: optional string Title
    5: optional string FeedbackUserID
    6: optional string SubProduct
    7: optional string TicketID
    8: optional string SortByFeedbackTime
    9: optional string SortByHandleStartTime
    10: optional string SortByHandleEndTime
    11: optional i32 PageNumber
    12: optional i32 PageSize
    13: optional bool IsLike
    14: optional bool Valid
    254: required string Action         (api.query = "Action",api.const = "QueryFeedback")
    255: optional string Version        (api.query = "Version",api.const = "2024-12-01")
}

struct QueryFeedbackResp {
    1: optional QueryFeedbackResult Result,
	255: optional base.ResponseMetadata ResponseMetadata
}
struct QueryFeedbackResult {
    1: optional list<Feedback> FeedbackList,
    2: optional common.Pagination Pagination,
}
struct Feedback {
    1: required i64 FeedbackID,
    2: required string Stage,
    3: optional string Title,
    4: required string SessionID,
    5: optional string FeedbackUserID,
    6: optional string FeedbackDepartment,
    7: optional string ReportLink,
    8: optional string ProductCategory,
    9: optional list<string> FeedbackLabelNames,
    10: optional string TicketID,
    11: optional list<string> HandleUserIDs,
    12: required string Status,
    13: required string FeedbackTime,
    14: optional string StartHandleTime,
    15: optional string EndHandleTime,
    16: optional string StartHandleUserID,
    17: optional string EndHandleUserID,
    18: optional list<EndHandleResult> HandleResults,
    19: required bool IsLike,
    20: required bool Valid,
    21: optional string Description,
    22: required string MessageID,
}

struct QueryFeedbackLabelReq {
    1: optional string Stage
    2: optional i64 ID
    3: optional string Key
    4: optional string Name
    254: required string Action         (api.query = "Action",api.const = "QueryFeedbackLabel")
    255: optional string Version        (api.query = "Version",api.const = "2024-12-01")
}
struct QueryFeedbackLabelResp {
    1: optional QueryFeedbackLabelResult Result,
	255: optional base.ResponseMetadata ResponseMetadata
}
struct QueryFeedbackLabelResult {
    1: optional list<FeedbackLabel> FeedbackLabelList,
}
struct FeedbackLabel {
    1: required i64 ID,
    2: required string Stage,
    3: required string Key,
    4: required string Name,
    5: optional map<string,list<string>> HandleUserIDs,
}

struct StartHandleReq {
    1: required i64 FeedbackID
    2: optional string HandleUserID
    254: required string Action         (api.query = "Action",api.const = "StartHandle")
}
struct StartHandleResp {
    255: optional base.ResponseMetadata ResponseMetadata
}

struct EndHandleReq {
    1: required i64 FeedbackID
    2: optional list<EndHandleResult> HandleResults
    4: optional string HandleUserID
    254: required string Action         (api.query = "Action",api.const = "EndHandle")
}

struct EndHandleResult {
    1: optional string ResultLabel,
    2: optional string Description,
}
struct EndHandleResp {
    255: optional base.ResponseMetadata ResponseMetadata
}

struct AddFeedbackLabelReq {
    1: required string Stage
    2: required string Key
    3: required string Name
    4: required map<string,list<string>> HandleUserIDs
    254: required string Action         (api.query = "Action",api.const = "AddFeedbackLabel")
    255: optional string Version        (api.query = "Version",api.const = "2024-12-01")
}

struct AddFeedbackLabelResp {
    255: optional base.ResponseMetadata ResponseMetadata
}

struct DeleteFeedbackLabelReq {
    1: required i64 ID
    254: required string Action         (api.query = "Action",api.const = "DeleteFeedbackLabel")
}
struct DeleteFeedbackLabelResp {
    255: optional base.ResponseMetadata ResponseMetadata
}

struct QueryResultLabelsReq {
    254: required string Action         (api.query = "Action",api.const = "QueryResultLabels")
    255: optional string Version        (api.query = "Version",api.const = "2024-12-01")
}

struct QueryResultLabelsResult {
    1: optional list<string> ResultLabelList,
}

struct QueryResultLabelsResp {
    1: optional QueryResultLabelsResult Result,
    255: optional base.ResponseMetadata ResponseMetadata
}

struct PagePermissionReq{
    1: optional string UserID
    254: required string Action         (api.query = "Action",api.const = "PagePermission")
    255: optional string Version        (api.query = "Version",api.const = "2024-12-01")
}

struct PagePermissionResp {
    255: optional base.ResponseMetadata ResponseMetadata
}

