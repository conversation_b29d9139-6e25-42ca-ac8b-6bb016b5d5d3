include "base.thrift"
include "error_code.thrift"
include "permission.thrift"
include "customer.thrift"
include "common_enum.thrift"
include "feedback.thrift"
include "diagnose_result.thrift"
include "diagnose_item.thrift"
include "diagnose_template.thrift"
include "customer_failure.thrift"
include "describe_ecs.thrift"
include "describe_resource.thrift"
include "diagnose_task_run.thrift"
include "diagnose_task_v2.thrift"
include "ticket_diagnose_task.thrift"
include "product_category.thrift"
include "oauth2.thrift"
include "aksk.thrift"
include "openapi.thrift"
include "fh_openapi.thrift"
include "common_enum.thrift"
include "diagnose_authorization.thrift"

namespace go volcengine.support.cloud_sherlock_worker
service Service {
    // 公共枚举配置接口
    common_enum.GetCommonEnumConfigResp GetCommonEnumConfig(1: common_enum.GetCommonEnumConfigReq req)(api.post="/common",api.category = "公共接口")
    // 获取ImageX配置
    // 获取图片上传token
    common_enum.GetUploadTokenResp GetUploadToken(1: common_enum.GetUploadTokenReq req)(api.post="/common",api.category = "公共接口")
    // 获取产管产品
    // 获取产管产品树
    // 获取销售信息

    // 获取用户角色接口
    permission.ListPermissionRolesResp ListPermissionRoles(1: permission.ListPermissionRolesReq req)(api.post="/permission",api.category = "权限")
    // 获取用户权限接口
    permission.GetUserPermissionResp GetUserPermission(1: permission.GetUserPermissionReq req)(api.post="/permission",api.category = "权限")
    // 校验用户权限接口
    permission.CheckUserPermissionResp CheckUserPermission(1: permission.CheckUserPermissionReq req)(api.post="/permission",api.category = "权限")
    // 获取菜单权限配置接口
    permission.GetMenuPermissionConfigResp GetMenuPermissionConfig(1: permission.GetMenuPermissionConfigReq req)(api.post="/permission",api.category = "权限")


    // API接口同步客户接口
    customer.SyncCustomerResp SyncCustomer(1: customer.SyncCustomerReq req)(api.post="/customer",api.category = "客户")

    // 客户重保列表页接口
    // 客户重保数据统计接口

    // 获取客户信息接口
    customer.GetCustomerInfoResp GetCustomerInfo(1: customer.GetCustomerInfoReq req)(api.post="/customer",api.category = "客户")
    // 获取客户简单信息列表接口
    customer.ListCustomerSimpleInfoResp ListCustomerSimpleInfo(1: customer.ListCustomerSimpleInfoReq req)(api.post="/customer",api.category = "客户")

    // 获取客户服务团队接口
    customer.GetCustomerServiceTeamResp GetCustomerServiceTeam(1: customer.GetCustomerServiceTeamReq req)(api.post="/customer",api.category = "客户")

    // 单客户工单统计接口


    // 提交评价反馈
    feedback.SubmitDiagnoseTaskFeedbackResp SubmitDiagnoseTaskFeedback(1: feedback.SubmitDiagnoseTaskFeedbackReq req)(api.post="/feedback",api.category = "反馈")

    // 诊断结果
    ticket_diagnose_task.DescribeTaskDiagnoseItemResp DescribeTaskDiagnoseItem(1: ticket_diagnose_task.DescribeTaskDiagnoseItemReq req)(api.post="/diagnose_result",api.category = "诊断结果")
    //工单关联的诊断结果列表
    ticket_diagnose_task.ListTicketDiagnoseTaskResp ListTicketDiagnoseTask(1: ticket_diagnose_task.ListTicketDiagnoseTaskReq req)(api.post="/diagnose_result",api.category = "诊断结果")
    //诊断任务列表
    ticket_diagnose_task.ListDiagnoseTaskResp ListDiagnoseTask(1: ticket_diagnose_task.ListDiagnoseTaskReq req)(api.post="/diagnose_result",api.category = "诊断结果")
    //查询诊断任务分类
    ticket_diagnose_task.QueryDiagnoseTaskCategoryResp QueryDiagnoseTaskCategory(1: ticket_diagnose_task.QueryDiagnoseTaskCategoryReq req)(api.post="/diagnose_result",api.category = "诊断结果")
    //查询诊断任务汇总
    ticket_diagnose_task.QueryDiagnoseTaskSummaryResp QueryDiagnoseTaskSummary(1: ticket_diagnose_task.QueryDiagnoseTaskSummaryReq req)(api.post="/diagnose_result",api.category = "诊断结果")
    //创建诊断任务
    ticket_diagnose_task.CreateDiagnoseTaskResp CreateDiagnoseTask(1: ticket_diagnose_task.CreateDiagnoseTaskReq req)(api.post="/diagnose_result",api.category = "诊断结果")
    //查询诊断任务的诊断项信息
    ticket_diagnose_task.QueryDiagnoseTaskItemsResp QueryDiagnoseTaskItem(1: ticket_diagnose_task.QueryDiagnoseTaskItemsReq req)(api.post="/diagnose_result",api.category = "诊断结果")
    //查询诊断任务资源信息
    ticket_diagnose_task.QueryDiagnoseTaskResourcesResp QueryDiagnoseTaskResources(1: ticket_diagnose_task.QueryDiagnoseTaskResourcesReq req)(api.post="/diagnose_result",api.category = "诊断结果")

    //诊断项接口
    diagnose_item.RegisterDiagnoseItemResponse RegisterDiagnoseItme(1: diagnose_item.RegisterDiagnoseItemRequest req)(api.post="/diagnose_item",api.category = "诊断项")
    diagnose_item.ListDiagnoseItemResponse ListDiagnoseItme(1: diagnose_item.ListDiagnoseItemRequest req)(api.post="/diagnose_item",api.category = "诊断项")
    diagnose_item.GetAllDiagnoseItemWithProductResponse GetAllDiagnoseItemWithProduct(1: diagnose_item.GetAllDiagnoseItemWithProductRequest req)(api.post="/diagnose_item",api.category = "诊断项")
    diagnose_item.UpdateDiagnoseItemResponse UpdateDiagnoseItme(1: diagnose_item.UpdateDiagnoseItemRequest req)(api.post="/diagnose_item",api.category = "诊断项")
    diagnose_item.DeleteDiagnoseItemResponse DeleteDiagnoseItme(1: diagnose_item.DeleteDiagnoseItemRequest req)(api.post="/diagnose_item",api.category = "诊断项")

    //诊断模板接口
    diagnose_template.RegisterDiagnoseTemplateResponse RegisterDiagnoseTemplate(1: diagnose_template.RegisterDiagnoseTemplateRequest req)(api.post="/diagnose_template",api.category = "诊断模板")
    diagnose_template.DeleteDiagnoseTemplateResponse DeleteDiagnoseTemplate(1: diagnose_template.DeleteDiagnoseTemplateRequest req)(api.post="/diagnose_template",api.category = "诊断模板")
    diagnose_template.ListDiagnoseTemplateResponse ListDiagnoseTemplate(1: diagnose_template.ListDiagnoseTemplateRequest req)(api.post="/diagnose_template",api.category = "诊断模板")
    diagnose_template.UpdateDiagnoseTemplateResponse UpdateDiagnoseTemplate(1: diagnose_template.UpdateDiagnoseTemplateRequest req)(api.post="/diagnose_template",api.category = "诊断模板")

    //诊断任务接口
    //查询诊断任务列表
    diagnose_task_v2.QueryDiagnoseTaskListResp QueryDiagnoseTaskList(1: diagnose_task_v2.QueryDiagnoseTaskListReq req)(api.post="/diagnose_task_v2",api.category = "诊断任务")
    //删除诊断任务
    diagnose_task_v2.DeleteDiagnoseTaskResp DeleteDiagnoseTask(1: diagnose_task_v2.DeleteDiagnoseTaskReq req)(api.post="/diagnose_task_v2",api.category = "诊断任务")
    //查询诊断任务详情
    diagnose_task_v2.QueryDiagnoseTaskDetailResp QueryDiagnoseTaskDetail(1: diagnose_task_v2.QueryDiagnoseTaskDetailReq req)(api.post="/diagnose_task_v2",api.category = "诊断任务")
    //新增诊断任务
    diagnose_task_v2.CreateDiagnoseTaskV2Resp CreateDiagnoseTaskV2(1: diagnose_task_v2.CreateDiagnoseTaskV2Req req)(api.post="/diagnose_task_v2",api.category = "诊断任务")
    //更新诊断任务
    diagnose_task_v2.UpdateDiagnoseTaskResp UpdateDiagnoseTask(1: diagnose_task_v2.UpdateDiagnoseTaskReq req)(api.post="/diagnose_task_v2",api.category = "诊断任务")

    //诊断任务运行接口
    //运行诊断任务
    diagnose_task_run.RunDiagnoseTaskResp RunDiagnoseTask(1: diagnose_task_run.RunDiagnoseTaskReq req)(api.post="/diagnose_run",api.category = "任务执行")
    //查询任务执行列表
    diagnose_task_run.QueryDiagnoseTaskRunListResp QueryDiagnoseTaskRunList(1: diagnose_task_run.QueryDiagnoseTaskRunListReq req)(api.post="/diagnose_run",api.category = "任务执行")
    //删除任务执行
    diagnose_task_run.DeleteDiagnoseTaskRunResp DeleteDiagnoseTaskRun(1: diagnose_task_run.DeleteDiagnoseTaskRunReq req)(api.post="/diagnose_run",api.category = "任务执行")
    //评价执行结果
//    diagnose_task_run.SubmitDiagnoseTaskRunFeedbackResp SubmitDiagnoseTaskRunFeedback(1: diagnose_task_run.SubmitDiagnoseTaskRunFeedbackReq req)(api.post="/diagnose_run",api.category = "任务执行")
    //查询任务执行详情
    diagnose_task_run.QueryDiagnoseTaskRunDetailResp QueryDiagnoseTaskRunDetail(1: diagnose_task_run.QueryDiagnoseTaskRunDetailReq req)(api.post="/diagnose_run",api.category = "任务执行")
    //查询执行结果中某个诊断项详情
    diagnose_task_run.QueryDiagnoseTaskRunItemDetailResp QueryDiagnoseTaskRunItemDetail(1: diagnose_task_run.QueryDiagnoseTaskRunItemDetailReq req)(api.post="/diagnose_run",api.category = "任务执行")


    //烽火openAPI
    fh_openapi.ListCustomerVolcAccountResponse ListCustomerVolcAccount(1: fh_openapi.ListCustomerVolcAccountRequest req)(api.post="/fh_openapi",api.category = "烽火openAPI")


    //诊断授权接口
    diagnose_authorization.ApplyDiagnoseAuthorizationResponse ApplyDiagnoseAuthorization(1: diagnose_authorization.ApplyDiagnoseAuthorizationRequest req)(api.post="/diagnose_authorization",api.category = "诊断授权")

}