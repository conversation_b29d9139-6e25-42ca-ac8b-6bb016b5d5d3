package cmdb

import (
	"bytes"
	"context"
	"io"
	"net/http"
	"sync"

	"code.byted.org/volcengine-support/cloud-sherlock/pkg/tcc"
	signer "code.byted.org/volcengine/volc-signer-golang"
)

type CMDBClient struct {
	baseURL     string
	client      *http.Client
	AccessKey   string
	SecretKey   string
	ServiceCode string
	Region      string
}

var (
	once    sync.Once
	service *CMDBClient
)

const (
	ServiceName = "reef"
)

func GetCMDBClient() *CMDBClient {
	once.Do(func() {
		config := tcc.GetServiceConfig()
		s := &CMDBClient{
			baseURL:     config.CMDBConfig.Host,
			client:      &http.Client{},
			AccessKey:   config.CMDBConfig.AK,
			SecretKey:   config.CMDBConfig.SK,
			ServiceCode: config.CMDBConfig.ServiceCode,
			Region:      config.CMDBConfig.Region,
		}
		service = s

	})
	return service
}

func (c *CMDBClient) apiRequest(ctx context.Context, request *http.Request) ([]byte, error) {
	request.Header.Set("Content-Type", "application/json")

	req := signer.Sign(request, signer.Credentials{
		AccessKeyID:     c.AccessKey,
		SecretAccessKey: c.SecretKey,
		Region:          c.Region,
		Service:         ServiceName,
	})
	response, err := c.client.Do(req)
	if err != nil {
		return nil, err
	}

	defer response.Body.Close()
	buf := make([]byte, 0)
	buffer := bytes.NewBuffer(buf)
	_, err = io.Copy(buffer, response.Body)
	if err != nil {
		return nil, err
	}
	return buffer.Bytes(), nil
}
