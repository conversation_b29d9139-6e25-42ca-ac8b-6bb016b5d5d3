package cmdb

import (
	"bytes"
	"context"
	"encoding/json"
	"net/http"
	"net/url"
)

const (
	OperatorEq      OperatorType = "="
	OperatorNotEq   OperatorType = "!="
	OperatorIn      OperatorType = "in"
	OperatorNotIn   OperatorType = "not in"
	OperatorLike    OperatorType = "like"
	OperatorNotLike OperatorType = "not like"
)

type OperatorType string

type ListEntitiesRequest struct {
	PageNumber int32
	PageSize   int32
	NextToken  string
	MaxResults int
	Kind       string
	Filter     ListEntitiesFilter
	OrderBy    []OrderByItem
}

type ListEntitiesResponse struct {
	PageNumber int32
	PageSize   int32
	TotalCount int64
	Items      []interface{}
	NextToken  string
}

type FieldFilterItem struct {
	Field    string
	Value    any
	Operator OperatorType
	RefKind  string
}

type FieldFilter []FieldFilterItem

type ListEntitiesFilter struct {
	FieldFilters []FieldFilter
}

type OrderByItem struct {
	Field string
	Desc  bool
}

func (c *CMDBClient) ListEntities(ctx context.Context, req *ListEntitiesRequest) (*ListEntitiesResponse, error) {
	ListEntitiesRequest, err := c.createListEntitiesRequest(ctx, req)
	if err != nil {
		return nil, err
	}
	var resp ListEntitiesResponse
	output, err := c.apiRequest(ctx, ListEntitiesRequest)
	if err != nil {
		return nil, err
	}

	err = json.Unmarshal(output, &resp)
	if err != nil {
		return nil, err
	}
	return &resp, nil

}

func (c *CMDBClient) createListEntitiesRequest(ctx context.Context, request *ListEntitiesRequest) (*http.Request, error) {
	cmdbUrl, err := url.Parse(c.baseURL)
	if err != nil {
		return nil, err
	}
	params := url.Values{}
	params.Add("Action", "ListEntities")
	params.Add("Version", "2025-06-07")
	cmdbUrl.RawQuery = params.Encode()
	payload, err := json.Marshal(request)
	if err != nil {
		return nil, err
	}

	return http.NewRequestWithContext(ctx, http.MethodPost, cmdbUrl.String(), bytes.NewBuffer(payload))
}
