package cmdb

import (
	"context"
	"fmt"
	"regexp"
	"strconv"
	"strings"

	fh_hertz "code.byted.org/volcengine-support/cloud-sherlock/gen/hertz/fh_openapi"
	"code.byted.org/volcengine-support/cloud-sherlock/pkg/fh_openapi"

	"code.byted.org/volcengine-support/cloud-sherlock/biz/errorcode"
	"code.byted.org/volcengine-support/cloud-sherlock/biz/service/product_category"
	"code.byted.org/volcengine-support/cloud-sherlock/gen/hertz/describe_resource"
	"code.byted.org/volcengine-support/cloud-sherlock/utils"
	ehttp "code.byted.org/volcengine-support/gopkg/hertzext"
	"github.com/bytedance/gopkg/util/logger"
	"github.com/cloudwego/hertz/pkg/app"
	"github.com/samber/lo"
)

type CLBInstance struct {
	ApiVersion string `json:"apiVersion"`
	Kind       string `json:"kind"`
	Metadata   struct {
		Name      string `json:"name"`
		NameSpace string `json:"namespace"`
	} `json:"metadata"`
	Spec *CLBInstanceSpec `json:"spec"`
}

type CLBInstanceSpec struct {
	AccountID                    string `json:"accountId"`
	AddressIpVersion             string `json:"addressIpVersion"`
	AllocatedBandwidth           int64  `json:"allocatedBandwidth"`
	AvailableZones               string `json:"availableZones"`
	BusinessStatus               string `json:"businessStatus"`
	BypassSecurityGroupEnabled   int64  `json:"bypassSecurityGroupEnabled"`
	ClusterID                    string `json:"clusterId"`
	CPS                          int64  `json:"cps"`
	CreatedAt                    string `json:"createdAt"`
	CustomLimitType              string `json:"customLimitType"`
	DCEnabled                    int64  `json:"dcEnabled"`
	DCType                       string `json:"dcType"`
	DeletedAt                    string `json:"deletedAt"`
	DeletedTime                  string `json:"deletedTime"`
	Description                  string `json:"description"`
	DispatchUnitType             string `json:"dispatchUnitType"`
	EIPAddress                   string `json:"eipAddress"`
	EIPID                        string `json:"eipId"`
	EIPType                      string `json:"eipType"`
	Enabled                      int64  `json:"enabled"`
	ENIAddress                   string `json:"eniAddress"`
	ENIID                        string `json:"eniId"`
	ENIIpv6Address               string `json:"eniIpv6Address"`
	ExclusiveClusterID           string `json:"exclusiveClusterId"`
	ExpiredTime                  string `json:"expiredTime"`
	Ipv6EIPID                    string `json:"ipv6EipId"`
	Ipv6ENIID                    string `json:"ipv6EniId"`
	Ipv6ServiceID                string `json:"ipv6ServiceId"`
	KBPS                         int64  `json:"kbps"`
	KPPS                         int64  `json:"kpps"`
	LoadBalancerBillingType      int64  `json:"loadBalancerBillingType"`
	LoadBalancerID               string `json:"loadBalancerId"`
	LoadBalancerName             string `json:"loadBalancerName"`
	LoadBalancerSpec             string `json:"loadBalancerSpec"`
	LockReason                   string `json:"lockReason"`
	MasterZoneID                 string `json:"masterZoneId"`
	MaxConnections               int64  `json:"maxConnections"`
	ModificationProtectionReason string `json:"modificationProtectionReason"`
	ModificationProtectionStatus string `json:"modificationProtectionStatus"`
	NewArch                      int64  `json:"newArch"`
	OverdueTime                  string `json:"overdueTime"`
	ProjectName                  string `json:"projectName"`
	ProjectUpdatedAt             int64  `json:"projectUpdatedAt"`
	Region                       string `json:"region"`
	ResetEnabled                 string `json:"resetEnabled"`
	RSMode                       string `json:"rsMode"`
	ServiceID                    string `json:"serviceId"`
	ServiceManaged               string `json:"serviceManaged"`
	ServiceMode                  string `json:"serviceMode"`
	ServiceType                  string `json:"serviceType"`
	SlaveZoneID                  string `json:"slaveZoneId"`
	SpecFactor                   int64  `json:"specFactor"`
	Status                       string `json:"status"`
	SubnetID                     string `json:"subnetId"`
	TimestampRemoveEnabled       int64  `json:"timestampRemoveEnabled"`
	TOAEnable                    int64  `json:"toaEnable"`
	TOAVipVport                  int64  `json:"toaVipVport"`
	Type                         string `json:"type"`
	UpcallKPPS                   int64  `json:"upcallKpps"`
	UpdatedAt                    string `json:"updatedAt"`
	VPCAccountID                 string `json:"vpcAccountId"`
	VPCID                        string `json:"vpcId"`
	WLCOverheadWeightDisabled    int64  `json:"wlcOverheadWeightDisabled"`
	XOACazEnabled                int64  `json:"xoaCazEnabled"`
	XOARemoveEnabled             int64  `json:"xoaRemoveEnabled"`
}

func (clbIns *CLBInstance) BuildListEntitiesRequest(ctx context.Context, instances []string) (*ListEntitiesRequest, error) {
	ins := lo.FilterMap(instances, func(instanceID string, index int) (FieldFilter, bool) {
		return []FieldFilterItem{
			{
				Field: "metadata.name",
				Value: instanceID,
			}, {
				Field:    "spec",
				Operator: OperatorNotEq,
				Value:    nil,
			},
		}, true
	})
	listResourcesRequest := &ListEntitiesRequest{
		Kind: "LoadBalancer",
		Filter: ListEntitiesFilter{
			FieldFilters: ins,
		},
	}
	return listResourcesRequest, nil
}

func (clbIns *CLBInstance) GetResources(ctx context.Context, c *app.RequestContext) error {
	logger := utils.NewModuleLogger("GetClbInstanceResources")

	params := &describe_resource.DescribeResourceRequest{}
	err := utils.JsonUnmarshal(c.Request.Body(), params)
	if err != nil {
		ehttp.DoResponse(ctx, c, errorcode.ErrRequestParamInvalid.WithArgs(err))
		return err
	}
	ins := params.Resources.Instances
	allItems := []interface{}{}
	var nextToken string
	for {
		listEntitiesRequest, err := clbIns.BuildListEntitiesRequest(ctx, ins)
		if err != nil {
			logger.CtxWarn(ctx, "BuildListEntitiesRequest error: %s", err)
			return err
		}
		listEntitiesRequest.MaxResults = 100
		listEntitiesRequest.NextToken = nextToken

		listEntitiesResponse, err := GetCMDBClient().ListEntities(ctx, listEntitiesRequest)
		if err != nil {
			// logger.CtxWarn(ctx, "ListEntities error: %s", err)
			return errorcode.ErrUpStreamSystemError.WithArgs(err)
		}
		allItems = append(allItems, listEntitiesResponse.Items...)

		if listEntitiesResponse.NextToken == "EOF" {
			break
		}
		nextToken = listEntitiesResponse.NextToken
	}

	logger.CtxInfo(ctx, "ListEntitiesResponse : %s\n", utils.JsonToString(allItems))
	if len(allItems) == 0 {
		return errorcode.ErrDataNotFound.WithArgs("clbInstanceInfo is empty")
	}
	clbInstanceInfo := &CLBInstance{}

	err = utils.JsonCopy(allItems[0], clbInstanceInfo)
	if err != nil {
		// logger.CtxWarn(ctx, "JsonCopy error: %s", err)
		// ehttp.ErrorResp(ctx, c, errorcode.ErrCommonInternalError.WithArgs(err))
		return errorcode.ErrCommonInternalError.WithArgs(err)
	}
	if clbInstanceInfo.Spec != nil {
		// logger.CtxWarn(ctx, "ClusterId is empty")
		// 计费类型对应关系
		BillingTypeMap := map[int64]string{
			1: "包年包月",
			2: "按量计费-按规格计费",
			3: "按量计费-按使用量计费",
		}
		// resourceData := &DescribeResourceResponse{}
		resourceMetadata := []*ResourceEntry{
			CreateEntry("AccountId", "账号ID", clbInstanceInfo.Spec.AccountID),
			CreateEntry("Name", "实例名称", clbInstanceInfo.Spec.LoadBalancerName),
			CreateEntry("Status", "状态", clbInstanceInfo.Spec.Status),
			CreateEntry("LoadBalancerID", "ClbID", clbInstanceInfo.Spec.LoadBalancerID),
			CreateEntry("LoadBalancerSpec", "规格", clbInstanceInfo.Spec.LoadBalancerSpec),
			CreateEntry("AddressIpVersion", "版本类型", clbInstanceInfo.Spec.AddressIpVersion),
			CreateEntry("Type", "网络类型", clbInstanceInfo.Spec.Type),
			CreateEntry("CreatedAt", "创建时间", clbInstanceInfo.Spec.CreatedAt),
			CreateEntry("ExpiredTime", "过期时间", clbInstanceInfo.Spec.ExpiredTime),
			CreateEntry("LoadBalancerBillingType", "计费类型", BillingTypeMap[clbInstanceInfo.Spec.LoadBalancerBillingType]),
			CreateEntry("MasterZoneID", "主可用区", clbInstanceInfo.Spec.MasterZoneID),
			CreateEntry("SlaveZoneID", "备可用区", clbInstanceInfo.Spec.SlaveZoneID),
			CreateEntry("Region", "地域", clbInstanceInfo.Spec.Region),
		}
		resourceNetworkInfo := []*ResourceEntry{
			CreateEntry("EipAddress", "公网IP", clbInstanceInfo.Spec.EIPAddress),
			CreateEntry("EIPID", "EipID", clbInstanceInfo.Spec.EIPID),
			CreateEntry("ENIAddress", "私网IP", clbInstanceInfo.Spec.ENIAddress),
			CreateEntry("EniId", "EniID", clbInstanceInfo.Spec.ENIID),
			CreateEntry("SubnetID", "子网", clbInstanceInfo.Spec.SubnetID),
			CreateEntry("VpcId", "VpcID", clbInstanceInfo.Spec.VPCID),
		}

		//根据账号信息获取客户的客户编码，生成烽火平台的跳转链接
		customerInfo := &CustomerInfo{
			AccountId: &clbInstanceInfo.Spec.AccountID,
		}
		resp, err := fh_openapi.ListCustomerVolcAccount(&fh_hertz.ListCustomerVolcAccountRequest{
			VolcAccountID: []string{clbInstanceInfo.Spec.AccountID},
		})
		if err != nil {
			// ehttp.ErrorResp(ctx, c, errorcode.ErrRequestParamInvalid.WithArgs(err))
			return errorcode.ErrUpStreamSystemError.WithArgs(err)
		}
		if len(resp.Result) <= 0 {
			customerInfo.CustomerName = lo.ToPtr(fmt.Sprintf("volcAccountID: %s未找到客户信息", clbInstanceInfo.Spec.AccountID))
		} else {
			if resp.Result[0] != nil {
				customerInfo.CustomerLink = lo.ToPtr(fh_openapi.GetCustomerInfoLink(resp.Result[0].CustomerNumber))
				customerInfo.CustomerName = resp.Result[0].CustomerName
			} else {
				customerInfo.CustomerName = lo.ToPtr(fmt.Sprintf("volcAccountID: %s未找到客户信息", clbInstanceInfo.Spec.AccountID))
			}
		}

		clbInstanceInfoResp := &DescribeResourceResponse{
			ResourceType:        "clbInstanceID",
			ResourceMetadata:    resourceMetadata,
			ResourceNetworkInfo: resourceNetworkInfo,
			CustomerInfo:        customerInfo,
			OriginMsg:           clbInstanceInfo,
			OriginPlatformLink:  fmt.Sprintf("https://vnet.byted.org/lb/%s?region=%s&tab=detail", clbInstanceInfo.Spec.LoadBalancerID, clbInstanceInfo.Spec.Region),
		}
		// 获取Grafana链接
		if url := clbIns.getGrafanaURL(ctx, clbInstanceInfo); url != nil {
			clbInstanceInfoResp.GrafanaLink = url
			clbInstanceInfoResp.ResourceMetadata = append(clbInstanceInfoResp.ResourceMetadata,
				CreateEntry("GrafanaLink", "监控链接", *url))
		}

		ehttp.DoResponse(ctx, c, clbInstanceInfoResp)

	} else {

		// ehttp.ErrorResp(ctx, c, errorcode.ErrDataNotFound)
		return errorcode.ErrDataNotFound.WithArgs("clbInstanceInfo is empty")
	}

	return nil

}

func (clbIns *CLBInstance) GetResourcesDependency(ctx context.Context, c *app.RequestContext) error {
	// logger := utils.NewModuleLogger("GetClbInstanceResources")
	params := &describe_resource.DescribeResourceRequest{}
	err := utils.JsonUnmarshal(c.Request.Body(), params)
	if err != nil {
		ehttp.DoResponse(ctx, c, errorcode.ErrRequestParamInvalid.WithArgs(err))
		return err
	}
	ins := params.Resources.Instances
	clbInstanceInfo, err := GetCLBResource(ctx, ins)
	if err != nil {
		// ehttp.ErrorResp(ctx, c, errorcode.ErrCommonInternalError.WithArgs(err))
		return errorcode.ErrCommonInternalError.WithArgs(err)
	}
	if clbInstanceInfo.Spec != nil {
		result := DescribeResourceDependencyResponse{}
		if clbInstanceInfo.Spec.LoadBalancerID != "" {
			result.InstanceID = clbInstanceInfo.Spec.LoadBalancerID
		}

		//获取CLB的信息返回数据，生成返回信息
		CLB := &Product{}
		CLB.ProductName = "网卡"
		// CLB.Header = []*Field{
		// 	AddField("ResourceID", "资源ID"),
		// 	AddField("ResourceName", "资源名称"),
		// 	AddField("Region", "地域"),
		// 	AddField("AccountID", "账号ID"),
		// 	AddField("EipAddress", "公网IP"),
		// 	AddField("ENIAddress", "私网IP"),
		// 	AddField("EniId", "EniID"),
		// 	AddField("SubnetID", "子网"),
		// 	AddField("VpcId", "VpcId"),
		// }
		// CLB.Items = []map[string]interface{}{
		// 	{
		// 		"ResourceID":   clbInstanceInfo.Spec.LoadBalancerID,
		// 		"ResourceName": clbInstanceInfo.Spec.LoadBalancerName,
		// 		"Region":       clbInstanceInfo.Spec.Region,
		// 		"AccountID":    clbInstanceInfo.Spec.AccountID,
		// 		"EipAddress":   clbInstanceInfo.Spec.EIPAddress,
		// 		"ENIAddress":   clbInstanceInfo.Spec.ENIAddress,
		// 		"EniId":        clbInstanceInfo.Spec.ENIID,
		// 		"SubnetID":     clbInstanceInfo.Spec.SubnetID,
		// 		"VpcId":        clbInstanceInfo.Spec.VPCID,
		// 	},
		// }
		CLB.Header = []*Field{
			AddField("ResourceID", "资源ID"),
			AddField("ENIAddress", "私网IP"),
			AddField("SubnetID", "子网"),
			AddField("VpcId", "VpcId"),
		}
		CLB.Items = []map[string]interface{}{
			{
				"ResourceID": clbInstanceInfo.Spec.ENIID,
				"ENIAddress": clbInstanceInfo.Spec.ENIAddress,
				"SubnetID":   clbInstanceInfo.Spec.SubnetID,
				"VpcId":      clbInstanceInfo.Spec.VPCID,
			},
		}
		result.Products = append(result.Products, CLB)

		eipInstanceInfo, err := GetEIPResourceBind(ctx, ins)
		if err != nil {
			// ehttp.ErrorResp(ctx, c, errorcode.ErrCommonInternalError.WithArgs(err))
			return errorcode.ErrCommonInternalError.WithArgs(err)
		}
		if eipInstanceInfo != nil {
			if eipInstanceInfo.Spec != nil {
				//获取EIP的信息返回数据，生成返回信息
				EIP := &Product{}
				EIP.ProductName = "公网IP"
				EIP.Header = []*Field{
					AddField("ResourceID", "资源ID"),
					AddField("EipAddress", "公网IP"),
					AddField("Bandwidth", "带宽"),
					AddField("BandwidthPackageId", "绑定带宽包ID"),
				}
				EIP.Items = []map[string]interface{}{
					{
						"ResourceID":         eipInstanceInfo.Spec.EipId,
						"EipAddress":         eipInstanceInfo.Spec.EipAddress,
						"Bandwidth":          strconv.FormatInt(eipInstanceInfo.Spec.Bandwidth, 10) + " Mbps",
						"BandwidthPackageId": eipInstanceInfo.Spec.BandwidthPackageId,
					},
				}
				result.Products = append(result.Products, EIP)
			}
		}
		ehttp.DoResponse(ctx, c, result)

	} else {
		// ehttp.ErrorResp(ctx, c, errorcode.ErrDataNotFound)
		return errorcode.ErrDataNotFound.WithArgs("clbInstanceInfo is empty")
	}
	return nil
}

func (clbIns *CLBInstance) getGrafanaURL(ctx context.Context, instance *CLBInstance) *string {
	productCategory, err := product_category.GetProductCategoryService().GetProductCategoryByResourceType(ctx, clbIns.GetResourceType())

	if err != nil || productCategory == nil || productCategory.GrafanaTemplate == nil {
		logger.CtxWarnf(ctx, "get grafana url failed: %v", err)
		return nil
	}

	data := map[string]string{
		"Region":        instance.Spec.Region,
		"ClbInstanceID": instance.Spec.LoadBalancerID,
	}
	url, err := utils.RenderGrafanaTemplate(*productCategory.GrafanaTemplate, data)
	if err != nil {
		logger.CtxWarnf(ctx, "render grafana template failed: %v", err)
		return nil
	}
	return &url
}

func (vc *CLBInstance) GetResourceType() string {
	return "clbInstanceID"
}
func (vn *CLBInstance) Match(ctx context.Context, inputInfo *describe_resource.DescribeResourceRequest) bool {
	// 优先检查明确指定的resourceType
	if strings.TrimSpace(inputInfo.Resources.ResourceType) != "" {
		return strings.TrimSpace(inputInfo.Resources.ResourceType) == vn.GetResourceType()
	}

	// 当resourceType为空时，进行实例ID正则匹配
	regex := regexp.MustCompile(`^clb-[a-z0-9]{15,29}$`) // 严格匹配：clb开头 + 20位小写字母/数字，总长度21
	for _, resource := range inputInfo.Resources.Instances {
		if regex.MatchString(resource) {
			return true
		}
	}

	return false
}
