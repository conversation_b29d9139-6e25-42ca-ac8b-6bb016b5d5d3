package cmdb

import (
	"context"
	"fmt"
	"regexp"
	"strconv"
	"strings"

	fh_hertz "code.byted.org/volcengine-support/cloud-sherlock/gen/hertz/fh_openapi"
	"code.byted.org/volcengine-support/cloud-sherlock/pkg/fh_openapi"

	"code.byted.org/volcengine-support/cloud-sherlock/biz/errorcode"
	"code.byted.org/volcengine-support/cloud-sherlock/biz/service/product_category"
	"code.byted.org/volcengine-support/cloud-sherlock/gen/hertz/describe_resource"
	"code.byted.org/volcengine-support/cloud-sherlock/utils"
	ehttp "code.byted.org/volcengine-support/gopkg/hertzext"
	"github.com/bytedance/gopkg/util/logger"
	"github.com/cloudwego/hertz/pkg/app"
	"github.com/samber/lo"
)

type EipInstance struct {
	ApiVersion string `json:"apiVersion"`
	Kind       string `json:"kind"`
	Metadata   struct {
		Name      string `json:"name"`
		NameSpace string `json:"namespace"`
	} `json:"metadata"`
	Spec *EipInstanceSpec `json:"spec"`
}

type EipInstanceSpec struct {
	AccountID               string `json:"accountId"`
	Analyzable              int64  `json:"analyzable"`
	Bandwidth               int64  `json:"bandwidth"`
	BandwidthPackageId      string `json:"bandwidthPackageId"`
	BillingLock             string `json:"billingLock"`
	BillingType             int64  `json:"billingType"`
	BusinessStatus          string `json:"businessStatus"`
	CreatedAt               string `json:"createdAt"`
	DeletedAt               string `json:"deletedAt"`
	DeletedTime             string `json:"deletedTime"`
	Description             string `json:"description"`
	DirectMode              int64  `json:"directMode"`
	EgressAlgorithm         string `json:"egressAlgorithm"`
	EipAddress              string `json:"eipAddress"`
	EipId                   string `json:"eipId"`
	EniId                   string `json:"eniId"`
	ExpiredTime             string `json:"expiredTime"`
	GaIpAddress             string `json:"gaIpAddress"`
	IgwVtepIp               string `json:"igwVtepIp"`
	IgwuGrayNo              int64  `json:"igwuGrayNo"`
	IndependentBandwidth    int64  `json:"independentBandwidth"`
	IngressAlgorithm        string `json:"ingressAlgorithm"`
	InstanceId              string `json:"instanceId"`
	InstanceType            string `json:"instanceType"`
	IpPoolCidrBlockId       string `json:"ipPoolCidrBlockId"`
	IpPoolId                string `json:"ipPoolId"`
	IsBlocked               int64  `json:"isBlocked"`
	IsGaResource            int64  `json:"isGaResource"`
	IsInternal              int64  `json:"isInternal"`
	IsIpv6                  int64  `json:"isIpv6"`
	IsXgwvGray              int64  `json:"isXgwvGray"`
	Isp                     string `json:"isp"`
	LockReason              string `json:"lockReason"`
	Mode                    string `json:"mode"`
	Name                    string `json:"name"`
	Observable              int64  `json:"observable"`
	OpLock                  string `json:"opLock"`
	OverdueTime             string `json:"overdueTime"`
	PrivateIpAddress        string `json:"privateIpAddress"`
	ProjectName             string `json:"projectName"`
	ProjectTime             int64  `json:"projectTime"`
	RateLimitId             int64  `json:"rateLimitId"`
	Region                  string `json:"region"`
	ReleaseWithInstance     int64  `json:"releaseWithInstance"`
	ReservationActiveTime   string `json:"reservationActiveTime"`
	ReservationBandwidth    int64  `json:"reservationBandwidth"`
	SecurityProtectionTypes string `json:"securityProtectionTypes"`
	ServiceCreated          int64  `json:"serviceCreated"`
	ServiceManaged          int64  `json:"serviceManaged"`
	ServiceName             string `json:"serviceName"`
	SpecificEgress          string `json:"specificEgress"`
	SpecifiedRxBandwidth    int64  `json:"specifiedRxBandwidth"`
	Status                  string `json:"status"`
	UpdatedAt               string `json:"updatedAt"`
	VpcId                   string `json:"vpcId"`
	XgwvMetaId              string `json:"xgwvMetaId"`
	Zone                    string `json:"zone"`
}

func (eipIns *EipInstance) BuildListEntitiesRequest(ctx context.Context, instances []string) (*ListEntitiesRequest, error) {
	regex := regexp.MustCompile(`^eip-[a-z0-9]{15,29}$`)
	ins := lo.FilterMap(instances, func(instanceID string, index int) (FieldFilter, bool) {
		if regex.MatchString(instanceID) {
			return []FieldFilterItem{
				{
					Field: "metadata.name",
					Value: instanceID,
				}, {
					Field:    "spec",
					Operator: OperatorNotEq,
					Value:    nil,
				},
			}, true
		} else {
			return []FieldFilterItem{
				{
					Field: "spec.eipAddress",
					Value: instanceID,
				}, {
					Field:    "spec",
					Operator: OperatorNotEq,
					Value:    nil,
				},
			}, true
		}
	})
	listResourcesRequest := &ListEntitiesRequest{
		Kind: "Eip",
		Filter: ListEntitiesFilter{
			FieldFilters: ins,
		},
	}
	return listResourcesRequest, nil
}

func (eipIns *EipInstance) BuildListEntitiesRequestBind(ctx context.Context, instances []string) (*ListEntitiesRequest, error) {
	ins := lo.FilterMap(instances, func(instanceID string, index int) (FieldFilter, bool) {
		return []FieldFilterItem{
			{
				Field: "spec.instanceId",
				Value: instanceID,
			}, {
				Field:    "spec",
				Operator: OperatorNotEq,
				Value:    nil,
			},
		}, true
	})
	listResourcesRequest := &ListEntitiesRequest{
		Kind: "Eip",
		Filter: ListEntitiesFilter{
			FieldFilters: ins,
		},
	}
	return listResourcesRequest, nil
}

func (eipIns *EipInstance) GetResources(ctx context.Context, c *app.RequestContext) error {
	logger := utils.NewModuleLogger("GetEipInstanceResources")
	params := &describe_resource.DescribeResourceRequest{}
	err := utils.JsonUnmarshal(c.Request.Body(), params)
	if err != nil {
		ehttp.DoResponse(ctx, c, errorcode.ErrRequestParamInvalid.WithArgs(err))
		return err
	}
	ins := params.Resources.Instances
	allItems := []interface{}{}
	var nextToken string
	for {
		listEntitiesRequest, err := eipIns.BuildListEntitiesRequest(ctx, ins)
		if err != nil {
			// logger.CtxWarn(ctx, "BuildListEntitiesRequest error: %s", err)
			return err
		}
		listEntitiesRequest.MaxResults = 100
		listEntitiesRequest.NextToken = nextToken

		listEntitiesResponse, err := GetCMDBClient().ListEntities(ctx, listEntitiesRequest)
		if err != nil {
			// logger.CtxWarn(ctx, "ListEntities error: %s", err)
			return err
		}
		allItems = append(allItems, listEntitiesResponse.Items...)

		if listEntitiesResponse.NextToken == "EOF" {
			break
		}
		nextToken = listEntitiesResponse.NextToken
	}
	logger.CtxInfo(ctx, "ListEIPEntitiesResponse : %s\n", utils.JsonToString(allItems))
	if len(allItems) == 0 {
		// logger.CtxWarn(ctx, "ListEntities response is empty")
		// ehttp.ErrorResp(ctx, c, errorcode.ErrDataNotFound)
		return errorcode.ErrDataNotFound.WithArgs("eipIns response is empty") // --- IGNORE ---
	}
	eipInstanceInfo := &EipInstance{}

	err = utils.JsonCopy(allItems[0], eipInstanceInfo)
	if err != nil {
		// logger.CtxWarn(ctx, "JsonCopy error: %s", err)
		// ehttp.ErrorResp(ctx, c, errorcode.ErrCommonInternalError.WithArgs(err))
		return errorcode.ErrCommonInternalError.WithArgs(err)
	}
	if eipInstanceInfo.Spec != nil {
		// logger.CtxWarn(ctx, "ClusterId is empty")
		// 计费类型对应关系
		BillingTypeMap := map[int64]string{
			1: "包年包月",
			2: "按量计费-按带宽上限计费",
			3: "按量计费-按实际流量计费",
		}
		// 封禁对应关系
		IsBlockedMap := map[int64]string{
			0: "未封禁",
			1: "封禁",
		}
		// resourceData := &DescribeResourceResponse{}
		resourceMetadata := []*ResourceEntry{
			CreateEntry("AccountId", "账号名称", eipInstanceInfo.Spec.AccountID),
			CreateEntry("Name", "实例名称", eipInstanceInfo.Spec.Name),
			CreateEntry("Status", "状态", eipInstanceInfo.Spec.Status),
			CreateEntry("IsBlocked", "是否封禁", IsBlockedMap[eipInstanceInfo.Spec.IsBlocked]),
			CreateEntry("BusinessStatus", "是否锁定", eipInstanceInfo.Spec.BusinessStatus),
			CreateEntry("EipId", "EipID", eipInstanceInfo.Spec.EipId),
			CreateEntry("Isp", "线路类型", eipInstanceInfo.Spec.Isp),
			CreateEntry("CreatedAt", "创建时间", eipInstanceInfo.Spec.CreatedAt),
			CreateEntry("ExpiredTime", "过期时间", eipInstanceInfo.Spec.ExpiredTime),
			CreateEntry("Bandwidth", "带宽", strconv.FormatInt(eipInstanceInfo.Spec.Bandwidth, 10)+" Mbps"),
			CreateEntry("BandwidthPackageId", "绑定带宽包ID", eipInstanceInfo.Spec.BandwidthPackageId),
			CreateEntry("BillingType", "计费类型", BillingTypeMap[eipInstanceInfo.Spec.BillingType]),
			CreateEntry("Region", "地域", eipInstanceInfo.Spec.Region),
		}
		resourceNetworkInfo := []*ResourceEntry{
			CreateEntry("EipAddress", "公网IP", eipInstanceInfo.Spec.EipAddress),
			CreateEntry("PrivateIpAddress", "私网IP", eipInstanceInfo.Spec.PrivateIpAddress),
			CreateEntry("EniId", "EniID", eipInstanceInfo.Spec.EniId),
			CreateEntry("InstanceId", "绑定资源ID", eipInstanceInfo.Spec.InstanceId),
			CreateEntry("InstanceType", "绑定资源类型", eipInstanceInfo.Spec.InstanceType),
			CreateEntry("VpcId", "VpcID", eipInstanceInfo.Spec.VpcId),
		}

		//根据账号信息获取客户的客户编码，生成烽火平台的跳转链接
		customerInfo := &CustomerInfo{
			AccountId: &eipInstanceInfo.Spec.AccountID,
		}
		resp, err := fh_openapi.ListCustomerVolcAccount(&fh_hertz.ListCustomerVolcAccountRequest{
			VolcAccountID: []string{eipInstanceInfo.Spec.AccountID},
		})
		if err != nil {
			// ehttp.ErrorResp(ctx, c, errorcode.ErrRequestParamInvalid.WithArgs(err))
			return errorcode.ErrUpStreamSystemError.WithArgs(err)
		}
		if len(resp.Result) <= 0 {
			customerInfo.CustomerName = lo.ToPtr(fmt.Sprintf("volcAccountID: %s未找到客户信息", eipInstanceInfo.Spec.AccountID))
		} else {
			if resp.Result[0] != nil {
				customerInfo.CustomerLink = lo.ToPtr(fh_openapi.GetCustomerInfoLink(resp.Result[0].CustomerNumber))
				customerInfo.CustomerName = resp.Result[0].CustomerName
			} else {
				customerInfo.CustomerName = lo.ToPtr(fmt.Sprintf("volcAccountID: %s未找到客户信息", eipInstanceInfo.Spec.AccountID))
			}
		}

		eipInstanceInfoResp := &DescribeResourceResponse{
			ResourceType:        "eipID",
			ResourceMetadata:    resourceMetadata,
			ResourceNetworkInfo: resourceNetworkInfo,
			CustomerInfo:        customerInfo,
			OriginMsg:           eipInstanceInfo,
			OriginPlatformLink:  fmt.Sprintf("https://vnet.byted.org/eip/%s?region=%s&tab=detail", eipInstanceInfo.Spec.EipId, eipInstanceInfo.Spec.Region),
		}
		// 获取Grafana链接
		if url := eipIns.getGrafanaURL(ctx, eipInstanceInfo); url != nil {
			eipInstanceInfoResp.GrafanaLink = url
			eipInstanceInfoResp.ResourceMetadata = append(eipInstanceInfoResp.ResourceMetadata,
				CreateEntry("GrafanaLink", "监控链接", *url))
		}

		ehttp.DoResponse(ctx, c, eipInstanceInfoResp)

	} else {

		// ehttp.ErrorResp(ctx, c, errorcode.ErrDataNotFound)
		return errorcode.ErrDataNotFound
	}

	return nil

}

func (eipIns *EipInstance) GetResourcesDependency(ctx context.Context, c *app.RequestContext) error {
	// logger := utils.NewModuleLogger("GetEipInstanceResources")
	params := &describe_resource.DescribeResourceRequest{}
	err := utils.JsonUnmarshal(c.Request.Body(), params)
	if err != nil {
		ehttp.DoResponse(ctx, c, errorcode.ErrRequestParamInvalid.WithArgs(err))
		return err
	}
	ins := params.Resources.Instances
	EIPInstanceInfo, err := GetEIPResource(ctx, ins)
	if err != nil {
		// ehttp.ErrorResp(ctx, c, errorcode.ErrCommonInternalError.WithArgs(err))
		return errorcode.ErrCommonInternalError.WithArgs(err)
	}
	if EIPInstanceInfo.Spec != nil {
		result := DescribeResourceDependencyResponse{}
		if EIPInstanceInfo.Spec.EipId != "" {
			result.InstanceID = EIPInstanceInfo.Spec.EipId
		}

		//获取EIP的信息返回数据，生成返回信息
		EIP := &Product{}
		EIP.ProductName = "绑定资源信息"
		// EIP.Header = []*Field{
		// 	AddField("ResourceID", "资源ID"),
		// 	AddField("ResourceName", "资源名称"),
		// 	AddField("Region", "地域"),
		// 	AddField("AccountID", "账号ID"),
		// 	AddField("EipAddress", "公网IP"),
		// 	AddField("PrivateIpAddress", "私网IP"),
		// 	AddField("EniId", "EniID"),
		// 	AddField("InstanceId", "绑定资源ID"),
		// 	AddField("VpcId", "VpcId"),
		// }
		// EIP.Items = []map[string]interface{}{
		// 	{
		// 		"ResourceID":       EIPInstanceInfo.Spec.EipId,
		// 		"ResourceName":     EIPInstanceInfo.Spec.Name,
		// 		"Region":           EIPInstanceInfo.Spec.Region,
		// 		"AccountID":        EIPInstanceInfo.Spec.AccountID,
		// 		"EipAddress":       EIPInstanceInfo.Spec.EipAddress,
		// 		"PrivateIpAddress": EIPInstanceInfo.Spec.PrivateIpAddress,
		// 		"EniId":            EIPInstanceInfo.Spec.EniId,
		// 		"InstanceId":       EIPInstanceInfo.Spec.InstanceId,
		// 		"VpcId":            EIPInstanceInfo.Spec.VpcId,
		// 	},
		// }
		EIP.Header = []*Field{
			AddField("ResourceID", "资源ID"),
			AddField("ResourceName", "资源类型"),
			AddField("EniId", "EniID"),
			AddField("PrivateIpAddress", "私网IP"),
			AddField("EipAddress", "公网IP"),
			AddField("VpcId", "VpcId"),
		}
		EIP.Items = []map[string]interface{}{
			{
				"ResourceID":       EIPInstanceInfo.Spec.InstanceId,
				"ResourceName":     EIPInstanceInfo.Spec.InstanceType,
				"EniId":            EIPInstanceInfo.Spec.EniId,
				"PrivateIpAddress": EIPInstanceInfo.Spec.PrivateIpAddress,
				"EipAddress":       EIPInstanceInfo.Spec.EipAddress,
				"VpcId":            EIPInstanceInfo.Spec.VpcId,
			},
		}

		result.Products = append(result.Products, EIP)

		ehttp.DoResponse(ctx, c, result)
	} else {

		// ehttp.ErrorResp(ctx, c, errorcode.ErrDataNotFound)
		return errorcode.ErrDataNotFound
	}

	return nil
}

func (eipIns *EipInstance) getGrafanaURL(ctx context.Context, instance *EipInstance) *string {
	productCategory, err := product_category.GetProductCategoryService().GetProductCategoryByResourceType(ctx, "eipID")
	if err != nil || productCategory == nil || productCategory.GrafanaTemplate == nil {
		logger.CtxWarnf(ctx, "get grafana url failed: %v", err)
		return nil
	}

	data := map[string]string{
		"Region": instance.Spec.Region,
		"Eip":    instance.Spec.EipAddress,
	}
	url, err := utils.RenderGrafanaTemplate(*productCategory.GrafanaTemplate, data)
	if err != nil {
		logger.CtxWarnf(ctx, "render grafana template failed: %v", err)
		return nil
	}
	return &url
}

func (eipIns *EipInstance) GetResourceType() string {
	return "eipID"
}
func (eipIns *EipInstance) Match(ctx context.Context, inputInfo *describe_resource.DescribeResourceRequest) bool {
	// 优先检查明确指定的resourceType
	if strings.TrimSpace(inputInfo.Resources.ResourceType) != "" {
		return strings.TrimSpace(inputInfo.Resources.ResourceType) == eipIns.GetResourceType()
	}

	// 当resourceType为空时，进行实例ID正则匹配
	regex := regexp.MustCompile(`^eip-[a-z0-9]{15,29}$`)                                                                   // 严格匹配：eip开头 + 20位小写字母/数字，总长度21
	IPRegex := regexp.MustCompile(`^((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$`) //匹配IP地址
	privateIPBlocks := []*regexp.Regexp{
		regexp.MustCompile(`^0\.\d+\.\d+\.\d+$`),                   // 0.0.0.0/8 (本网络)
		regexp.MustCompile(`^10\.\d+\.\d+\.\d+$`),                  // 10.0.0.0/8 (私有网络)
		regexp.MustCompile(`^127\.\d+\.\d+\.\d+$`),                 // *********/8 (环回地址)
		regexp.MustCompile(`^169\.254\.\d+\.\d+$`),                 // ***********/16 (链路本地)
		regexp.MustCompile(`^172\.(1[6-9]|2\d|3[0-1])\.\d+\.\d+$`), // **********/12 (私有网络)
		regexp.MustCompile(`^192\.0\.0\.\d+$`),                     // *********/24 (IETF 协议分配)
		regexp.MustCompile(`^192\.0\.2\.\d+$`),                     // *********/24 (文档和示例)
		regexp.MustCompile(`^192\.88\.99\.\d+$`),                   // ***********/24 (6to4 中继)
		regexp.MustCompile(`^192\.168\.\d+\.\d+$`),                 // ***********/16 (私有网络)
		regexp.MustCompile(`^198\.18\.\d+\.\d+$`),                  // **********/15 (网络设备测试)
		regexp.MustCompile(`^198\.51\.100\.\d+$`),                  // 198.51.100.0/24 (文档和示例)
		regexp.MustCompile(`^203\.0\.113\.\d+$`),                   // 203.0.113.0/24 (文档和示例)
		regexp.MustCompile(`^224\.\d+\.\d+\.\d+$`),                 // *********/4 (组播)
		regexp.MustCompile(`^240\.\d+\.\d+\.\d+$`),                 // 240.0.0.0/4 (保留)
		regexp.MustCompile(`^255\.255\.255\.255$`),                 // *************** (广播)
		regexp.MustCompile(`^100\.(6[4-9]|7[0-9]|8[0-9]|9[0-9]|1[01][0-9]|12[0-7])\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$`), // **********/10
	}
	for _, resource := range inputInfo.Resources.Instances {
		if regex.MatchString(resource) {
			return true
		} else if IPRegex.MatchString(resource) {
			for _, block := range privateIPBlocks {
				if block.MatchString(resource) {
					return false
				}
			}
			return true
		}
	}

	return false
}
