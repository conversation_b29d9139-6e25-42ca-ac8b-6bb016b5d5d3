package cmdb

import (
	"context"
	"errors"
	"fmt"
	"regexp"
	"strings"

	fh_hertz "code.byted.org/volcengine-support/cloud-sherlock/gen/hertz/fh_openapi"
	"code.byted.org/volcengine-support/cloud-sherlock/pkg/fh_openapi"

	"code.byted.org/volcengine-support/cloud-sherlock/biz/errorcode"
	"code.byted.org/volcengine-support/cloud-sherlock/biz/service/product_category"
	"code.byted.org/volcengine-support/cloud-sherlock/gen/hertz/describe_resource"
	paasdiagnose "code.byted.org/volcengine-support/cloud-sherlock/pkg/paas"
	"code.byted.org/volcengine-support/cloud-sherlock/utils"
	ehttp "code.byted.org/volcengine-support/gopkg/hertzext"
	"github.com/bytedance/gopkg/util/logger"
	"github.com/cloudwego/hertz/pkg/app"
	"github.com/samber/lo"
)

type VKEClusterInstanceHandler struct {
	ehttp.CommonActionHandler
}
type VKEClusterInstance struct {
	ApiVersion string `json:"apiVersion"`
	Kind       string `json:"kind"`
	Metadata   struct {
		Name      string `json:"name"`
		NameSpace string `json:"namespace"`
	} `json:"metadata"`
	Spec *VKEClusterInstanceSpec `json:"spec"`
}

type VKEClusterInstanceSpec struct {
	AccountId                    int64    `json:"accountId"`
	ApiserverLoadBalancerId      string   `json:"apiserverLoadBalancerId"`
	ApiserverPrivateEndpointIPv4 string   `json:"apiserverPrivateEndpointIPv4"`
	ApiserverPublicEndpointIPv4  string   `json:"apiserverPublicEndpointIPv4"`
	ChargeType                   string   `json:"chargeType"`
	ClusterDomain                string   `json:"clusterDomain"`
	ClusterId                    string   `json:"clusterId"`
	ControlPlaneFlavor           string   `json:"controlPlaneFlavor"`
	CreateTime                   string   `json:"createTime"`
	DeleteProtectionEnabled      bool     `json:"deleteProtectionEnabled"`
	KubernetesVersion            string   `json:"kubernetesVersion"`
	MaxPodsPerNode               int      `json:"maxPodsPerNode"`
	MetaCluster                  string   `json:"metaCluster"`
	Name                         string   `json:"name"`
	PodCidrs                     []string `json:"podCidrs"`
	PodNetworkMode               string   `json:"podNetworkMode"`
	Region                       string   `json:"region"`
	SecurityGroupIds             []string `json:"securityGroupIds"`
	ServiceCidrs                 []string `json:"serviceCidrs"`
	StatsPhase                   string   `json:"statsPhase"`
	VpcId                        string   `json:"vpcId"`
}

func (vc *VKEClusterInstance) BuildDiagnoseRequest(ctx context.Context,
	scenario, ticketID string, instanceInfo interface{}) (*paasdiagnose.CreateDiagnoseTaskRequest, error) {
	logger := utils.NewModuleLogger("BuildVkeInstanceDiagnoseRequest")
	if err := utils.JsonCopy(instanceInfo, vc); err != nil {
		logger.CtxError(ctx, "vkeClusterInfo JSON解析失败: %s | 原始数据: %s", err, utils.JsonToString(instanceInfo))
		return nil, err
	}

	// 增加字段校验
	if strings.TrimSpace(vc.Spec.Region) == "" || strings.TrimSpace(vc.Spec.ClusterId) == "" {
		logger.CtxWarn(ctx, "vkeClusterInfo 缺失必要字段 Region=%s ClusterId=%s",
			vc.Spec.Region, vc.Spec.ClusterId)
		return nil, errors.New("vkeClusterInfo 缺失必要字段: Region 或 ClusterId")
	}

	region := strings.TrimSpace(vc.Spec.Region)
	logger.CtxInfo(ctx, "vkeClusterInfo 解析成功 Region=%s ClusterID=%s",
		region, vc.Spec.ClusterId)
	diagnoseRequestBody := &paasdiagnose.CreateDiagnoseTaskRequest{
		TargetType: "VkeInstance",
		Region:     region,
		Identities: []string{vc.Spec.ClusterId},
		Scenario:   scenario,
		OrderId:    ticketID,
	}
	return diagnoseRequestBody, nil

}

func (vc *VKEClusterInstance) BuildListEntitiesRequest(ctx context.Context, instances []string) (*ListEntitiesRequest, error) {
	ins := lo.FilterMap(instances, func(clusterID string, index int) (FieldFilter, bool) {
		return []FieldFilterItem{
			{
				Field: "metadata.name",
				Value: clusterID,
			}, {
				Field:    "spec",
				Operator: OperatorNotEq,
				Value:    nil,
			},
		}, true
	})
	listResourcesRequest := &ListEntitiesRequest{
		Kind: "VkeInstance",
		Filter: ListEntitiesFilter{
			FieldFilters: ins,
		},
	}
	return listResourcesRequest, nil
}

func (vc *VKEClusterInstance) GetResources(ctx context.Context, c *app.RequestContext) error {
	logger := utils.NewModuleLogger("GetVkeInstanceResources")
	params := &describe_resource.DescribeResourceRequest{}
	err := utils.JsonUnmarshal(c.Request.Body(), params)
	if err != nil {
		ehttp.DoResponse(ctx, c, errorcode.ErrRequestParamInvalid.WithArgs(err))
		return err
	}
	ins := params.Resources.Instances
	allItems := []interface{}{}
	var nextToken string
	for {
		listEntitiesRequest, err := vc.BuildListEntitiesRequest(ctx, ins)
		if err != nil {
			// logger.CtxWarn(ctx, "BuildListEntitiesRequest error: %s", err)
			return err
		}
		listEntitiesRequest.MaxResults = 100
		listEntitiesRequest.NextToken = nextToken

		listEntitiesResponse, err := GetCMDBClient().ListEntities(ctx, listEntitiesRequest)
		if err != nil {
			// logger.CtxWarn(ctx, "ListEntities error: %s", err)
			return err
		}
		allItems = append(allItems, listEntitiesResponse.Items...)

		if listEntitiesResponse.NextToken == "EOF" {
			break
		}
		nextToken = listEntitiesResponse.NextToken
	}
	logger.CtxInfo(ctx, "ListVKEEntitiesResponse : %s\n", utils.JsonToString(allItems))
	if len(allItems) == 0 {
		// logger.CtxWarn(ctx, "ListEntities response is empty")
		// ehttp.ErrorResp(ctx, c, errorcode.ErrDataNotFound)
		return errorcode.ErrDataNotFound.WithArgs("vkeInstanceInfo is empty") // --- IGNORE ---
	}
	vkeInstanceInfo := &VKEClusterInstance{}

	err = utils.JsonCopy(allItems[0], vkeInstanceInfo)
	if err != nil {
		// logger.CtxWarn(ctx, "JsonCopy error: %s", err)
		// ehttp.ErrorResp(ctx, c, errorcode.ErrCommonInternalError.WithArgs(err))
		return errorcode.ErrCommonInternalError.WithArgs(err)
	}
	if vkeInstanceInfo.Spec != nil {
		// logger.CtxWarn(ctx, "ClusterId is empty")
		// resourceData := &DescribeResourceResponse{}
		resourceMetadata := []*ResourceEntry{
			CreateEntry("AccountId", "账号名称", vkeInstanceInfo.Spec.AccountId),
			CreateEntry("ClusterId", "集群ID", vkeInstanceInfo.Spec.ClusterId),
			CreateEntry("Region", "地域", vkeInstanceInfo.Spec.Region),
			CreateEntry("ClusterName", "集群名称", vkeInstanceInfo.Spec.Name),
			CreateEntry("KubernetesVersion", "Kubernetes版本", vkeInstanceInfo.Spec.KubernetesVersion),
			CreateEntry("ControlPlaneFlavor", "控制平面规格", vkeInstanceInfo.Spec.ControlPlaneFlavor),
			CreateEntry("CreateTime", "创建时间", vkeInstanceInfo.Spec.CreateTime),
			CreateEntry("StatsPhase", "集群状态", vkeInstanceInfo.Spec.StatsPhase),
			CreateEntry("DeleteProtectionEnabled", "删除保护开启状态", vkeInstanceInfo.Spec.DeleteProtectionEnabled),
			CreateEntry("MaxPodsPerNode", "最大Pod数量", vkeInstanceInfo.Spec.MaxPodsPerNode),
			CreateEntry("MetaCluster", "元集群", vkeInstanceInfo.Spec.MetaCluster),
			CreateEntry("ChargeType", "计费类型", vkeInstanceInfo.Spec.ChargeType),
		}
		resourceNetworkInfo := []*ResourceEntry{
			CreateEntry("ApiserverLoadBalancerId", "apiServer负载均衡ID", vkeInstanceInfo.Spec.ApiserverLoadBalancerId),
			CreateEntry("ApiserverPrivateEndpointIPv4", "apiServer私网IP", vkeInstanceInfo.Spec.ApiserverPrivateEndpointIPv4),
			CreateEntry("ApiserverPublicEndpointIPv4", "apiServer公网IP", vkeInstanceInfo.Spec.ApiserverPublicEndpointIPv4),
			CreateEntry("VpcId", "VPC ID", vkeInstanceInfo.Spec.VpcId),
			CreateEntry("PodNetworkMode", "Pod网络模式", vkeInstanceInfo.Spec.PodNetworkMode),
			CreateEntry("SecurityGroupIds", "安全组", strings.Join(vkeInstanceInfo.Spec.SecurityGroupIds, ",")),
			CreateEntry("ServiceCidrs", "ServiceCidrs", strings.Join(vkeInstanceInfo.Spec.ServiceCidrs, ",")),
		}
		originPlatformLink := "https://infra-ops.byted.org/product/paas-ops/diagnosis/vke"
		if vkeOpsEnvNum, ok := VkeOpsEnvMap[vkeInstanceInfo.Spec.Region]; ok {
			originPlatformLink = fmt.Sprintf("https://infra-ops.byted.org/product/paas-ops/diagnosis/vke/region/%s/env/%d/%s", vkeInstanceInfo.Spec.Region, vkeOpsEnvNum, vkeInstanceInfo.Spec.ClusterId)
		}

		//根据账号信息获取客户的客户编码，生成烽火平台的跳转链接
		accountID := fmt.Sprintf("%d", vkeInstanceInfo.Spec.AccountId)
		customerInfo := &CustomerInfo{
			AccountId: lo.ToPtr(accountID),
		}
		resp, err := fh_openapi.ListCustomerVolcAccount(&fh_hertz.ListCustomerVolcAccountRequest{
			VolcAccountID: []string{accountID},
		})
		if err != nil {
			// ehttp.ErrorResp(ctx, c, errorcode.ErrRequestParamInvalid.WithArgs(err))
			return errorcode.ErrRequestParamInvalid.WithArgs(err)
		}
		if len(resp.Result) <= 0 {
			customerInfo.CustomerName = lo.ToPtr(fmt.Sprintf("volcAccountID: %s未找到客户信息", accountID))
		} else {
			if resp.Result[0] != nil {
				customerInfo.CustomerLink = lo.ToPtr(fh_openapi.GetCustomerInfoLink(resp.Result[0].CustomerNumber))
				customerInfo.CustomerName = resp.Result[0].CustomerName
			} else {
				customerInfo.CustomerName = lo.ToPtr(fmt.Sprintf("volcAccountID: %s未找到客户信息", accountID))
			}
		}

		vkeInstanceInfoResp := &DescribeResourceResponse{
			ResourceType:        "VkeInstance",
			ResourceMetadata:    resourceMetadata,
			ResourceNetworkInfo: resourceNetworkInfo,
			CustomerInfo:        customerInfo,
			OriginMsg:           vkeInstanceInfo,
			OriginPlatformLink:  originPlatformLink,
		}
		// 获取Grafana链接
		if url := vc.getGrafanaURL(ctx, vkeInstanceInfo); url != nil {
			vkeInstanceInfoResp.GrafanaLink = url
			vkeInstanceInfoResp.ResourceMetadata = append(vkeInstanceInfoResp.ResourceMetadata,
				CreateEntry("GrafanaLink", "监控链接", *url))
		}

		ehttp.DoResponse(ctx, c, vkeInstanceInfoResp)

	} else {

		// ehttp.ErrorResp(ctx, c, errorcode.ErrDataNotFound)
		return errorcode.ErrDataNotFound.WithArgs("vkeInstanceInfo is empty") // --- IGNORE ---
	}

	return nil

}

func (vc *VKEClusterInstance) getGrafanaURL(ctx context.Context, instance *VKEClusterInstance) *string {
	productCategory, err := product_category.GetProductCategoryService().GetProductCategoryByResourceType(ctx, vc.GetResourceType())
	if err != nil || productCategory == nil || productCategory.GrafanaTemplate == nil {
		logger.CtxWarnf(ctx, "get grafana url failed: %v", err)
		return nil
	}

	data := map[string]string{
		"Region":       instance.Spec.Region,
		"VkeClusterID": instance.Spec.ClusterId,
		"AccountID":    fmt.Sprintf("%d", instance.Spec.AccountId),
	}
	url, err := utils.RenderGrafanaTemplate(*productCategory.GrafanaTemplate, data)
	if err != nil {
		logger.CtxWarnf(ctx, "render grafana template failed: %v", err)
		return nil
	}
	return &url
}

func (vc *VKEClusterInstance) GetResourceType() string {
	return "VkeInstance"
}
func (vn *VKEClusterInstance) Match(ctx context.Context, inputInfo *describe_resource.DescribeResourceRequest) bool {
	// 优先检查明确指定的resourceType
	if strings.TrimSpace(inputInfo.Resources.ResourceType) != "" {
		return strings.TrimSpace(inputInfo.Resources.ResourceType) == vn.GetResourceType()
	}

	// 当resourceType为空时，进行实例ID正则匹配
	regex := regexp.MustCompile(`^c[a-z0-9]{20}$`) // 严格匹配：c开头 + 20位小写字母/数字，总长度21
	for _, resource := range inputInfo.Resources.Instances {
		if regex.MatchString(resource) {
			return true
		}
	}

	return false
}

func (vc *VKEClusterInstance) GetResourcesDependency(ctx context.Context, c *app.RequestContext) error {
	// logger := utils.NewModuleLogger("GetVkeInstanceResources")
	params := &describe_resource.DescribeResourceRequest{}
	err := utils.JsonUnmarshal(c.Request.Body(), params)
	if err != nil {
		ehttp.DoResponse(ctx, c, errorcode.ErrRequestParamInvalid.WithArgs(err))
		return err
	}
	ins := params.Resources.Instances
	vkeInstanceInfo, err := GetVkeCLusterResource(ctx, ins)
	if err != nil {
		// ehttp.ErrorResp(ctx, c, errorcode.ErrCommonInternalError.WithArgs(err))
		return errorcode.ErrCommonInternalError.WithArgs(err)
	}
	if vkeInstanceInfo.Spec != nil {
		result := DescribeResourceDependencyResponse{}
		if vkeInstanceInfo.Spec.ClusterId != "" {
			result.InstanceID = vkeInstanceInfo.Spec.ClusterId
		}

		//获取VkeNode的信息返回数据
		//获取vkeInstance的信息，生成返回数据
		vkeCluster := &Product{}
		vkeCluster.ProductName = "VKE集群"
		vkeCluster.Header = []*Field{
			AddField("ResourceID", "资源ID"),
			AddField("ResourceName", "资源名称"),
			AddField("Region", "地域"),
			AddField("AccountID", "账号ID"),
			AddField("KubernetesVersion", "Kubernetes版本"),
			AddField("SecurityGroupIds", "安全组"),
			AddField("PodNetworkMode", "网络模型"),
		}
		vkeCluster.Items = []map[string]interface{}{
			{
				"ResourceID":        vkeInstanceInfo.Spec.ClusterId,
				"ResourceName":      vkeInstanceInfo.Spec.Name,
				"Region":            vkeInstanceInfo.Spec.Region,
				"AccountID":         vkeInstanceInfo.Spec.AccountId,
				"KubernetesVersion": vkeInstanceInfo.Spec.KubernetesVersion,
				"SecurityGroupIds":  vkeInstanceInfo.Spec.SecurityGroupIds,
				"PodNetworkMode":    vkeInstanceInfo.Spec.PodNetworkMode,
			},
		}
		result.Products = append(result.Products, vkeCluster)

		ehttp.DoResponse(ctx, c, result)
	} else {

		// ehttp.ErrorResp(ctx, c, errorcode.ErrDataNotFound)
		return errorcode.ErrRequestParamInvalid.WithArgs("ClusterId is empty")
	}

	return nil
}
