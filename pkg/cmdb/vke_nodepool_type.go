package cmdb

import (
	"context"
	"errors"
	"fmt"
	"regexp"
	"strings"
	"time"

	fh_hertz "code.byted.org/volcengine-support/cloud-sherlock/gen/hertz/fh_openapi"
	"code.byted.org/volcengine-support/cloud-sherlock/pkg/fh_openapi"

	"code.byted.org/volcengine-support/cloud-sherlock/biz/errorcode"
	"code.byted.org/volcengine-support/cloud-sherlock/biz/service/product_category"
	"code.byted.org/volcengine-support/cloud-sherlock/gen/hertz/describe_resource"
	paasdiagnose "code.byted.org/volcengine-support/cloud-sherlock/pkg/paas"
	"code.byted.org/volcengine-support/cloud-sherlock/utils"
	ehttp "code.byted.org/volcengine-support/gopkg/hertzext"
	"github.com/bytedance/gopkg/util/logger"
	"github.com/cloudwego/hertz/pkg/app"
	"github.com/samber/lo"
)

type VkeNodePool struct {
	ApiVersion string `json:"apiVersion"`
	Kind       string `json:"kind"`
	Metadata   struct {
		Name        string            `json:"name"`
		Namespace   string            `json:"namespace"`
		Annotations map[string]string `json:"annotations"`
	}
	Spec *VkeNodePoolSpec `json:"spec"`
}

type VkeNodePoolSpec struct {
	AccountId                  int64     `json:"accountId"`
	AutoscalingDesiredReplicas int       `json:"autoscalingDesiredReplicas"`
	AutoscalingEnabled         bool      `json:"autoscalingEnabled"`
	AutoscalingMaxReplicas     int       `json:"autoscalingMaxReplicas"`
	AutoscalingMinReplicas     int       `json:"autoscalingMinReplicas"`
	AutoscalingPriority        int       `json:"autoscalingPriority"`
	ClusterId                  string    `json:"clusterId"`
	Cordon                     bool      `json:"cordon"`
	CreateTime                 time.Time `json:"createTime"`
	DeleteAt                   time.Time `json:"deleteAt"`
	Id                         int64     `json:"id"`
	InstanceTypeIds            []string  `json:"instance_type_ids"` // example: ["ecs.c3il.4xlarge"]
	Labels                     string    `json:"labels"`
	Name                       string    `json:"name"`
	NodePoolId                 string    `json:"nodePoolId"`
	Region                     string    `json:"region"`
	SpotStrategy               string    `json:"spotStrategy"`
	StatusPhase                string    `json:"statusPhase"`
	SubnetIds                  []string  `json:"subnet_ids"` // example : ["subnet-12345678"]
	Taints                     string    `json:"taints"`
	UpdateTime                 time.Time `json:"updateTime"`
}

func (vnp *VkeNodePool) BuildDiagnoseRequest(ctx context.Context,
	scenario, ticketID string, instanceInfo interface{}) (*paasdiagnose.CreateDiagnoseTaskRequest, error) {
	logger := utils.NewModuleLogger("BuildVkeNodePoolDiagnoseRequest")
	if err := utils.JsonCopy(instanceInfo, vnp); err != nil {
		logger.CtxError(ctx, "VkeNodePool JSON解析失败: %s | 原始数据: %s", err, utils.JsonToString(instanceInfo))
		return nil, err
	}

	// 增加字段校验
	if strings.TrimSpace(vnp.Spec.Region) == "" {
		logger.CtxWarn(ctx, "VkeNodePool 缺失必要字段 Region=%s ",
			vnp.Spec.Region)
		return nil, errors.New("VkeNodePool 缺失必要字段: Region")
	}

	region := strings.TrimSpace(vnp.Spec.Region)
	logger.CtxInfo(ctx, "vkeNodePoolInfo 解析成功 Region=%s ",
		region)

	diagnoseRequestBody := &paasdiagnose.CreateDiagnoseTaskRequest{
		TargetType: "VkeNodePool",
		Region:     region,
		Identities: []string{vnp.Spec.NodePoolId},
		Scenario:   scenario,
		OrderId:    ticketID,
	}
	return diagnoseRequestBody, nil

}

func (vnp *VkeNodePool) BuildListEntitiesRequest(ctx context.Context, instances []string) (*ListEntitiesRequest, error) {
	ins := lo.FilterMap(instances, func(nodePoolID string, index int) (FieldFilter, bool) {
		return []FieldFilterItem{
			{
				Field: "spec.nodePoolId",
				Value: nodePoolID,
			}, {
				Field:    "spec",
				Operator: OperatorNotEq,
				Value:    nil,
			},
		}, true
	})
	listResourcesRequest := &ListEntitiesRequest{
		Kind: "VkeNodePool",
		Filter: ListEntitiesFilter{
			FieldFilters: ins,
		},
	}
	return listResourcesRequest, nil
}

func (vnp *VkeNodePool) GetResourceType() string {
	return "VkeNodePool"
}

func (vnp *VkeNodePool) GetResources(ctx context.Context, c *app.RequestContext) error {
	logger := utils.NewModuleLogger("GetVkeNodePoolResources")
	params := &describe_resource.DescribeResourceRequest{}
	err := utils.JsonUnmarshal(c.Request.Body(), params)
	if err != nil {
		ehttp.DoResponse(ctx, c, errorcode.ErrRequestParamInvalid.WithArgs(err))
		return err
	}
	ins := params.Resources.Instances
	allItems := []interface{}{}
	var nextToken string
	for {
		listEntitiesRequest, err := vnp.BuildListEntitiesRequest(ctx, ins)
		if err != nil {
			// logger.CtxWarn(ctx, "BuildListEntitiesRequest error: %s", err)
			return err
		}
		listEntitiesRequest.MaxResults = 100
		listEntitiesRequest.NextToken = nextToken

		listEntitiesResponse, err := GetCMDBClient().ListEntities(ctx, listEntitiesRequest)
		if err != nil {
			// logger.CtxWarn(ctx, "ListEntities error: %s", err)
			return err
		}
		allItems = append(allItems, listEntitiesResponse.Items...)

		if listEntitiesResponse.NextToken == "EOF" {
			break
		}
		nextToken = listEntitiesResponse.NextToken
	}
	logger.CtxInfo(ctx, "ListVkeNodePoolEntitiesResponse : %s\n", utils.JsonToString(allItems))
	if len(allItems) == 0 {
		// logger.CtxWarn(ctx, "ListEntities response is empty")
		return errorcode.ErrDataNotFound.WithArgs("vkeNodePoolInfo is empty") // --- IGNORE ---
	}
	vkeNodPooleInfo := &VkeNodePool{}

	err = utils.JsonCopy(allItems[0], vkeNodPooleInfo)
	if err != nil {
		// logger.CtxWarn(ctx, "JsonCopy error: %s", err)
		return errorcode.ErrCommonInternalError.WithArgs(err)
	}
	if vkeNodPooleInfo.Spec != nil {
		resourceMetadata := []*ResourceEntry{
			CreateEntry("AccountId", "账号ID", vkeNodPooleInfo.Spec.AccountId),
			CreateEntry("Name", "节点池名称", strings.Join(vkeNodPooleInfo.Spec.InstanceTypeIds, ",")),
			CreateEntry("NodePoolId", "节点池ID", vkeNodPooleInfo.Spec.NodePoolId),
			CreateEntry("Status", "节点池状态", vkeNodPooleInfo.Spec.StatusPhase),
			CreateEntry("Region", "地域", vkeNodPooleInfo.Spec.Region),

			CreateEntry("AutoscalingDesiredReplicas", "节点池期望数量", vkeNodPooleInfo.Spec.AutoscalingDesiredReplicas),
			CreateEntry("AutoscalingEnabled", "是否开启自动弹性伸缩", vkeNodPooleInfo.Spec.AutoscalingEnabled),
			CreateEntry("AutoscalingMaxReplicas", "节点池最大节点数量", vkeNodPooleInfo.Spec.AutoscalingMaxReplicas),
			CreateEntry("AutoscalingPriority", "节点池伸缩优先级", vkeNodPooleInfo.Spec.AutoscalingPriority),
			CreateEntry("ClusterId", "VKE集群ID", vkeNodPooleInfo.Spec.ClusterId),
			CreateEntry("Cordon", "是否开启节点封锁", vkeNodPooleInfo.Spec.Cordon),
			CreateEntry("CreateTime", "创建时间", vkeNodPooleInfo.Spec.CreateTime),
			CreateEntry("InstanceTypeIds", "节点规格", strings.Join(vkeNodPooleInfo.Spec.InstanceTypeIds, ",")),
			CreateEntry("Taints", "污点", vkeNodPooleInfo.Spec.Taints),
			CreateEntry("Labels", "标签", vkeNodPooleInfo.Spec.Labels),
			CreateEntry("SpotStrategy", "竞价策略", vkeNodPooleInfo.Spec.SpotStrategy),
			CreateEntry("UpdateTime", "更新时间", vkeNodPooleInfo.Spec.UpdateTime),
		}

		//根据账号信息获取客户的客户编码，生成烽火平台的跳转链接
		accountID := fmt.Sprintf("%d", vkeNodPooleInfo.Spec.AccountId)
		customerInfo := &CustomerInfo{
			AccountId: lo.ToPtr(accountID),
		}
		resp, err := fh_openapi.ListCustomerVolcAccount(&fh_hertz.ListCustomerVolcAccountRequest{
			VolcAccountID: []string{accountID},
		})
		if err != nil {
			// ehttp.ErrorResp(ctx, c, errorcode.ErrRequestParamInvalid.WithArgs(err))
			return errorcode.ErrUpStreamSystemError.WithArgs(err)
		}
		if len(resp.Result) <= 0 {
			customerInfo.CustomerName = lo.ToPtr(fmt.Sprintf("volcAccountID: %s未找到客户信息", accountID))
		} else {
			if resp.Result[0] != nil {
				customerInfo.CustomerLink = lo.ToPtr(fh_openapi.GetCustomerInfoLink(resp.Result[0].CustomerNumber))
				customerInfo.CustomerName = resp.Result[0].CustomerName
			} else {
				customerInfo.CustomerName = lo.ToPtr(fmt.Sprintf("volcAccountID: %s未找到客户信息", accountID))
			}
		}

		resourceNetworkInfo := []*ResourceEntry{
			CreateEntry("SubnetIds", "子网ID", strings.Join(vkeNodPooleInfo.Spec.SubnetIds, ",")),
		}
		vkeNodePoolInfoResp := &DescribeResourceResponse{
			ResourceType:        vkeNodPooleInfo.GetResourceType(),
			ResourceMetadata:    resourceMetadata,
			ResourceNetworkInfo: resourceNetworkInfo,
			CustomerInfo:        customerInfo,
			OriginMsg:           vkeNodPooleInfo,
		}
		// 获取Grafana链接
		if url := vnp.getGrafanaURL(ctx, vkeNodPooleInfo); url != nil {
			vkeNodePoolInfoResp.GrafanaLink = url
			vkeNodePoolInfoResp.ResourceMetadata = append(vkeNodePoolInfoResp.ResourceMetadata,
				CreateEntry("GrafanaLink", "监控链接", *url))
		}
		ehttp.DoResponse(ctx, c, vkeNodePoolInfoResp)

	} else {
		return errorcode.ErrDataNotFound.WithArgs("vkeNodePoolInfo is empty") // --- IGNORE ---
	}

	return nil

}

const (
	nodePoolIDPattern = `^p[a-z0-9]{20}$`
)

// Match 验证节点池ID格式：p开头 + 19位小写字母数字
func (vnp *VkeNodePool) Match(ctx context.Context, inputInfo *describe_resource.DescribeResourceRequest) bool {
	if strings.TrimSpace(inputInfo.Resources.ResourceType) != "" {
		return strings.TrimSpace(inputInfo.Resources.ResourceType) == vnp.GetResourceType()
	}
	regex := regexp.MustCompile(nodePoolIDPattern)
	for _, resource := range inputInfo.Resources.Instances {
		if regex.MatchString(resource) {
			return true
		}
	}
	return false
}

func (vnp *VkeNodePool) getGrafanaURL(ctx context.Context, instance *VkeNodePool) *string {
	productCategory, err := product_category.GetProductCategoryService().GetProductCategoryByResourceType(ctx, "vkeNodePool")
	if err != nil || productCategory == nil || productCategory.GrafanaTemplate == nil {
		logger.CtxWarnf(ctx, "get grafana url failed: %v", err)
		return nil
	}

	data := map[string]string{
		"Region":        instance.Spec.Region,
		"VkeClusterID":  instance.Spec.ClusterId,
		"AccountID":     fmt.Sprintf("%d", instance.Spec.AccountId),
		"VkeNodePoolID": instance.Spec.NodePoolId,
	}
	url, err := utils.RenderGrafanaTemplate(*productCategory.GrafanaTemplate, data)
	if err != nil {
		logger.CtxWarnf(ctx, "render grafana template failed: %v", err)
		return nil
	}
	return &url
}

func (vnp *VkeNodePool) GetResourcesDependency(ctx context.Context, c *app.RequestContext) error {
	// logger := utils.NewModuleLogger("GetVkeInstanceResources")
	params := &describe_resource.DescribeResourceRequest{}
	err := utils.JsonUnmarshal(c.Request.Body(), params)
	if err != nil {
		ehttp.DoResponse(ctx, c, errorcode.ErrRequestParamInvalid.WithArgs(err))
		return err
	}
	vkeNodPooleInfo, err := GetVkeNodePoolResource(ctx, params.Resources.Instances)
	if err != nil {
		// logger.CtxWarn(ctx, "GetVkeNodePoolResource error: %s", err)
		ehttp.ErrorResp(ctx, c, errorcode.ErrCommonInternalError.WithArgs(err))
		return err
	}
	if vkeNodPooleInfo.Spec != nil {
		result := &DescribeResourceDependencyResponse{}
		if vkeNodPooleInfo.Spec.NodePoolId != "" {
			result.InstanceID = vkeNodPooleInfo.Spec.NodePoolId
		}

		//获取vkeInstance的信息，生成返回数据
		vkeCluster := &Product{}
		vkeCluster.ProductName = "VKE集群"
		vkeCluster.Header = []*Field{
			AddField("ResourceID", "资源ID"),
			AddField("ResourceName", "资源名称"),
			AddField("Region", "地域"),
			AddField("AccountID", "账号ID"),
			AddField("KubernetesVersion", "Kubernetes版本"),
			AddField("SecurityGroupIds", "安全组"),
			AddField("PodNetworkMode", "网络模型"),
		}
		vkeClusterInfo, err := GetVkeCLusterResource(ctx, []string{vkeNodPooleInfo.Spec.ClusterId})
		if err != nil {
			// logger.CtxWarn(ctx, "GetVkeCLusterResource error: %s", err)
			ehttp.ErrorResp(ctx, c, errorcode.ErrCommonInternalError.WithArgs(err))
			return err
		}
		vkeCluster.Items = []map[string]interface{}{
			{
				"ResourceID":        vkeClusterInfo.Spec.ClusterId,
				"ResourceName":      vkeClusterInfo.Spec.Name,
				"Region":            vkeClusterInfo.Spec.Region,
				"AccountID":         vkeClusterInfo.Spec.AccountId,
				"KubernetesVersion": vkeClusterInfo.Spec.KubernetesVersion,
				"SecurityGroupIds":  vkeClusterInfo.Spec.SecurityGroupIds,
				"PodNetworkMode":    vkeClusterInfo.Spec.PodNetworkMode,
			},
		}
		result.Products = append(result.Products, vkeCluster)

		//获取vkeNodePool的信息，生成返回数据
		vkeNodePool := &Product{}
		vkeNodePool.ProductName = "VKE节点池"
		vkeNodePool.Header = []*Field{
			AddField("ResourceID", "资源ID"),
			AddField("ResourceName", "资源名称"),
			AddField("AccountID", "账号ID"),
			AddField("ClusterID", "集群ID"),
			AddField("InstanceTypeIds", "节点规格"),
		}
		vkeNodePool.Items = []map[string]interface{}{
			{
				"ResourceID":      vkeNodPooleInfo.Spec.NodePoolId,
				"ResourceName":    vkeNodPooleInfo.Spec.Name,
				"AccountID":       vkeNodPooleInfo.Spec.AccountId,
				"ClusterID":       vkeNodPooleInfo.Spec.ClusterId,
				"InstanceTypeIds": strings.Join(vkeNodPooleInfo.Spec.InstanceTypeIds, ","),
			},
		}
		result.Products = append(result.Products, vkeNodePool)

		ehttp.DoResponse(ctx, c, result)

	} else {
		ehttp.ErrorResp(ctx, c, errorcode.ErrCommonInternalError.WithArgs("vkeNodePoolInfo.Spec is nil"))
	}

	return nil

}
