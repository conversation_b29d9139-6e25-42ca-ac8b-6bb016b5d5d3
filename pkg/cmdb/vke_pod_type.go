package cmdb

import (
	"context"
	"errors"
	"fmt"
	"regexp"
	"strings"

	fh_hertz "code.byted.org/volcengine-support/cloud-sherlock/gen/hertz/fh_openapi"
	"code.byted.org/volcengine-support/cloud-sherlock/pkg/fh_openapi"

	"code.byted.org/volcengine-support/cloud-sherlock/biz/errorcode"
	"code.byted.org/volcengine-support/cloud-sherlock/biz/service/product_category"
	"code.byted.org/volcengine-support/cloud-sherlock/gen/hertz/describe_resource"
	paasdiagnose "code.byted.org/volcengine-support/cloud-sherlock/pkg/paas"
	"code.byted.org/volcengine-support/cloud-sherlock/utils"
	ehttp "code.byted.org/volcengine-support/gopkg/hertzext"
	"github.com/bytedance/gopkg/util/logger"
	"github.com/cloudwego/hertz/pkg/app"
	"github.com/samber/lo"
	corev1 "k8s.io/api/core/v1"
)

type VkePod struct {
	ApiVersion string `json:"apiVersion"`
	Kind       string `json:"kind"` // VkePod
	Metadata   struct {
		Name      string `json:"name"`
		Namespace string `json:"namespace"`
	} `json:"metadata"`

	Spec *VkePodSpec `json:"spec"`
}

type VkePodSpec struct {
	AccountId int64       `json:"accountId"`
	ClusterId string      `json:"clusterId"`
	Namespace string      `json:"namespace"`
	Node      VkeNode     `json:"node"`
	Pod       *corev1.Pod `json:"pod"`
	Region    string      `json:"region"`
}

func (vp *VkePod) BuildDiagnoseRequest(ctx context.Context,
	scenario, ticketID string, instanceInfo interface{}) (*paasdiagnose.CreateDiagnoseTaskRequest, error) {

	logger := utils.NewModuleLogger("BuildVkePodDiagnoseRequest")
	if err := utils.JsonCopy(instanceInfo, vp); err != nil {
		logger.CtxError(ctx, "vkePodInfo JSON解析失败: %s | 原始数据: %s", err, utils.JsonToString(instanceInfo))
		return nil, err
	}

	// 增加字段校验
	if strings.TrimSpace(vp.Spec.Region) == "" || strings.TrimSpace(vp.Spec.ClusterId) == "" {
		logger.CtxWarn(ctx, "vkeClusterInfo 缺失必要字段 Region=%s ClusterId=%s",
			vp.Spec.Region, vp.Spec.ClusterId)
		return nil, errors.New("vkeClusterInfo 缺失必要字段: Region 或 ClusterId")
	}

	region := strings.TrimSpace(vp.Spec.Region)
	logger.CtxInfo(ctx, "vkeClusterInfo 解析成功 Region=%s ClusterID=%s",
		region, vp.Spec.ClusterId)

	diagnoseRequestBody := &paasdiagnose.CreateDiagnoseTaskRequest{
		TargetType: "VkePod",
		Region:     region,
		Identities: []string{fmt.Sprintf("%s/%s/%s", vp.Spec.ClusterId, vp.Spec.Namespace, vp.Spec.Pod.Name)},
		Scenario:   scenario,
		OrderId:    ticketID,
	}
	return diagnoseRequestBody, nil

}

func (vp *VkePod) BuildListEntitiesRequest(ctx context.Context, instances []string) (*ListEntitiesRequest, error) {
	ins := lo.FilterMap(instances, func(item string, index int) (FieldFilter, bool) {
		args := strings.Split(item, ":")
		if len(args) != 3 {
			return nil, false
		}
		clusterID := args[0]
		namespace := args[1]
		podName := args[2]
		return []FieldFilterItem{
			{
				Field: "spec.clusterId",
				Value: clusterID,
			},
			{
				Field: "spec.namespace",
				Value: namespace,
			},
			{
				Field: "metadata.name",
				Value: podName,
			},
		}, true
	})
	listResourcesRequest := &ListEntitiesRequest{
		Kind: "VkePod",
		Filter: ListEntitiesFilter{
			FieldFilters: ins,
		},
	}
	return listResourcesRequest, nil
}

func (vp *VkePod) GetResourceType() string {
	return "VkePod"
}

func (vp *VkePod) GetResources(ctx context.Context, c *app.RequestContext) error {
	logger := utils.NewModuleLogger("GetVkePodResources")
	params := &describe_resource.DescribeResourceRequest{}
	err := utils.JsonUnmarshal(c.Request.Body(), params)
	if err != nil {
		// ehttp.DoResponse(ctx, c, errorcode.ErrRequestParamInvalid.WithArgs(err))
		return errorcode.ErrRequestParamInvalid.WithArgs(err)
	}
	ins := params.Resources.Instances
	allItems := []interface{}{}
	var nextToken string
	for {
		listEntitiesRequest, err := vp.BuildListEntitiesRequest(ctx, ins)
		if err != nil {
			// logger.CtxWarn(ctx, "BuildListEntitiesRequest error: %s", err)
			return err
		}
		listEntitiesRequest.MaxResults = 100
		listEntitiesRequest.NextToken = nextToken

		listEntitiesResponse, err := GetCMDBClient().ListEntities(ctx, listEntitiesRequest)
		if err != nil {
			// logger.CtxWarn(ctx, "ListEntities error: %s", err)
			return err
		}
		allItems = append(allItems, listEntitiesResponse.Items...)

		if listEntitiesResponse.NextToken == "EOF" {
			break
		}
		nextToken = listEntitiesResponse.NextToken
	}
	logger.CtxInfo(ctx, "ListVkePodEntitiesResponse : %s\n", utils.JsonToString(allItems))
	if len(allItems) == 0 {
		// logger.CtxWarn(ctx, "ListEntities response is empty")
		// ehttp.ErrorResp(ctx, c, errorcode.ErrDataNotFound)
		return errorcode.ErrDataNotFound.WithArgs("vkePodInfo is empty") // --- IGNORE ---
	}
	vkePodInfo := &VkePod{}
	err = utils.JsonCopy(allItems[0], vkePodInfo)
	if err != nil {
		// logger.CtxWarn(ctx, "JsonCopy error: %s", err)
		// ehttp.ErrorResp(ctx, c, errorcode.ErrCommonInternalError.WithArgs(err))
		return errorcode.ErrCommonInternalError.WithArgs(err)
	}
	if vkePodInfo.Spec != nil {
		if vkePodInfo.Spec.Pod != nil {
			resourceMetadata := []*ResourceEntry{
				CreateEntry("ClusterId", "VKE集群ID", vkePodInfo.Spec.ClusterId),
				CreateEntry("PodName", "PodName", vkePodInfo.Spec.Pod.Name),
				CreateEntry("Region", "Region", vkePodInfo.Spec.Region),
				CreateEntry("Namespace", "命名空间", vkePodInfo.Spec.Pod.Namespace),
				CreateEntry("NodeName", "所在节点名称", vkePodInfo.Spec.Pod.Spec.NodeName),
				CreateEntry("NodeIP", "所在节点IP", vkePodInfo.Spec.Pod.Status.HostIP),
				CreateEntry("PodIP", "Pod IP", vkePodInfo.Spec.Pod.Status.PodIP),
				CreateEntry("PodStatus", "状态", vkePodInfo.Spec.Pod.Status.Phase),
				CreateEntry("RestartPolicy", "重启策略", vkePodInfo.Spec.Pod.Spec.RestartPolicy),
				CreateEntry("schedulerName", "调度器", vkePodInfo.Spec.Pod.Spec.SchedulerName),
				CreateEntry("TerminationGracePeriodSeconds", "优雅终止时间", vkePodInfo.Spec.Pod.Spec.TerminationGracePeriodSeconds),
				CreateEntry("DnsPolicy", "DNS策略", vkePodInfo.Spec.Pod.Spec.DNSPolicy),
				CreateEntry("QosClass", "QOS类", vkePodInfo.Spec.Pod.Status.QOSClass),
				CreateEntry("StartTime", "启动时间", vkePodInfo.Spec.Pod.Status.StartTime),
				CreateEntry("CreateTime", "创建时间", vkePodInfo.Spec.Pod.CreationTimestamp),
				CreateEntry("Labels", "Labels", strings.Join(lo.MapToSlice(vkePodInfo.Spec.Pod.Labels, func(k, v string) string {
					return fmt.Sprintf("%s:%s", k, v)
				}), ",")),
				CreateEntry("Annotations", "Annotations", strings.Join(lo.MapToSlice(vkePodInfo.Spec.Pod.Annotations, func(k, v string) string {
					return fmt.Sprintf("%s:%s", k, v)
				}), ",")),
			}
			resourceNetworkInfo := []*ResourceEntry{}

			//根据账号信息获取客户的客户编码，生成烽火平台的跳转链接
			accountID := fmt.Sprintf("%d", vkePodInfo.Spec.AccountId)
			customerInfo := &CustomerInfo{
				AccountId: lo.ToPtr(accountID),
			}
			resp, err := fh_openapi.ListCustomerVolcAccount(&fh_hertz.ListCustomerVolcAccountRequest{
				VolcAccountID: []string{accountID},
			})
			if err != nil {
				// ehttp.ErrorResp(ctx, c, errorcode.ErrRequestParamInvalid.WithArgs(err))
				return errorcode.ErrUpStreamSystemError.WithArgs(err)
			}
			if len(resp.Result) <= 0 {
				customerInfo.CustomerName = lo.ToPtr(fmt.Sprintf("volcAccountID: %s未找到客户信息", accountID))
			} else {
				if resp.Result[0] != nil {
					customerInfo.CustomerLink = lo.ToPtr(fh_openapi.GetCustomerInfoLink(resp.Result[0].CustomerNumber))
					customerInfo.CustomerName = resp.Result[0].CustomerName
				} else {
					customerInfo.CustomerName = lo.ToPtr(fmt.Sprintf("volcAccountID: %s未找到客户信息", accountID))
				}
			}

			vkePodInfoResp := &DescribeResourceResponse{
				ResourceType:        vkePodInfo.GetResourceType(),
				ResourceMetadata:    resourceMetadata,
				ResourceNetworkInfo: resourceNetworkInfo,
				CustomerInfo:        customerInfo,
				OriginMsg:           vkePodInfo,
			}
			// 获取Grafana链接
			if url := vp.getGrafanaURL(ctx, vkePodInfo); url != nil {
				vkePodInfoResp.GrafanaLink = url
				vkePodInfoResp.ResourceMetadata = append(vkePodInfoResp.ResourceMetadata,
					CreateEntry("GrafanaLink", "监控链接", *url))
			}
			ehttp.DoResponse(ctx, c, vkePodInfoResp)
		} else {
			// ehttp.ErrorResp(ctx, c, errorcode.ErrCommonInternalError.WithArgs("vkeNodePoolInfo.Spec.Pod is nil"))
			return errorcode.ErrDataNotFound.WithArgs("vkePodInfo.Spec.Pod is nil") // --- IGNORE ---
		}

	} else {
		// ehttp.ErrorResp(ctx, c, errorcode.ErrCommonInternalError.WithArgs("vkeNodePoolInfo.Spec is nil"))
		return errorcode.ErrDataNotFound.WithArgs("vkePodInfo.Spec is nil") // --- IGNORE ---
	}

	return nil

}

const (
	vkePodPattern = `^c[a-z0-9]{20}:[^:]+:[^:]+$`
)

func (vp *VkePod) Match(ctx context.Context, inputInfo *describe_resource.DescribeResourceRequest) bool {
	if strings.TrimSpace(inputInfo.Resources.ResourceType) != "" {
		return strings.TrimSpace(inputInfo.Resources.ResourceType) == vp.GetResourceType()
	}
	for _, resource := range inputInfo.Resources.Instances {

		// 匹配格式: cc开头位字符:非空字符串:非空字符串
		regex := regexp.MustCompile(vkePodPattern)
		if regex.MatchString(resource) {
			parts := strings.Split(resource, ":")
			if len(parts) == 3 && parts[1] != "" && parts[2] != "" {
				return true
			}
		}

	}

	return false

}

func (vp *VkePod) getGrafanaURL(ctx context.Context, instance *VkePod) *string {
	productCategory, err := product_category.GetProductCategoryService().GetProductCategoryByResourceType(ctx, "vkePod")
	if err != nil || productCategory == nil || productCategory.GrafanaTemplate == nil {
		logger.CtxWarnf(ctx, "get grafana url failed: %v", err)
		return nil
	}

	data := map[string]string{
		"Region":       instance.Spec.Region,
		"AccountID":    fmt.Sprintf("%d", instance.Spec.AccountId),
		"VkeClusterID": instance.Spec.ClusterId,
		"Namespace":    instance.Spec.Namespace,
		"PodName":      instance.Spec.Pod.Name,
	}
	url, err := utils.RenderGrafanaTemplate(*productCategory.GrafanaTemplate, data)
	if err != nil {
		logger.CtxWarnf(ctx, "render grafana template failed: %v", err)
		return nil
	}
	return &url
}

func (vp *VkePod) GetResourcesDependency(ctx context.Context, c *app.RequestContext) error {
	// logger := utils.NewModuleLogger("GetVkeInstanceResources")
	params := &describe_resource.DescribeResourceRequest{}
	err := utils.JsonUnmarshal(c.Request.Body(), params)
	if err != nil {
		ehttp.DoResponse(ctx, c, errorcode.ErrRequestParamInvalid.WithArgs(err))
		return err
	}
	ins := params.Resources.Instances
	vkePodInfo, err := GetVkePodResource(ctx, ins)
	if err != nil {
		// ehttp.DoResponse(ctx, c, errorcode.ErrDataNotFound.WithArgs(err))
		return errorcode.ErrDataNotFound.WithArgs(err)
	}

	//根据pod的信息去查询关联关系
	if vkePodInfo.Spec != nil {
		if vkePodInfo.Spec.Pod != nil {
			result := DescribeResourceDependencyResponse{}
			if vkePodInfo.Spec.Pod.ObjectMeta.Name != "" {
				result.InstanceID = vkePodInfo.Spec.Pod.ObjectMeta.Name
			}

			//获取vkeInstance的信息，生成返回数据
			vkeCluster := &Product{}
			vkeCluster.ProductName = "VKE集群"
			vkeCluster.Header = []*Field{
				AddField("ResourceID", "资源ID"),
				AddField("ResourceName", "资源名称"),
				AddField("Region", "地域"),
				AddField("AccountID", "账号ID"),
				AddField("KubernetesVersion", "Kubernetes版本"),
				AddField("SecurityGroupIds", "安全组"),
				AddField("PodNetworkMode", "网络模型"),
			}
			vkeClusterInfo, err := GetVkeCLusterResource(ctx, []string{vkePodInfo.Spec.ClusterId})
			if err != nil {
				// logger.CtxWarn(ctx, "GetVkeCLusterResource error: %s", err)
				ehttp.ErrorResp(ctx, c, errorcode.ErrCommonInternalError.WithArgs(err))
				return err
			}
			vkeCluster.Items = []map[string]interface{}{
				{
					"ResourceID":        vkeClusterInfo.Spec.ClusterId,
					"ResourceName":      vkeClusterInfo.Spec.Name,
					"Region":            vkeClusterInfo.Spec.Region,
					"AccountID":         vkeClusterInfo.Spec.AccountId,
					"KubernetesVersion": vkeClusterInfo.Spec.KubernetesVersion,
					"SecurityGroupIds":  vkeClusterInfo.Spec.SecurityGroupIds,
					"PodNetworkMode":    vkeClusterInfo.Spec.PodNetworkMode,
				},
			}
			result.Products = append(result.Products, vkeCluster)

			//获取vkeNodePool的信息，生成返回数据
			vkeNodePool := &Product{}
			vkeNodePool.ProductName = "VKE节点池"
			vkeNodePool.Header = []*Field{
				AddField("ResourceID", "资源ID"),
				AddField("ResourceName", "资源名称"),
				AddField("AccountID", "账号ID"),
				AddField("ClusterID", "集群ID"),
				AddField("InstanceTypeIds", "节点规格"),
			}
			vkeNodePoolInfo, err := GetVkeNodePoolResource(ctx, []string{vkePodInfo.Spec.Node.Spec.NodePoolId})
			if err != nil {
				// logger.CtxWarn(ctx, "GetVkeNodePoolResource error: %s", err)
				// ehttp.ErrorResp(ctx, c, errorcode.ErrCommonInternalError.WithArgs(err))
				return errorcode.ErrCommonInternalError.WithArgs(err)
			}
			vkeNodePool.Items = []map[string]interface{}{
				{
					"ResourceID":      vkeNodePoolInfo.Spec.NodePoolId,
					"ResourceName":    vkeNodePoolInfo.Spec.Name,
					"AccountID":       vkeNodePoolInfo.Spec.AccountId,
					"ClusterID":       vkeNodePoolInfo.Spec.ClusterId,
					"InstanceTypeIds": strings.Join(vkeNodePoolInfo.Spec.InstanceTypeIds, ","),
				},
			}
			result.Products = append(result.Products, vkeNodePool)

			//获取vkeNode的信息，生成返回数据
			vkeNode := &Product{}
			vkeNode.ProductName = "VKE节点"
			vkeNode.Header = []*Field{
				AddField("ResourceID", "资源ID"),
				AddField("AccountID", "账号ID"),
				AddField("NodePoolID", "节点池ID"),
				AddField("ClusterID", "集群ID"),
				AddField("MachineID", "机器ID"),
				AddField("EcsID", "节点ECS ID"),
			}
			vkeNode.Items = []map[string]interface{}{
				{
					"ResourceID": vkePodInfo.Spec.Node.Spec.NodeId,
					"AccountID":  vkePodInfo.Spec.AccountId,
					"NodePoolID": vkePodInfo.Spec.Node.Spec.NodePoolId,
					"EcsID":      vkePodInfo.Spec.Node.Spec.InstanceId,
					"ClusterID":  vkePodInfo.Spec.Node.Spec.ClusterId,
				},
			}
			result.Products = append(result.Products, vkeNode)

			//获取Ecs的信息，生成返回数据
			ecs := &Product{}
			ecs.ProductName = "ECS"
			ecs.Header = []*Field{
				AddField("ResourceID", "资源ID"),
				AddField("AccountID", "账号ID"),
				AddField("Region", "地域"),
				AddField("Description", "描述"),
				AddField("VpcID", "VpcID"),
			}
			ecsInfo, err := GetEcsResource(ctx, vkePodInfo.Spec.Node.Spec.InstanceId)
			if err != nil {
				// logger.CtxWarn(ctx, "GetEcsResource error: %s", err)
				// ehttp.ErrorResp(ctx, c, errorcode.ErrCommonInternalError.WithArgs(err))
				return errorcode.ErrCommonInternalError.WithArgs(err)
			}
			ecs.Items = []map[string]interface{}{
				{
					"ResourceID":  AddValue(ecsInfo.Instance.Id),
					"AccountID":   AddValue(ecsInfo.Instance.AccountId),
					"Region":      AddValue(ecsInfo.Region),
					"Description": AddValue(ecsInfo.Instance.Description),
					"VpcID":       AddValue(ecsInfo.Instance.VpcId),
				},
			}
			result.Products = append(result.Products, ecs)

			ehttp.DoResponse(ctx, c, result)
		} else {
			// ehttp.ErrorResp(ctx, c, errorcode.ErrCommonInternalError.WithArgs("vkeNodePoolInfo.Spec.Pod is nil"))
			return errorcode.ErrCommonInternalError.WithArgs("vkePodInfo.Spec.Pod is nil")
		}

	} else {
		// ehttp.ErrorResp(ctx, c, errorcode.ErrCommonInternalError.WithArgs(" vkePodInfo.Spec is nil"))
		return errorcode.ErrCommonInternalError.WithArgs(" vkePodInfo.Spec is nil")

	}

	return nil
}
