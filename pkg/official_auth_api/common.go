package official_auth_api

const (
	ONLINE_HOST  = "http://volcengine-sop.byted.org"
	OFFLINE_HOST = "http://volcengine-sop-boe.byted.org"
)

type ResponseMetadata struct {
	RequestId string `json:"RequestId"`
	Action    string `json:"Action"`
	Service   string `json:"Service"`
	Error     *Error `json:"Error"`
	Version   string `json:"Version"`
	Region    string `json:"Region"`
}

type Error struct {
	Code    string
	Message string
}

type DiagnosticAuthorization struct {
	ID                         int                         `json:"ID"`
	AuthorizationID            string                      `json:"AuthorizationID"`
	CreateOrigin               string                      `json:"CreateOrigin"`
	TicketID                   string                      `json:"TicketID"`
	AccountID                  string                      `json:"AccountID"`
	ApplicationReason          string                      `json:"ApplicationReason"`
	ApplicationPolicy          []ApplicationPolicy         `json:"ApplicationPolicy"`
	AuthorizationTimeIntervals []AuthorizationTimeInterval `json:"AuthorizationTimeIntervals"`
	Role                       string                      `json:"Role"`
	ApplicantDuration          int                         `json:"ApplicantDuration"`
	ActualEndTime              int                         `json:"ActualEndTime"`
	ExpectedDuration           int                         `json:"ExpectedDuration"`
	ActualDuration             int                         `json:"ActualDuration"`
	ExpiredTime                int                         `json:"ExpiredTime"`
	StartTime                  int                         `json:"StartTime"`
	EndTime                    int                         `json:"EndTime"`
	AuthorizerType             int                         `json:"AuthorizerType"`
	Authorizer                 string                      `json:"Authorizer"`
	TerminateType              int                         `json:"TerminateType"`
	Terminator                 string                      `json:"Terminator"`
	Creator                    string                      `json:"Creator"`
	CreatedTime                int                         `json:"CreatedTime"`
	UpdatedTime                int                         `json:"UpdatedTime"`
	Status                     int                         `json:"Status"`
}

type ApplicationPolicy struct {
	Key           string          `json:"Key"`
	ContentParams []ContentParams `json:"ContentParams"`
}

type ContentParams struct {
	Key   string      `json:"Key"`
	Value interface{} `json:"Value"`
}

type AuthorizationTimeInterval struct {
	LeftInterval  int `json:"LeftInterval"`
	RightInterval int `json:"RightInterval"`
}

func GetStatus(i int) string {
	status := map[int]string{
		1: "待审批",
		2: "生效中",
		3: "拒绝",
		4: "结束",
		5: "过期",
	}
	return status[i]
}
