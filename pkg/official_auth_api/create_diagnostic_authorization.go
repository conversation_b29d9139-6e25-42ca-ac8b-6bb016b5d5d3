package official_auth_api

import (
	"code.byted.org/volcengine-support/cloud-sherlock/pkg/http_client"
	"code.byted.org/volcengine-support/cloud-sherlock/pkg/tcc"
	"code.byted.org/volcengine-support/cloud-sherlock/utils"
	"context"
	"encoding/json"
	"errors"
	"net/http"
)

const (
	CreateDiagnosticAuthorizationAction = "CreateDiagnosticAuthorization20210101"
	CreateDiagnosticAuthorizationPath   = "/inner/authorization"
)

type CreateDiagnosticAuthorizationRequest struct {
	TicketID          string               `json:"TicketID"`
	Email             string               `json:"Email"`
	CreateOrigin      string               `json:"CreateOrigin"`
	ApplicationReason string               `json:"ApplicationReason"`
	ApplicationPolicy []*ApplicationPolicy `json:"ApplicationPolicy"`
	ApplicantDuration int                  `json:"ApplicantDuration"` //单位：分钟
	ExpiredTime       int                  `json:"ExpiredTime"`       //秒级时间戳
	Creator           string               `json:"Creator"`
}

type CreateDiagnosticAuthorizationResponse struct {
	ResponseMetadata ResponseMetadata `json:"ResponseMetadata"`
	Result           struct {
		Data *CreateDiagnosticAuthorizationData `json:"Data"`
	} `json:"Result"`
}

type CreateDiagnosticAuthorizationData struct {
	ID              int    `json:"ID"`
	AuthorizationID string `json:"AuthorizationID"`
}

func CreateDiagnosticAuthorization(req *CreateDiagnosticAuthorizationRequest) (CreateDiagnosticAuthorizationResponse, error) {
	logger := utils.NewModuleLogger("official_auth.CreateDiagnosticAuthorization")
	logger.CtxInfo(context.Background(), "CreateDiagnosticAuthorization req:%v", req)
	ctx := context.Background()
	conf := tcc.GetServiceConfig()
	resp := CreateDiagnosticAuthorizationResponse{}
	env := conf.SSOEnv.Env
	//env := "boe"
	host := ""
	jsonBody, err := json.Marshal(req)
	if err != nil {
		logger.CtxError(ctx, "CreateDiagnosticAuthorization err:%v", err)
		return CreateDiagnosticAuthorizationResponse{}, err
	}
	headers := map[string]string{
		"Content-Type":     "application/json",
		"X-Employee-Email": req.Email,
	}
	if env == "boe" {
		host = OFFLINE_HOST
		headers["x-tt-env"] = "boe_common_train_05xw29"
	} else {
		host = ONLINE_HOST
	}

	params := http_client.RequestParams{
		Method:  http.MethodPost,
		Path:    CreateDiagnosticAuthorizationPath,
		Host:    host,
		Headers: headers,
		Body:    jsonBody,
		Query:   "Action=" + CreateDiagnosticAuthorizationAction + "&Service=SOP",
	}

	data, err := http_client.DoRequest(ctx, params)
	if err != nil {
		logger.CtxError(ctx, "CreateDiagnosticAuthorization err:%v", err)
		return CreateDiagnosticAuthorizationResponse{}, err
	}
	if data.StatusCode() != http.StatusOK {
		logger.CtxError(ctx, "CreateDiagnosticAuthorization err:%v", err)
		return CreateDiagnosticAuthorizationResponse{}, errors.New("CreateDiagnosticAuthorization failed")
	}
	if data.HasBodyBytes() {
		err := json.Unmarshal(data.Body(), &resp)
		if err != nil {
			logger.CtxError(ctx, "CreateDiagnosticAuthorization err:%v", err)
			return CreateDiagnosticAuthorizationResponse{}, err
		}
		return resp, nil
	} else {
		return CreateDiagnosticAuthorizationResponse{}, errors.New("CreateDiagnosticAuthorization failed")
	}

}
