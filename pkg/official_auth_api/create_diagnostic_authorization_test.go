package official_auth_api

import (
	"fmt"
	"testing"
)

func TestCreateDiagnosticAuthorization(t *testing.T) {
	req := &CreateDiagnosticAuthorizationRequest{
		TicketID:          "T-****************",
		AccountID:         "**********",
		CreateOrigin:      "工具平台",
		ApplicationReason: "测试",
		ApplicationPolicy: []ApplicationPolicy{
			{
				Key: "ecs_troubleshooting_01",
				ContentParams: []ContentParams{
					{
						Key:   "instance_id",
						Value: "aaaa",
					},
					{
						Key:   "sayhello",
						Value: "bbbb",
					},
				},
			},
		},
		ApplicantDuration: 20160,
		ExpiredTime:       **********,
		Creator:           "<EMAIL>",
	}
	resp, err := CreateDiagnosticAuthorization(req)
	if err != nil {
		t.Error(err)
	}
	fmt.Println(resp)
}
