package official_auth_api

import (
	"code.byted.org/volcengine-support/cloud-sherlock/pkg/http_client"
	"code.byted.org/volcengine-support/cloud-sherlock/pkg/tcc"
	"code.byted.org/volcengine-support/cloud-sherlock/utils"
	"context"
	"encoding/json"
	"errors"
	"net/http"
)

const (
	GetDiagnosticAuthorizationAction = "GetDiagnosticAuthorization20210101"
	GetDiagnosticAuthorizationPath   = "/inner/authorization"
)

type GetDiagnosticAuthorizationRequest struct {
	Email           string `json:"Email"`
	AuthorizationID string `json:"AuthorizationID"`
}

type GetDiagnosticAuthorizationResponse struct {
	ResponseMetadata ResponseMetadata `json:"ResponseMetadata"`
	Result           struct {
		Data DiagnosticAuthorization `json:"Data"`
	} `json:"Result"`
}

func GetDiagnosticAuthorization(req *GetDiagnosticAuthorizationRequest) (GetDiagnosticAuthorizationResponse, error) {
	logger := utils.NewModuleLogger("official_auth.GetDiagnosticAuthorization")
	ctx := context.Background()
	conf := tcc.GetServiceConfig()
	resp := GetDiagnosticAuthorizationResponse{}
	env := conf.SSOEnv.Env
	//env := "boe"
	host := ""
	body := struct {
		AuthorizationID string `json:"AuthorizationID"`
	}{
		AuthorizationID: req.AuthorizationID,
	}
	jsonBody, err := json.Marshal(body)
	if err != nil {
		logger.CtxError(ctx, "GetDiagnosticAuthorization err:%v", err)
		return GetDiagnosticAuthorizationResponse{}, err
	}
	headers := map[string]string{
		"Content-Type":     "application/json",
		"X-Employee-Email": req.Email,
	}
	if env == "boe" {
		host = OFFLINE_HOST
		headers["x-tt-env"] = "boe_common_train_05xw29"
	} else {
		host = ONLINE_HOST
	}

	params := http_client.RequestParams{
		Method:  http.MethodPost,
		Path:    GetDiagnosticAuthorizationPath,
		Host:    host,
		Headers: headers,
		Body:    jsonBody,
		Query:   "Action=" + GetDiagnosticAuthorizationAction + "&Service=SOP",
	}

	data, err := http_client.DoRequest(ctx, params)
	if err != nil {
		logger.CtxError(ctx, "GetDiagnosticAuthorization err:%v", err)
		return GetDiagnosticAuthorizationResponse{}, err
	}
	if data.StatusCode() != http.StatusOK {
		logger.CtxError(ctx, "GetDiagnosticAuthorization err:%v", err)
		return GetDiagnosticAuthorizationResponse{}, errors.New("GetDiagnosticAuthorization failed")
	}
	if data.HasBodyBytes() {
		err := json.Unmarshal(data.Body(), &resp)
		if err != nil {
			logger.CtxError(ctx, "GetDiagnosticAuthorization err:%v", err)
			return GetDiagnosticAuthorizationResponse{}, err
		}
		return resp, nil
	} else {
		return GetDiagnosticAuthorizationResponse{}, errors.New("GetDiagnosticAuthorization failed")
	}

}
