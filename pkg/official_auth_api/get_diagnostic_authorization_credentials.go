package official_auth_api

import (
	"code.byted.org/volcengine-support/cloud-sherlock/pkg/http_client"
	"code.byted.org/volcengine-support/cloud-sherlock/pkg/tcc"
	"code.byted.org/volcengine-support/cloud-sherlock/utils"
	"context"
	"encoding/json"
	"net/http"
)

const (
	GetDiagnosticAuthorizationCredentialsAction = "GetDiagnosticAuthorizationCredentials20210101"
	GetDiagnosticAuthorizationCredentialsPath   = "/inner/authorization"
)

type GetDiagnosticAuthorizationCredentialsRequest struct {
	Email           string `json:"Email"`
	AuthorizationID string `json:"AuthorizationID"`
}

type GetDiagnosticAuthorizationCredentialsResponse struct {
	ResponseMetadata ResponseMetadata `json:"ResponseMetadata"`
	Result           struct {
		Data DiagnosticAuthorization `json:"Data"`
	} `json:"Result"`
}

type GetDiagnosticAuthorizationCredentialsData struct {
	CurrentTime     string          `json:"CurrentTime"`
	ExpiredTime     string          `json:"ExpiredTime"`
	AccessKeyId     string          `json:"AccessKeyId"`
	SecretAccessKey string          `json:"SecretAccessKey"`
	SessionToken    string          `json:"SessionToken"`
	AssumedRoleUser AssumedRoleUser `json:"AssumedRoleUser"`
}

type AssumedRoleUser struct {
	Trn           string `json:"Trn"`
	AssumedRoleId string `json:"AssumedRoleId"`
}

func GetDiagnosticAuthorizationCredentials(req *GetDiagnosticAuthorizationCredentialsRequest) (GetDiagnosticAuthorizationCredentialsResponse, error) {
	logger := utils.NewModuleLogger("official_auth.GetDiagnosticAuthorizationCredentials")
	ctx := context.Background()
	conf := tcc.GetServiceConfig()
	resp := GetDiagnosticAuthorizationCredentialsResponse{}
	env := conf.SSOEnv.Env
	//env := "boe"
	host := ""
	body := struct {
		AuthorizationID string `json:"AuthorizationID"`
	}{
		AuthorizationID: req.AuthorizationID,
	}
	jsonBody, err := json.Marshal(body)
	if err != nil {
		logger.CtxError(ctx, "GetDiagnosticAuthorizationCredentials err:%v", err)
		return GetDiagnosticAuthorizationCredentialsResponse{}, err
	}
	headers := map[string]string{
		"Content-Type":     "application/json",
		"X-Employee-Email": req.Email,
	}
	if env == "boe" {
		host = OFFLINE_HOST
		headers["x-tt-env"] = "boe_common_train_05xw29"
	} else {
		host = ONLINE_HOST
	}

	params := http_client.RequestParams{
		Method:  http.MethodPost,
		Path:    GetDiagnosticAuthorizationCredentialsPath,
		Host:    host,
		Headers: headers,
		Body:    jsonBody,
		Query:   "Action=" + GetDiagnosticAuthorizationCredentialsAction + "&Service=SOP",
	}

	data, err := http_client.DoRequest(ctx, params)
	if err != nil {
		logger.CtxError(ctx, "GetDiagnosticAuthorizationCredentials err:%v", err)
		return GetDiagnosticAuthorizationCredentialsResponse{}, err
	}
	////if data.StatusCode() != http.StatusOK {
	////	logger.CtxError(ctx, "GetDiagnosticAuthorizationCredentials err:%v", err)
	////	return GetDiagnosticAuthorizationCredentialsResponse{}, errors.New("GetDiagnosticAuthorizationCredentials failed error")
	////}
	//if data.HasBodyBytes() {
	//
	//} else {
	//	return GetDiagnosticAuthorizationCredentialsResponse{}, errors.New("GetDiagnosticAuthorizationCredentials failed")
	//}
	err = json.Unmarshal(data.Body(), &resp)
	if err != nil {
		logger.CtxError(ctx, "GetDiagnosticAuthorizationCredentials err:%v", err)
		return GetDiagnosticAuthorizationCredentialsResponse{}, err
	}
	return resp, nil

}
