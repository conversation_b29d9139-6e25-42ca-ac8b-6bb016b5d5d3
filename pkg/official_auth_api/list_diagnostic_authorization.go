package official_auth_api

import (
	"code.byted.org/volcengine-support/cloud-sherlock/pkg/http_client"
	"code.byted.org/volcengine-support/cloud-sherlock/pkg/tcc"
	"code.byted.org/volcengine-support/cloud-sherlock/utils"
	"context"
	"encoding/json"
	"errors"
	"net/http"
)

const (
	ListDiagnosticAuthorizationAction = "ListDiagnosticAuthorization20210101"
	ListDiagnosticAuthorizationPath   = "/inner/authorization"
)

type ListDiagnosticAuthorizationRequest struct {
	Email                 string   `json:"Email"`
	AuthorizationID       []string `json:"AuthorizationID"`
	AccountID             string   `json:"AccountID"`
	Status                int      `json:"Status"`
	TicketID              string   `json:"TicketID"`
	CreateOrigin          string   `json:"CreateOrigin"`
	Applicant             string   `json:"Applicant"`
	CreatedTimeLowerBound int      `json:"CreatedTimeLowerBound"`
	CreatedTimeUpperBound int      `json:"CreatedTimeUpperBound"`
	UpdatedTimeLowerBound int      `json:"UpdatedTimeLowerBound"`
	UpdatedTimeUpperBound int      `json:"UpdatedTimeUpperBound"`
	Limit                 int      `json:"Limit"`
	Offset                int      `json:"Offset"`
}

type ListDiagnosticAuthorizationResponse struct {
	ResponseMetadata ResponseMetadata `json:"ResponseMetadata"`
	Result           struct {
		Data   []*DiagnosticAuthorization `json:"Data"`
		Limit  int                        `json:"Limit"`
		Offset int                        `json:"Offset"`
		Total  int                        `json:"Total"`
	} `json:"Result"`
}

func ListDiagnosticAuthorization(req *ListDiagnosticAuthorizationRequest) (ListDiagnosticAuthorizationResponse, error) {
	logger := utils.NewModuleLogger("official_auth.ListDiagnosticAuthorization")
	ctx := context.Background()
	conf := tcc.GetServiceConfig()
	resp := ListDiagnosticAuthorizationResponse{}
	env := conf.SSOEnv.Env
	//env := "boe"
	host := ""
	jsonBody, err := json.Marshal(req)
	if err != nil {
		logger.CtxError(ctx, "ListDiagnosticAuthorization err:%v", err)
		return ListDiagnosticAuthorizationResponse{}, err
	}
	headers := map[string]string{
		"Content-Type":     "application/json",
		"X-Employee-Email": req.Email,
	}
	if env == "boe" {
		host = OFFLINE_HOST
		headers["x-tt-env"] = "boe_common_train_05xw29"
	} else {
		host = ONLINE_HOST
	}

	params := http_client.RequestParams{
		Method:  http.MethodPost,
		Path:    ListDiagnosticAuthorizationPath,
		Host:    host,
		Headers: headers,
		Body:    jsonBody,
		Query:   "Action=" + ListDiagnosticAuthorizationAction + "&Service=SOP",
	}

	data, err := http_client.DoRequest(ctx, params)
	if err != nil {
		logger.CtxError(ctx, "ListDiagnosticAuthorization err:%v", err)
		return ListDiagnosticAuthorizationResponse{}, err
	}
	if data.StatusCode() != http.StatusOK {
		logger.CtxError(ctx, "ListDiagnosticAuthorization err:%v", err)
		return ListDiagnosticAuthorizationResponse{}, errors.New("ListDiagnosticAuthorization failed")
	}
	if data.HasBodyBytes() {
		err := json.Unmarshal(data.Body(), &resp)
		if err != nil {
			logger.CtxError(ctx, "ListDiagnosticAuthorization err:%v", err)
			return ListDiagnosticAuthorizationResponse{}, err
		}
		return resp, nil
	} else {
		return ListDiagnosticAuthorizationResponse{}, errors.New("ListDiagnosticAuthorization failed")
	}

}
