package official_auth_api

import (
	"code.byted.org/kv/redis-v6"
	"code.byted.org/volcengine-support/cloud-sherlock/biz/dal"
	"code.byted.org/volcengine-support/cloud-sherlock/gen/gorm/model"
	"code.byted.org/volcengine-support/cloud-sherlock/pkg/rds_redis"
	"code.byted.org/volcengine-support/cloud-sherlock/utils"
	"context"
	"errors"
	"github.com/samber/lo"
	"gorm.io/gorm"
	"strconv"
	"time"
)

const (
	SYNC_LAST_TIME_KEY = "sync_last_time"
	SYNC_LOCK_KEY      = "sync_lock"
)

func SyncAuthData() (bool, error) {

	syncAuthLogger := utils.NewModuleLogger("SyncAuthData")
	syncAuthLogger.CtxInfo(context.Background(), "开始同步数据")
	//1.获取分布式锁，拿到了锁再进行同步，否则跳过
	isSure, err := rds_redis.GetLock(SYNC_LOCK_KEY, 2*time.Minute)
	if err != nil || isSure == false {
		syncAuthLogger.CtxInfo(context.Background(), "获取分布式锁失败")
		return false, err
	}
	// 6. 释放锁
	defer rds_redis.DeleteLock(SYNC_LOCK_KEY)

	// 2. 获取上一次同步的时间
	var lastTime int64
	res, errRedis := rds_redis.RedisClient.Client.Get(SYNC_LAST_TIME_KEY).Result()
	if errRedis != nil {
		if errors.Is(errRedis, redis.Nil) {
			// 不存在时，应该从数据库中的UpdatedTime字段中获取最晚的时间
			syncAuthLogger.CtxInfo(context.Background(), "redis中不存在上一次同步的时间,从数据库中获取updated_time字段中获取最晚的时间")
			t, errMysql := dal.FindDiagnoseAuthLastUpdatedTime(context.Background())
			if errMysql != nil && errors.Is(errMysql, gorm.ErrRecordNotFound) {
				t = 0
				errMysql = nil
			}
			if errMysql != nil {
				syncAuthLogger.CtxInfo(context.Background(), "从数据库中获取updated_time字段中获取最晚的时间失败")
				return false, err
			}
			lastTime = int64(t)
		}
	} else {
		// 将上次的时间戳的string转换为int64
		lastTime, err = strconv.ParseInt(res, 10, 64)
		if err != nil {
			syncAuthLogger.CtxInfo(context.Background(), "redis中存在上一次同步的时间,但是转换为int64失败")
			return false, err
		}
	}

	// 3. 同步数据,从官网获取数据
	offset := 0
	limit := 100
	diagnoseAuths := make([]*model.DiagnoseAuthorization, 0)
	for {
		req := &ListDiagnosticAuthorizationRequest{
			Email:                 "<EMAIL>",
			UpdatedTimeLowerBound: int(lastTime),
			Limit:                 limit,
			Offset:                offset,
		}
		resp, err := ListDiagnosticAuthorization(req)
		if err != nil {
			syncAuthLogger.CtxInfo(context.Background(), "从官网获取数据失败")
			return false, err
		}
		diagnoseAuths = append(diagnoseAuths, DataToAuth(resp.Result.Data)...)
		if offset >= resp.Result.Total {
			break
		} else {
			offset += limit
		}
	}

	// 4. 保存数据到数据库
	err = dal.UpdatesDiagnoseAuthsWithTransaction(context.Background(), diagnoseAuths)
	if err != nil {
		syncAuthLogger.CtxInfo(context.Background(), "保存数据到数据库失败")
		return false, err
	}

	// 5. redis更新上一次同步的时间
	now := time.Now().Unix()
	err = rds_redis.RedisClient.Client.Set(SYNC_LAST_TIME_KEY, now, 0).Err()
	if err != nil {
		syncAuthLogger.CtxInfo(context.Background(), "redis更新上一次同步的时间失败")
		return false, err
	}

	return true, nil
}

func DataToAuth(datas []*DiagnosticAuthorization) []*model.DiagnoseAuthorization {
	if len(datas) == 0 {
		return nil
	}
	auths := make([]*model.DiagnoseAuthorization, 0)
	for _, data := range datas {
		auths = append(auths, &model.DiagnoseAuthorization{
			AuthorizationID: lo.ToPtr(data.AuthorizationID),
			Status:          lo.ToPtr(int32(data.Status)),
			StartTime:       lo.ToPtr(int32(data.StartTime)),
			EndTime:         lo.ToPtr(int32(data.EndTime)),
			ExpiredTime:     lo.ToPtr(int32(data.ExpiredTime)),
			UpdateTime:      lo.ToPtr(int32(time.Now().Unix())),
		})
	}
	return auths
}
