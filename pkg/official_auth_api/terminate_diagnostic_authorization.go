package official_auth_api

import (
	"code.byted.org/volcengine-support/cloud-sherlock/pkg/http_client"
	"code.byted.org/volcengine-support/cloud-sherlock/pkg/tcc"
	"code.byted.org/volcengine-support/cloud-sherlock/utils"
	"context"
	"encoding/json"
	"errors"
	"net/http"
)

const (
	TerminateDiagnosticAuthorizationAction = "TerminateDiagnosticAuthorization20210101"
	TerminateDiagnosticAuthorizationPath   = "/inner/authorization"
)

type TerminateDiagnosticAuthorizationRequest struct {
	Email           string `json:"Email"`
	TerminateType   int    `json:"TerminateType"`
	AuthorizationID string `json:"AuthorizationID"`
	Terminator      string `json:"Terminator"`
	AccountID       string `json:"AccountID"`
}

type TerminateDiagnosticAuthorizationResponse struct {
	ResponseMetadata ResponseMetadata `json:"ResponseMetadata"`
}

func TerminateDiagnosticAuthorization(req *TerminateDiagnosticAuthorizationRequest) (TerminateDiagnosticAuthorizationResponse, error) {
	logger := utils.NewModuleLogger("official_auth.TerminateDiagnosticAuthorization")
	ctx := context.Background()
	conf := tcc.GetServiceConfig()
	resp := TerminateDiagnosticAuthorizationResponse{}
	env := conf.SSOEnv.Env
	//env := "boe"
	host := ""
	body := struct {
		AuthorizationID string `json:"AuthorizationID"`
	}{
		AuthorizationID: req.AuthorizationID,
	}
	jsonBody, err := json.Marshal(body)
	if err != nil {
		logger.CtxError(ctx, "TerminateDiagnosticAuthorization err:%v", err)
		return TerminateDiagnosticAuthorizationResponse{}, err
	}
	headers := map[string]string{
		"Content-Type":     "application/json",
		"X-Employee-Email": req.Email,
	}
	if env == "boe" {
		host = OFFLINE_HOST
		headers["x-tt-env"] = "boe_common_train_05xw29"
	} else {
		host = ONLINE_HOST
	}

	params := http_client.RequestParams{
		Method:  http.MethodPost,
		Path:    TerminateDiagnosticAuthorizationPath,
		Host:    host,
		Headers: headers,
		Body:    jsonBody,
		Query:   "Action=" + TerminateDiagnosticAuthorizationAction + "&Service=SOP",
	}

	data, err := http_client.DoRequest(ctx, params)
	if err != nil {
		logger.CtxError(ctx, "TerminateDiagnosticAuthorization err:%v", err)
		return TerminateDiagnosticAuthorizationResponse{}, err
	}
	if data.StatusCode() != http.StatusOK {
		logger.CtxError(ctx, "TerminateDiagnosticAuthorization err:%v", err)
		return TerminateDiagnosticAuthorizationResponse{}, errors.New("TerminateDiagnosticAuthorization failed")
	}
	if data.HasBodyBytes() {
		err := json.Unmarshal(data.Body(), &resp)
		if err != nil {
			logger.CtxError(ctx, "TerminateDiagnosticAuthorization err:%v", err)
			return TerminateDiagnosticAuthorizationResponse{}, err
		}
		return resp, nil
	} else {
		return TerminateDiagnosticAuthorizationResponse{}, errors.New("TerminateDiagnosticAuthorization failed")
	}

}
