package official_auth_sdk

import (
	"code.byted.org/eps-platform/volcengine-innersdk-golang/service/sop"
	"code.byted.org/eps-platform/volcengine-innersdk-golang/volcengine"
	"code.byted.org/eps-platform/volcengine-innersdk-golang/volcengine/credentials"
	"code.byted.org/eps-platform/volcengine-innersdk-golang/volcengine/session"
	"code.byted.org/volcengine-support/cloud-sherlock/pkg/tcc"
	"code.byted.org/volcengine-support/cloud-sherlock/utils"
	"context"
	"sync"
)

const (
	ONLINE_HOST = "sop.innerapi.commonpcs.com"
	BOE_HOST    = "boe.innerapi.commonpcs.com"
	STABLE_HOST = "stable.innerapi.commonpcs.com"
)

var (
	client *sop.SOP
	once   sync.Once
)

func GetClient() *sop.SOP {
	logger := utils.NewModuleLogger("official_auth.CreateDiagnosticAuthorization")
	logger.CtxInfo(context.Background(), "GetClient")
	once.Do(func() {
		ak := ""
		sk := ""
		env := ""
		endPoint := ""
		region := "cn-beijing"
		conf := tcc.GetServiceConfig()
		if conf != nil {
			ak = conf.InnerTopConfig.Ak
			sk = conf.InnerTopConfig.Sk
			env = conf.SSOEnv.Env
		}
		if env == "boe" {
			endPoint = BOE_HOST
		} else {
			endPoint = ONLINE_HOST
		}
		config := volcengine.NewConfig().WithCredentials(
			credentials.NewStaticCredentials(ak, sk, "")).
			WithRegion(region).WithEndpoint(endPoint)
		sess, err := session.NewSession(config)
		if err != nil {
			logger.CtxError(context.Background(), "Init session err", err)
			panic(err)
		}
		client = sop.New(sess)
	})
	return client
}

type ApplicationPolicy struct {
	Key           string           `json:"Key"`
	ContentParams []*ContentParams `json:"ContentParams"`
}

type ContentParams struct {
	Key   string      `json:"Key"`
	Value interface{} `json:"Value"`
}
