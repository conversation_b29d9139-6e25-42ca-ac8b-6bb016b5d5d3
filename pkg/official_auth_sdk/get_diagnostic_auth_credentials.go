package official_auth_sdk

import (
	"code.byted.org/eps-platform/volcengine-innersdk-golang/service/sop"
)

func GetDiagnosticAuthCredentials(authID string) (*sop.GetDiagnosticAuthorizationCredentialsOutput, error) {
	client := GetClient()
	resp, err := client.GetDiagnosticAuthorizationCredentials(&sop.GetDiagnosticAuthorizationCredentialsInput{
		AuthorizationID: &authID,
	})
	if err != nil {
		return nil, err
	}
	return resp, nil
}
