package paas

type CreateDiagnoseTaskRequest struct {
	TargetType string   `json:"TargetType"`
	Region     string   `json:"Region"`
	Identities []string `json:"Identities"`
	Scenario   string   `json:"Scenario"`
	OrderId    string   `json:"OrderId"`
}

type PaaSRequest struct {
	Action      string      `json:"Action"`
	Version     string      `json:"Version"`
	Host        string      `json:"Host"`
	RequestBody interface{} `json:"RequestBody"`
}
type CreateDiagnoseTaskResponse struct {
	ResponseMetadata ResponseMetadata `json:"ResponseMetadata"`
	Result           struct {
		Id int64 `json:"Id"`
	} `json:"Result"`
}

type ModelObject struct {
	AccountID         int64  `json:"AccountId"`
	ID                string `json:"Id"`
	KubernetesVersion string `json:"KubernetesVersion"`
	Name              string `json:"Name"`
	Region            string `json:"Region"`
}

type ListDiagnoseTasksResponse struct {
	ResponseMetadata ResponseMetadata `json:"ResponseMetadata"`
	Result           Result           `json:"Result"`
}

// Result 定义了主要的业务数据结果
type Result struct {
	Data  []DataItem `json:"Data"`
	Total int        `json:"Total"`
}

// DataItem 定义了 Data 数组中每个元素的结构，代表一个诊断任务的数据项
type DataItem struct {
	Id         int64         `json:"Id"`
	TaskStatus string        `json:"TaskStatus"`
	Error      string        `json:"Error"`
	Results    []PhaseResult `json:"Results"`
}

// PhaseResult 定义了阶段性结果的结构，Results 数组中的元素
type PhaseResult struct {
	Phase     string       `json:"Phase"`
	StartTime string       `json:"StartTime"`
	EndTime   string       `json:"EndTime"`
	Results   []RuleResult `json:"Results"`
	Summary   Summary      `json:"Summary"`
	Target    Target       `json:"Target"`
}

// RuleResult 定义了规则检查结果的结构，Results 数组中的元素
type RuleResult struct {
	RuleId string `json:"RuleId"`
	Phase  string `json:"Phase"`
	Result string `json:"Result"`
	Detail string `json:"Detail"`
	Error  string `json:"Error"`
	Failed Failed `json:"Failed"`
	Rule   Rule   `json:"Rule"`
}

// Failed 定义了失败信息的结构
type Failed struct {
	Reason     string `json:"Reason"`
	Affection  string `json:"Affection"`
	Suggestion string `json:"Suggestion"`
}

// Rule 定义了规则的结构
type Rule struct {
	Description  string `json:"Description"`
	Name         string `json:"Name"`
	Group        string `json:"Group"`
	SubGroup     string `json:"SubGroup"`
	TargetModule string `json:"TargetModule"`
}

// Summary 定义了汇总信息的结构
type Summary struct {
	Total     int `json:"Total"`
	Succeeded int `json:"Succeeded"`
	Warning   int `json:"Warning"`
	Failed    int `json:"Failed"`
	Error     int `json:"Error"`
	Running   int `json:"Running"`
}

// Target 定义了目标对象的结构
type Target struct {
	Id            int    `json:"Id"`
	ModelObjectId string `json:"ModelObjectId"`
	Data          string `json:"Data"`
	BatchNum      int    `json:"BatchNum"`
	JobId         int    `json:"JobId"`
	ModelName     string `json:"ModelName"`
}
type ResponseMetadata struct {
	RequestId string `json:"RequestId"`
	Action    string `json:"Action"`
	Version   string `json:"Version"`
	Service   string `json:"Service"`
	Region    string `json:"Region"`
}

type ListDiagnoseTasksRequest struct {
	Ids []int64 `json:"Ids"`
}
