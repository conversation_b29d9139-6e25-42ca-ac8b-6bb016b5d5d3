package tcc

import (
	"code.byted.org/gopkg/logs/v2"
	"context"
)

var (
	hiagent = "agent-api-key"
)

type HiAgentConfig struct {
	Intention       string `json:"intention"`
	Diagnose        string `json:"diagnose"`
	Summary         string `json:"summary"`
	TicketIntention string `json:"ticket_intention"`
	BaseUrl         string `json:"base_url"`
	StageUrl        string `json:"stage_url"`
}

var hiAgentConfigGetter = NewGetter(hiagent, &HiAgentConfig{})

func GetHiagentConfig(ctx context.Context) (*HiAgentConfig, error) {
	res, err := hiAgentConfigGetter(ctx)
	if err != nil {
		logs.CtxError(ctx, "[tcc/GetHiAgentConfig] err:%v", err)
		return nil, err
	}
	return res.(*HiAgentConfig), nil
}
