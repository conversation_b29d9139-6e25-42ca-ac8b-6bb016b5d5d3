package tcc

import (
	"fmt"

	"code.byted.org/gopkg/logs/v2"
	"gopkg.in/yaml.v3"
)

type Service struct {
	PSM  string `yaml:"PSM"`
	Host string `yaml:"Host"`
}

type Config struct {
	Service         Service          `yaml:"Service"`
	CloudSherlockDB *MysqlConfig     `yaml:"CloudSherlockDB"`
	LdiDBReadOnly   *MysqlConfig     `yaml:"LdiDBReadOnly"`
	Permission      *Permission      `yaml:"Permission"`
	ByteFlowConfig  *ByteFlowConfig  `yaml:"ByteFlow"`
	Notify          *NotifyConfig    `yaml:"Notify"`
	EcsAccount      *EcsAccount      `yaml:"EcsAccount"`
	EcsWorkerConfig *EcsWorkerConfig `yaml:"EcsWorkerConfig"`
	CMDBConfig      *CMDBConfig      `yaml:"CMDBConfig"`
	PaaSConfig      *PaaSConfig      `yaml:"PaaSConfig"`
	NetworkConfig   *NetworkConfig   `yaml:"NetworkConfig"`
	CORSConfig      *CORSConfig      `yaml:"CORSConfig"`
	// redis 配置
	CloudSherlockRedis *RedisConfig `yaml:"CloudSherlockRedis"`
	// sso 配置
	SSOEnv         *SSOconfig      `yaml:"SSOEnv"`
	InnerTopConfig *InnerTopConfig `yaml:"InnerTopConfig"`

	//运营页面权限白名单
	OperationPagePermission []string `yaml:"OperationPagePermission"`
}

type EcsWorkerConfig struct {
	GetTokenUrl string `yaml:"GetTokenUrl"`
	TokenID     string `yaml:"TokenID"`
}

type InnerTopConfig struct {
	Ak string `yaml:"Ak"`
	Sk string `yaml:"Sk"`
}
type MysqlConfig struct {
	PSM    string `yaml:"PSM"`
	DBName string `yaml:"DBName"`
	Host   string `yaml:"Host"`
}

type NetworkConfig struct {
	AK   string `yaml:"AK"`
	SK   string `yaml:"SK"`
	Host string `yaml:"Host"`
}

// redis 在 tcc 保存的配置信息
type RedisConfig struct {
	// redis PSM
	PSM string `yaml:"PSM"`
}

// sso 在 tcc 保存的配置信息
type SSOconfig struct {
	Env string `yaml:"Env"`
}

type Permission struct {
	AppID int64 `yaml:"AppID"`
}

type LarkConfig struct {
	AppID  string `yaml:"AppID"`
	Secret string `yaml:"Secret"`
}

type VolcanoTicketConfig struct {
	Host                 string `yaml:"Host"`
	AppKey               string `yaml:"AppKey"`
	AppSecret            string `yaml:"AppSecret"`
	TicketDetailTemplate string `yaml:"TicketDetailTemplate"`
	CreateOrigin         string `yaml:"CreateOrigin"`
}

type HostCommonConfig struct {
	Host    string            `yaml:"Host"`
	PathMap map[string]string `yaml:"PathMap"`
	Token   string            `yaml:"Token"`
	Env     string            `yaml:"Env"`
}

type NotifyConfig struct {
	BoeChatGroup       string            `yaml:"BoeChatGroup"`
	AlarmChatGroup     string            `yaml:"AlarmChatGroup"`
	PreNotifyGroup     string            `yaml:"PreNotifyGroup"`
	EscapeCharacterMap map[string]string `yaml:"EscapeCharacterMap"`
}

type ByteFlowConfig struct {
	AppName       string `yaml:"AppName"`
	AppToken      string `yaml:"AppToken"`
	ServerURL     string `yaml:"ServerURL"`
	RocketCluster string `yaml:"RocketCluster"`
	RocketTopic   string `yaml:"RocketTopic"`
	SaSecret      string `yaml:"SaSecret"`
}

type ServiceAccount struct {
	Secret string `yaml:"Secret"`
}

type EcsAccount struct {
	AK string `yaml:"AK"`
	SK string `yaml:"SK"`
}

type CMDBConfig struct {
	AK          string `yaml:"AK"`
	SK          string `yaml:"SK"`
	ServiceCode string `yaml:"ServiceCode"`
	Host        string `yaml:"Host"`
}

type PaaSConfig struct {
	AK   string `yaml:"AK"`
	SK   string `yaml:"SK"`
	Host string `yaml:"Host"`
}

type CORSConfig struct {
	AllowOrigins []string `yaml:"AllowOrigins"`
	AllowHeaders []string `yaml:"AllowHeaders"`
}

var (
	serviceConfigKey = "config"
)
var serviceConfig *Config

func InitServiceConfig() {
	err := AddListener(serviceConfigKey, func(value string, err error) {
		if err != nil {
			logs.Error("[tcc/InitServiceConfig] tcc refresh config err:%+v", err)
			return
		}

		output := &Config{}
		err = yaml.Unmarshal([]byte(value), &output)
		if err != nil {
			logs.Error("[tcc/InitServiceConfig] conf yaml unmarshal err:%+v", err)
			return
		}

		serviceConfig = output
	})
	if err != nil || serviceConfig == nil {
		panic(fmt.Sprintf("Init service config error:%+v", err))
	}
}

func GetServiceConfig() *Config {
	return serviceConfig
}
