package ticker

import (
	"code.byted.org/volcengine-support/cloud-sherlock/pkg/official_auth_sdk"
	"code.byted.org/volcengine-support/cloud-sherlock/utils"
	"context"
	"time"
)

const (
	INTERVAL = 10 * time.Second
)

func Init() {
	go TickerSyncOfficialData()
}

func TickerSyncOfficialData() {
	TickerSyncOfficialDataLogger := utils.NewModuleLogger("TickerSyncOfficialData")
	TickerSyncOfficialDataLogger.CtxInfo(context.Background(), "从官网同步数据的定时器开启")

	ticker := time.NewTicker(INTERVAL)
	defer ticker.Stop()

	for {
		<-ticker.C
		isSure, err := official_auth_sdk.SyncAuthData()
		if err != nil || isSure == false {
			TickerSyncOfficialDataLogger.CtxInfo(context.Background(), "从官网同步数据失败")
			TickerSyncOfficialDataLogger.CtxError(context.Background(), "err: %v", err)
			continue
		}
	}
}
