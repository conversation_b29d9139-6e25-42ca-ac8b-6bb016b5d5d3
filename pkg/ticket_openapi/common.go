package ticket_openapi

const (
	OFFLINE_HOST = "http://volcengine-ticket-boe.byted.org"
	ONLINE_HOST  = "http://volcengine-ticket.byted.org"
)

type ResponseMetadata struct {
	RequestId string `json:"RequestId"`
	Action    string `json:"Action"`
}

type Ticket struct {
	CurrentFlowContent CurrentFlowContent `json:"CurrentFlowContent"`
}

type CurrentFlowContent struct {
	State            string   `json:"State"`
	CurrentHandler   string   `json:"CurrentHandler"`
	HandleList       []string `json:"HandleList"`
	CollaboratorList []string `json:"CollaboratorList"`
}
