package ticket_openapi

import (
	token_auth "code.byted.org/videoarch/cloud_gopkg/api_center/app"
	"code.byted.org/volcengine-support/cloud-sherlock/pkg/http_client"
	"code.byted.org/volcengine-support/cloud-sherlock/pkg/tcc"
	"code.byted.org/volcengine-support/cloud-sherlock/utils"
	"context"
	"encoding/json"
	"errors"
	"net/http"
	"time"
)

const (
	GetTicketSensitiveInfoAction = "GetTicketSensitiveInfo"
	GetTicketSensitiveInfoPath   = "/open/api/ticket"
)

type GetTicketSensitiveInfoRequest struct {
	TicketID string `json:"TicketID"`
}

type GetTicketSensitiveInfoResponse struct {
	ResponseMetadata ResponseMetadata `json:"ResponseMetadata"`
	Result           struct {
		Data Ticket `json:"Data"`
	} `json:"Result"`
}

func GetTicketSensitiveInfo(req *GetTicketSensitiveInfoRequest) (GetTicketSensitiveInfoResponse, error) {
	logger := utils.NewModuleLogger("ticket_openapi.GetTicketSensitiveInfo")
	ctx := context.Background()
	conf := tcc.GetServiceConfig()
	resp := GetTicketSensitiveInfoResponse{}
	tokenClient := &token_auth.Client{}
	signtoken := ""
	env := conf.SSOEnv.Env
	//env := "online"
	host := ""
	body := struct {
		TicketID string `json:"TicketID"`
	}{
		TicketID: req.TicketID,
	}
	jsonBody, err := json.Marshal(body)
	if err != nil {
		logger.CtxError(ctx, "GetDiagnosticAuthorization err:%v", err)
		return GetTicketSensitiveInfoResponse{}, err
	}

	if env == "boe" {
		host = OFFLINE_HOST
		tokenClient, _ = token_auth.NewClient("ticket_assignment_system", "bcEkcwCeMvAy7WVzKTR8pr3w3+wwf61Q7eGRYyA3tn8", 1*time.Minute)
		signtoken, _ = tokenClient.GenerateToken("")
	} else {
		host = ONLINE_HOST
		tokenClient, _ = token_auth.NewClient("ticket_assignment_system", "bcEkcwCeMvAy7WVzKTR8pr3w3+wwf61Q7eGRYyA3tn8", 1*time.Minute)
		signtoken, _ = tokenClient.GenerateToken("")
	}

	headers := map[string]string{
		"Content-Type":          "application/json",
		"X-Authorization-Token": signtoken,
	}

	params := http_client.RequestParams{
		Method:  http.MethodPost,
		Path:    GetTicketSensitiveInfoPath,
		Host:    host,
		Headers: headers,
		Body:    jsonBody,
		Query:   "Action=" + GetTicketSensitiveInfoAction + "&Version=2021-01-01",
	}

	data, err := http_client.DoRequest(ctx, params)
	if err != nil {
		logger.CtxError(ctx, "GetDiagnosticAuthorization err:%v", err)
		return GetTicketSensitiveInfoResponse{}, err
	}
	if data.StatusCode() != http.StatusOK {
		logger.CtxError(ctx, "GetDiagnosticAuthorization err:%v", err)
		return GetTicketSensitiveInfoResponse{}, errors.New("GetDiagnosticAuthorization failed")
	}
	if data.HasBodyBytes() {
		err := json.Unmarshal(data.Body(), &resp)
		if err != nil {
			logger.CtxError(ctx, "GetDiagnosticAuthorization err:%v", err)
			return GetTicketSensitiveInfoResponse{}, err
		}
		return resp, nil
	} else {
		return GetTicketSensitiveInfoResponse{}, errors.New("GetDiagnosticAuthorization failed")
	}

}
