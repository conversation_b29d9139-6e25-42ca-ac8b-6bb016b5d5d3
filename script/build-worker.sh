#!/bin/bash

RUN_NAME="volcengine.support.cloud_sherlock_worker"

mkdir -p output/bin output/conf
cp script/bootstrap_worker.sh output/bootstrap.sh 2>/dev/null
chmod +x output/bootstrap.sh
cp script/bootstrap_worker.sh output/bootstrap_staging.sh
chmod +x output/bootstrap_staging.sh
find conf/ -type f ! -name "*_local.*" | xargs -I{} cp {} output/conf/

go build -o output/bin/${RUN_NAME} cmd/worker/worker.go 
