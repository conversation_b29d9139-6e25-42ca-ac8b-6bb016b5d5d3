package utils

import (
	"encoding/json"
	"strings"
	"time"
)

func StringSplit(s, sep string) []string {
	if s == "" {
		return []string{}
	}
	return strings.Split(s, sep)
}

func MakeFuzz(s string) string {
	return "%" + s + "%"
}

func StringToTime(timeStr, layout, timezone string) (time.Time, error) {
	var (
		loc *time.Location
		err error
	)

	if timezone == "" {
		loc = time.Local // 默认使用本地时区
	} else {
		loc, err = time.LoadLocation(timezone)
		if err != nil {
			return time.Time{}, err
		}
	}

	// 使用 time.ParseInLocation 解析时间字符串
	t, err := time.ParseInLocation(layout, timeStr, loc)
	if err != nil {
		return time.Time{}, err
	}
	return t, nil
}

func UpdateIdsArray(idsStr *string, newID int64) (string, error) {
	var idsArray []int64
	if idsStr != nil {
		err := json.Unmarshal([]byte(*idsStr), &idsArray)
		if err != nil {
			return "", err
		}
	}
	idsArray = append(idsArray, newID)
	idsArrayStr, err := json.Marshal(idsArray)
	if err != nil {
		return "", err
	}
	return string(idsArrayStr), nil
}

// []string切片去重
func RemoveDuplicates(slice []string) []string {
	uniqueMap := make(map[string]bool)
	var uniqueSlice []string
	for _, item := range slice {
		if !uniqueMap[item] {
			uniqueMap[item] = true
			uniqueSlice = append(uniqueSlice, item)
		}
	}
	return uniqueSlice
}
func ContainsString(arr []string, str string) bool {
	for _, item := range arr {
		if item == str {
			return true
		}
	}
	return false
}

func TransferStringToArrayInt(result *string) ([]int64, error) {
	if result == nil {
		return nil, nil
	}
	intArray := make([]int64, 0)
	err := json.Unmarshal([]byte(*result), &intArray)
	if err != nil {
		return nil, err
	}
	return intArray, nil
}

func TransferArrayIntToString(result []int64) (string, error) {
	resultByte, err := json.Marshal(result)
	if err != nil {
		return "", err
	}
	return string(resultByte), nil
}

func TransferStringToArrayString(result *string) ([]string, error) {
	if result == nil {
		return nil, nil
	}
	stringArray := make([]string, 0)
	err := json.Unmarshal([]byte(*result), &stringArray)
	if err != nil {
		return nil, err
	}
	return stringArray, nil
}

func TransferArrayStringToString(result []string) (string, error) {
	resultByte, err := json.Marshal(result)
	if err != nil {
		return "", err
	}
	return string(resultByte), nil
}

func ArrayContains[T comparable](arr []T, target T) bool {
	for _, item := range arr {
		if item == target {
			return true
		}
	}
	return false
}

func TransferToMarshalString(result interface{}) (string, error) {
	resultByte, err := json.Marshal(result)
	if err != nil {
		return "", err
	}
	return string(resultByte), nil
}

func TransferStringToMapStringArray(result *string) (map[string][]string, error) {
	if result == nil {
		return nil, nil
	}
	stringMap := make(map[string][]string)
	err := json.Unmarshal([]byte(*result), &stringMap)
	if err != nil {
		return nil, err
	}
	return stringMap, nil
}
